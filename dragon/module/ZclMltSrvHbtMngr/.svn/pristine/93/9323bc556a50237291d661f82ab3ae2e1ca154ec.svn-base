/********************************************************************
	created:	2011/11/13
	created:	13:11:2011   19:06
	filename: 	\VICENTE\Code\src\XJHeartbeatHandler\XJHeartbeatHandler.cpp
	file path:	\VICENTE\Code\src\XJHeartbeatHandler
	file base:	XJHeartbeatHandler
	file ext:	cpp
	author:		qingch
	
	purpose:	
*********************************************************************/
#include "ZxMultiSrvHeartbeat.h"
#include "HeartbeatHander.h"


CHeartbeatInterface* CreateHeartbeatHandler(bool pIfLogMessage, CLogFile* pFlowLog ,CLogFile* pMessageLog )
{
	return new CHeartbeatHandler(pIf<PERSON>ogMessage,p<PERSON>lowLog,pMessageLog);
}

bool DestroyHeartbeatHandler( CHeartbeatInterface* pHandler )
{
	if (NULL == pHandler)
	{
		return false;
	}
	delete pHandler;
	return true;	
}

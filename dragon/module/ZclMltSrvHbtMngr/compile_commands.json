[{"arguments": ["/usr/sbin/g++", "-g", "-D_DEBUG", "-w", "-fpic", "-DOS_LINUX", "-DOS_LINUX", "-I../../common/head/", "-I../../libapimngr/", "-I./", "-c", "-o", "./debug/Heartbeat_Broadcaster.o", "Heartbeat_Broadcaster.cpp"], "directory": "/root/zexuan/dragon/module/ZclMltSrvHbtMngr", "file": "/root/zexuan/dragon/module/ZclMltSrvHbtMngr/Heartbeat_Broadcaster.cpp", "output": "/root/zexuan/dragon/module/ZclMltSrvHbtMngr/debug/Heartbeat_Broadcaster.o"}]
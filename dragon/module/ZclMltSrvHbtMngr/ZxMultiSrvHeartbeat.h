/********************************************************************
	created:	2011/11/13
	created:	13:11:2011   19:06
	filename: 	\VICENTE\Code\src\XJHeartbeatHandler\XJHeartbeatHandler.h
	file path:	\VICENTE\Code\src\XJHeartbeatHandler
	file base:	XJHeartbeatHandler
	file ext:	h
	author:		qingch
	
	purpose:	
*********************************************************************/
#pragma   warning   (disable   :   4786) 
#pragma   warning   (disable   :   4275) 

#ifndef XJHeartbeatHandler_h__
#define XJHeartbeatHandler_h__

#include "../../libapimngr/ZxLibApi_Heartbeat.h"
#include "../../common/head/ZxLogFile.h"


#ifdef __cplusplus
extern "C" {
#endif
#ifdef OS_WINDOWS
	extern "C"  __declspec( dllexport ) CHeartbeatInterface* CreateHeartbeatHandler(bool pIfLogMessage, CLogFile* pFlowLog ,CLogFile* pMessageLog);
	
	extern "C"  __declspec( dllexport ) bool DestroyHeartbeatHandler(CHeartbeatInterface* pHandler);
#endif
	
#ifdef OS_LINUX
	CHeartbeatInterface* CreateHeartbeatHandler(bool pIfLogMessage, CLogFile* pFlowLog ,CLogFile* pMessageLog);

	bool DestroyHeartbeatHandler(CHeartbeatInterface* pHandler);
#endif
	
#ifdef __cplusplus
}
#endif


#endif // XJHeartbeatHandler_h__


/********************************************************************
	created:	2011/12/05
	created:	5:12:2011   19:09
	filename: 	\vicente\code\src\XJSrvOnlineManager\ServerStatus.cpp
	file path:	\vicente\code\src\XJSrvOnlineManager
	file base:	ServerStatus
	file ext:	cpp
	author:		qingch
	
	purpose:	
*********************************************************************/
#include "ServerStatus.h" 

CServerStatus::CServerStatus()
{
	
}

CServerStatus::~CServerStatus()
{
	
}

std::string CServerStatus::ServerID() const
{
	return m_strServerID;
}

void CServerStatus::ServerID( std::string val )
{
	m_strServerID = val;
}

std::string CServerStatus::NetAddrA() const
{
	return m_strNetAddrA;
}

void CServerStatus::NetAddrA( std::string val )
{
	m_strNetAddrA = val;
}

std::string CServerStatus::NetBcastA() const
{
	return m_strNetBcastA;
}

void CServerStatus::NetBcastA( std::string val )
{
	m_strNetBcastA = val;
}

std::string CServerStatus::NetAddrB() const
{
	return m_strNetAddrB;
}

void CServerStatus::NetAddrB( std::string val )
{
	m_strNetAddrB = val;
}

std::string CServerStatus::NetBcastB() const
{
	return m_strNetBcastB;
}

void CServerStatus::NetBcastB( std::string val )
{
	m_strNetBcastB = val;
}

std::string CServerStatus::HeartbeatAddr() const
{
	return m_strHeartbeatAddr;
}

void CServerStatus::HeartbeatAddr( std::string val )
{
	m_strHeartbeatAddr = val;
}

std::string CServerStatus::HeartbeatBcast() const
{
	return m_strHeartbeatBcast;
}

void CServerStatus::HeartbeatBcast( std::string val )
{
	m_strHeartbeatBcast = val;
}

int CServerStatus::SrvGroup() const
{
	return m_iSrvGroup;
}

void CServerStatus::SrvGroup( int val )
{
	m_iSrvGroup = val;
}

int CServerStatus::RunModel() const
{
	return m_iRunModel;
}

void CServerStatus::RunModel( int val )
{
	m_iRunModel = val;
}

int CServerStatus::IfUseBakNet() const
{
	return m_iIfUseBakNet;
}

void CServerStatus::IfUseBakNet( int val )
{
	m_iIfUseBakNet = val;
}

int CServerStatus::HeartbeatFlag() const
{
	return m_iHeartbeatFlag;
}

void CServerStatus::HeartbeatFlag( int val )
{
	m_iHeartbeatFlag = val;
}

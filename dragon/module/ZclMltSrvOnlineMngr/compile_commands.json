[{"arguments": ["/usr/sbin/g++", "-g", "-D_DEBUG", "-w", "-fpic", "-DOS_LINUX", "-DOS_LINUX", "-I../../common/head/", "-I../../libapimngr/", "-I./", "-c", "-o", "./debug/AASrvSwitchHandler.o", "AASrvSwitchHandler.cpp"], "directory": "/root/zexuan/dragon/module/ZclMltSrvOnlineMngr", "file": "/root/zexuan/dragon/module/ZclMltSrvOnlineMngr/AASrvSwitchHandler.cpp", "output": "/root/zexuan/dragon/module/ZclMltSrvOnlineMngr/debug/AASrvSwitchHandler.o"}]
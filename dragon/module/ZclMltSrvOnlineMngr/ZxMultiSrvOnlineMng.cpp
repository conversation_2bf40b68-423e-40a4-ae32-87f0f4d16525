/********************************************************************
	created:	2011/11/11
	created:	11:11:2011   15:11
	filename: 	\VICENTE\CODE\SRC\XJSrvOnlineManager\XJSrvOnlineManager.cpp
	file path:	\VICENTE\CODE\SRC\XJSrvOnlineManager
	file base:	XJSrvOnlineManager
	file ext:	cpp
	author:		qingch
	
	purpose:	
*********************************************************************/

#include "ZxMultiSrvOnlineMng.h"
#include "SrvOnlineManager.h"

CSrvOnlineManager g_iSrvOnlineManager;

int StartSrvOnLineManager( const stXJSrvOnlineManager& pManager )
{
	return g_iSrvOnlineManager.StartSrvOnLineManager(pManager);
}

int StopSrvOnLineManager()
{
	return g_iSrvOnlineManager.StopSrvOnLineManager();	
}

int RegisterSrvSwitchCallback( PFUNONSRVSWITCH pOnSrvSwitch, void* pParam )
{
	return g_iSrvOnlineManager.RegisterSrvSwitchCallback(pOnSrvSwitch,pParam);	
}

int SetStationLoadStatus( stXJSubstationLoadStatus& PStatus )
{
	return g_iSrvOnlineManager.SetStationLoadStatus(PStatus);
}

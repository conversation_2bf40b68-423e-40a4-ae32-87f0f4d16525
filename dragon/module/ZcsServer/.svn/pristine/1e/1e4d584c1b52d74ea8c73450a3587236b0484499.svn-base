﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{33db7b09-a3ee-4624-ba34-652cef3a92a1}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{727460dc-d31f-4c39-b3fa-1310996fdb18}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{bf617381-cd9c-42f2-9118-f30b0cb18f42}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{d834c3bf-8f5a-4e05-9df0-5ea013689a96}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\common\Zx104Len2CpuMsgAttach.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\Zx104MsgAttach.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxAPCIWrapper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxASDUHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxComFunction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxCommonDBFunction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxHuBei103ASDUHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxHuBei103MsgAttachFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\common\ZxMsgCaster.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProHuBei103Client_update_history.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxProMainFlowWrapper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProRun.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_MsgMonitor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\thirdparty\tinyxml\tinyxml.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\thirdparty\tinyxml\tinystr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\thirdparty\tinyxml\tinyxmlerror.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\thirdparty\tinyxml\tinyxmlparser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\GWModelFileCreate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_GetDataType.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxCommuServer_protocol_common_update_history.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\InitDataToXml.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZclLibmngr_Gbk2Utf8.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxHuBei103ASDUNewHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\common\Zx104Len2CpuMsgAttach.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\Zx104MsgAttach.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxAPCIWrapper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxASDUHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxCommonDBFunction.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxHuBei103ASDUHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxHuBei103MsgAttachFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxProMainFlowWrapper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxProRun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\GWModelFileCreate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\InitDataToXml.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxHuBei103ASDUNewHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZxProHuBei103Client.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
    <None Include="ClassDiagram1.cd" />
    <None Include="..\..\server\ZcsServer.ini">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
</Project>
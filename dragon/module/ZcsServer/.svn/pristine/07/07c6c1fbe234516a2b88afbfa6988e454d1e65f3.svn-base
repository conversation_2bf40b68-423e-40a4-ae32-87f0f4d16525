﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7195B006-1183-4DB6-96F1-790D9A35116C}</ProjectGuid>
    <RootNamespace>ZcsGspCliPro</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>.\Debug\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <IntDir>.\Debug\</IntDir>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;..\..\..\..\common\head;..\..\..\..\thirdparty\tinyxml;..\..\..\..\libapimngr;..\..\common;..\common</IncludePath>
    <LibraryPath>$(VCInstallDir)lib;$(VCInstallDir)atlmfc\lib;$(WindowsSdkDir)lib;$(FrameworkSDKDir)\lib;..\..\..\..\lib\$(Configuration)</LibraryPath>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>.\Release\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <IntDir>.\Release\</IntDir>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;..\..\..\..\common\head;..\..\..\..\thirdparty\tinyxml;..\..\..\..\libapimngr;..\..\common;..\common</IncludePath>
    <LibraryPath>$(VCInstallDir)lib;$(VCInstallDir)atlmfc\lib;$(WindowsSdkDir)lib;$(FrameworkSDKDir)\lib;..\..\..\..\lib\$(Configuration)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;OS_WINDOWS</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OutputFile>..\..\..\..\bin\$(Configuration)\ZcsServer\ZcsGspCliPro.dll</OutputFile>
      <AdditionalDependencies>zxcommon.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <SubSystem>Console</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;OS_WINDOWS</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>zxcommon.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>..\..\..\..\bin\$(Configuration)\ZcsServer\ZcsGspCliPro.dll</OutputFile>
      <SubSystem>Console</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.cpp" />
    <ClCompile Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.cpp" />
    <ClCompile Include="..\..\..\..\thirdparty\tinyxml\tinystr.cpp" />
    <ClCompile Include="..\..\..\..\thirdparty\tinyxml\tinyxml.cpp" />
    <ClCompile Include="..\..\..\..\thirdparty\tinyxml\tinyxmlerror.cpp" />
    <ClCompile Include="..\..\..\..\thirdparty\tinyxml\tinyxmlparser.cpp" />
    <ClCompile Include="..\..\common\ZxPublisher.cpp" />
    <ClCompile Include="..\common\GWModelFileCreate.cpp" />
    <ClCompile Include="..\common\ZxComFunction.cpp" />
    <ClCompile Include="..\common\ZxCommonDBFunction.cpp" />
    <ClCompile Include="..\common\ZxLoadGspTransObjLib.cpp" />
    <ClCompile Include="..\common\ZxSubStationPublisher.cpp" />
    <ClCompile Include="SttpCvtGspInfo.cpp" />
    <ClCompile Include="ZcsGspCliPro.cpp" />
    <ClCompile Include="ZxGspExpBase.cpp" />
    <ClCompile Include="ZxProRun.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\common\head\ZxGlobal_Def.h" />
    <ClInclude Include="..\..\..\..\common\head\ZxLib.h" />
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibApi_Def_CimEExp.h" />
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.h" />
    <ClInclude Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.h" />
    <ClInclude Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.h" />
    <ClInclude Include="..\..\common\ZxPublisher.h" />
    <ClInclude Include="..\common\IZxGspTransObj.h" />
    <ClInclude Include="..\common\ZxLoadGspTransObjLib.h" />
    <ClInclude Include="..\common\ZxSubStationPublisher.h" />
    <ClInclude Include="ProDataStruct.h" />
    <ClInclude Include="SttpCvtGspInfo.h" />
    <ClInclude Include="ZcsGspCliPro.h" />
    <ClInclude Include="ZxGspExpBase.h" />
    <ClInclude Include="ZxProRun.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
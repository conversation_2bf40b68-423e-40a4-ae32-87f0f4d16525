/********************************************************************
dragon 2015-04-10 Beijin
*********************************************************************/
#include "ZxSrvASDU.h"
#include "../../../../common/head/ZxSttpMsgParser.h"

typedef vector<string>	EVENTSET;

class CXJSrvCASDU17 : public CXJSrvASDU
{
public:
	CXJSrvCASDU17(CXJSrvASDUFactory& pASDUFactory, ASDUMESSAGE& pAsduCMD, CXJCommonDBFunction& pComDBFun, CLogFile& pLogFile);
	virtual ~CXJSrvCASDU17();
	virtual int Handle(ASDUQUEUE& pASDUResults, STTP103CMDSET& pSttp103CMDs);
protected:
	virtual void WriteLog(const char * pLog,int nLevel);
	virtual void HookHandleASDU17(int& pSource, int& pType, char* pStartTime, char* pEndTime);
	virtual int  HookMake20105(string pPtID, int pType, const char* pBeginTime, const char* pEndTime, STTP103CMDSET& pSttp103CMDs);
	virtual void HookHandleEventHistory(int pType, string pPtID, const char* pBeginTime, const char* pEndTime,ASDUQUEUE& pASDUResults);
	virtual void HookHandleAlarmHistory(int pType, string pPtID, const char* pBeginTime, const char* pEndTime,ASDUQUEUE& pASDUResults);
	virtual void HookHandleDIChangeHistory(int pType, string pPtID, const char* pBeginTime, const char* pEndTime,ASDUQUEUE& pASDUResults);

	virtual void HookGetEventsFromDB(const char* pPtID, const char* pBeginTime, const char* pEndTime, vector<HISTORYINFO_STRUCT>& eventList);
	virtual void HookGetAlarmsFromDB(const char* pPtID, const char* pBeginTime, const char* pEndTime, vector<HISTORYINFO_STRUCT>& alarmList);
	virtual void HookGetDIChangesFromDB(const char* pPtID, const char* pBeginTime, const char* pEndTime, map<int, vector<HISTORYINFO_STRUCT> >& diList);
	void ParserFulldata(string& strSource,vector<STTP_DATA>& pCharDatas,vector<STTP_DATA>& pEventDatas);
private:
	int TemplateHandleLocalHistryInfo(int pType, const char* pBeginTime, const char* pEndTime, ASDUQUEUE& pASDUResults);	
	int TemplateHandleRemoteHistryInfo(int pType, const char* pBeginTime, const char* pEndTime,ASDUQUEUE& pASDUResults, STTP103CMDSET& pSttp103CMDs);

protected:
	int		m_iFun;
	int		m_iInf;
	int		m_iRII;
	ASDUMESSAGE		m_Command103;
	EVENTSET	m_setEvent;
};
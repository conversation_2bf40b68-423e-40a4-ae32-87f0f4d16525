/********************************************************************
Dragon 2015-04-10 Beijing.	
*********************************************************************/

#ifndef XJProResultOperation_h__
#define XJProResultOperation_h__

#include "ZxProOperation.h"

class CXJProResultOperation : public  CXJProOperation
{
public:
    CXJProResultOperation(CXJPro103ClientWay& pPro103Way, STTPMSG_CACHE_TYPE* pSttpCacheNode);
    virtual ~CXJProResultOperation();
    virtual int Handle();
protected:
	void			SttpResultHandle();
private:
    STTPMSG_CACHE_TYPE* m_pSttpCacheNode;
};

#endif // XJProResultOperation_h__
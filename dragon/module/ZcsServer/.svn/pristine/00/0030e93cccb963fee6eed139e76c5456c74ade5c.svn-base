#include "ThreadManger.h"

CThreadManger::CThreadManger(CLogFile* pLog)
{
	m_pLog = pLog;
	m_threadlist.clear();
}


CThreadManger::~CThreadManger(void)
{
	m_threadlist.clear();
}


void CThreadManger::AddThread(GA_THREAD_INF* pThread)
{
	printf("--------------------->1\n");
	if (pThread == NULL)
	{printf("--------------------->2\n");
		return;
	}
	else
	{printf("--------------------->3\n");
		m_threadlist.push_back(pThread);
		printf("--------------------->4\n");
	}
}

void CThreadManger::RemoveThread(GA_THREAD_INF* pThread)
{
	if (pThread == NULL)
	{
		return;
	}

	vector<GA_THREAD_INF*>::iterator Iter;
	Iter = m_threadlist.begin();
	while (Iter != m_threadlist.end())
	{
		if (*Iter == pThread)
		{
			Iter = m_threadlist.erase(Iter);
		}
		else
		{
			++Iter;
		}
	}
}

BOOL CThreadManger::PauseThread(GA_THREAD_INF* pThread)
{
	return FALSE;
}


BOOL CThreadManger::PauseAllThread()
{
	return FALSE;
}


BOOL CThreadManger::SuppendThread(GA_THREAD_INF* pThread)
{
	return FALSE;
}


BOOL CThreadManger::SuppendAllThread()
{
	return FALSE;
}
/********************************************************************
dragon 2015-04-10 Beijin
*********************************************************************/

#ifndef XJ104FoShanASDUAutoUp_h__
#define XJ104FoShanASDUAutoUp_h__

#include "ZxSrvASDU.h"
#include "../../../../common/head/ZxSttpMsgParser.h"
#include "../../../../common/head/ZxTime.h"


class CXJ104FoShanASDUAutoUp:public CXJSrvASDU
{
public:
	CXJ104FoShanASDUAutoUp( CXJSrvASDUFactory & pASDUFactory , STTPDATA_103CMDS_STRUCT & pSttpData , CXJCommonDBFunction & pComDBFun , CLogFile & pLogFile );
	virtual ~CXJ104FoShanASDUAutoUp();
	
	/**
	* @brief     	
	* @param[in]    & pASDUResults
	* @param[in]    & pSttp103CMDs
	* @return       virtual
	*/
	virtual int Handle( ASDUQUEUE & pASDUResults , STTP103CMDSET & pSttp103CMDs );
protected:
	/**
	* @brief     	
	* @param[in]    char * pLog
	* @param[in]    nLevel
	* @return       virtual
	*/
	virtual void WriteLog( const char * pLog , int nLevel );
	
	/**
	* @brief     	
	* @param[in]    & pASDUResults
	* @return       int
	*/
	int TemplateHandleDIChangeUp( ASDUQUEUE & pASDUResults );
	
	/**
	* @brief     	
	* @param[in]    & ASDU1Set
	* @return       virtual
	*/
	virtual void HookHandle20010( ASDU1_SET & ASDU1Set );
	
	/**
	* @brief     	
	* @param[in]    pStatus
	* @return       virtual
	*/
	virtual int HookConvertDIStatusToDPI( int pStatus );
protected:
	/** @brief     */
	STTPDATA_103CMDS_STRUCT	m_SttpData;
};
#endif
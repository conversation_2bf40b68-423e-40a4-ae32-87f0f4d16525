/****************************************************************
*  Zx104FoShanASDU100.h       author: xiaoli      date: 02/09/2022
*----------------------------------------------------------------
*  note: 
*  
*****************************************************************/
#include "ZxSrvASDU.h"
#include "../../../../common/head/ZxSttpMsgParser.h"


class CXJ104FoShanASDU100:public CXJSrvASDU
{
public:
	CXJ104FoShanASDU100( CXJSrvASDUFactory & pASDUFactory , ASDUMESSAGE & pAsduCMD , CXJCommonDBFunction & pComDBFun , CLogFile & pLogFile );
	virtual ~CXJ104FoShanASDU100();
	
	/**
	* @brief     	
	* @param[in]    & pASDUResults
	* @param[in]    & pSttp103CMDs
	* @return       virtual
	*/
	virtual int Handle( ASDUQUEUE & pASDUResults , STTP103CMDSET & pSttp103CMDs );
protected:
	/**
	* @brief     	
	* @param[in]    char * pLog
	* @param[in]    nLevel
	* @return       virtual
	*/
	virtual void WriteLog( const char * pLog , int nLevel );
	
	/**
	* @brief     	
	* @param[in]    pPtID
	* @param[in]    & pSttp103CMDs
	* @return       virtual
	*/
	virtual int HookMake20011( string pPtID , STTP103CMDSET & pSttp103CMDs );
	
	/**
	* @brief     	
	* @param[in]    & pASDUResults
	* @param[in]    & pSttp103CMDs
	* @return       virtual
	*/
	virtual int TemplateHandleReadDI( ASDUQUEUE & pASDUResults , STTP103CMDSET & pSttp103CMDs );
protected:
	/** @brief     */
	ASDUMESSAGE		m_Command103;
};
﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{1a71cdc8-608d-4b67-89ad-c6a943efd188}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{e4bee897-81f8-4eb1-a4e4-8c38a88eac04}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{2665189a-891b-490e-8cfc-181833553991}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{cf537a56-8dde-43d9-bc8d-f141d20e49da}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\common\Zx104Len2CpuMsgAttach.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\Zx104MsgAttach.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxAPCIWrapper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxComFunction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxCommonDBFunction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxGB103M2MASDUFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxGB103M2MCASDU13.CPP">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxGB103M2MCASDU15.CPP">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxGB103M2MCASDU17.CPP">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxGB103M2MCASDU21.CPP">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxGB103M2MCASDU7.CPP">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxGB103M2MMASDU14.CPP">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxGB103M2MMASDU18.CPP">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxGB103M2MMASDUAutoUp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxGB103M2MMsgAttach.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxGB103M2MMsgAttachFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\common\ZxMsgCaster.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProRun.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxProSrv103FlowWrapper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvASDU.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvASDUFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvCASDU101.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvCASDU103.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvCASDU13.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvCASDU15.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvCASDU17.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvCASDU21.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvCASDU7.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvMASDU10.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvMASDU14.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvMASDU16.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvMASDU18.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvMASDU42.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSrvMASDUAutoUp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_MsgMonitor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProGB103M2M_update_history.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\common\Zx103MsgAttachFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxGB103M2MASDUFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxGB103M2MCASDU13.H">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxGB103M2MCASDU15.H">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxGB103M2MCASDU17.H">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxGB103M2MCASDU21.H">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxGB103M2MCASDU7.H">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxGB103M2MMASDU10.H">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxGB103M2MMASDU16.H">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxGB103M2MMASDUAutoUp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxGB103M2MMsgAttachFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxProRun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxProSrv103FlowWrapper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrv103MASDU16.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvASDU.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvASDUFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvASDUInterface.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvCASDU13.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvCASDU15.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvCASDU17.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvCASDU21.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvCASDU7.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvMASDU10.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvMASDU14.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvMASDU16.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvMASDU42.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSrvMASDUAutoUp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZxProGB103M2M.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
  </ItemGroup>
</Project>
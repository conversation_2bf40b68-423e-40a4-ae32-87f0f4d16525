﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{954ddf6e-8acd-46c6-b549-109803d588cc}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{0896eb3e-8c64-423f-b704-86fa348a9f6f}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{9b3844c9-95be-4cdb-9dd4-269e99b7c960}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{81ac781d-afd3-4efc-a579-0df16a28e5eb}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ZxAutoCallPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxAutoCallSttpMsgHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxFrontPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxFrontSttpMsgHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxMainStationSttpMsgHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\common\ZxObserver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxPACKAGESttpMsgHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProRun.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProSttpServerWay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\common\ZxPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSectionDataSttpMsgHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxSttpServerMsgHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxSubStationTesterSttpMsgHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxWaveFileArchiveHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SttpMsgAnalyze.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxSttpMsgMaker.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_SectionData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\common\ZxRetransmitPublisher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\libapimngr\ZxLibMngr_DBAcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxProSttpServer_update_history.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ZxCmdTransSttpMsgHandler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="ZxAutoCallPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxAutoCallSttpMsgHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxFrontPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxFrontSttpMsgHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxMainStationSttpMsgHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\common\ZxObserver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxPACKAGESttpMsgHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxProRun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxProSttpServerWay.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSectionDataSttpMsgHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxSttpServerMsgHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxSubStationTesterSttpMsgHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxWaveFileArchiveHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\ZxRetransmitPublisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ZxCmdTransSttpMsgHandler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ZxProSttpServer.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
    <None Include="..\..\server\ZcsServer.ini">
      <Filter>MakeFile</Filter>
    </None>
  </ItemGroup>
</Project>
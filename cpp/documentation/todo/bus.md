# TCP消息总线系统开发计划书

## 1. 项目概述

### 1.1 项目信息
- **项目名称**: 基于muduo的TCP消息总线系统
- **项目目标**: 开发一个轻量级、高性能的TCP消息总线，支持微服务间通信，基于发布-订阅模式，使用JSON协议进行消息传输
- **技术栈**: C++17, muduo网络库, nlohmann/json, spdlog日志库
- **目标平台**: Linux环境
- **部署方式**: 独立程序运行

### 1.2 核心特性
- ✅ 基于MessageType和EventType的发布-订阅消息路由
- ✅ 支持CommonMessage和EventMessage两种消息类型
- ✅ 使用JSON格式进行消息序列化和传输
- ✅ JSON配置文件管理
- ✅ 基于spdlog的日志系统
- ✅ 高并发连接支持

## 2. JSON协议设计

### 2.1 配置文件格式 (config.json)

```json
{
    "bus": {
        "server": {
            "host": "0.0.0.0",
            "port": 8080,
            "thread_pool_size": 4
        }
    }
}
```

### 2.2 订阅/取消订阅消息格式

```json
{
  "action": "subscribe",  // "subscribe" | "unsubscribe"
  "message_types": [1, 2, 3],  // MessageType数组
  "event_types": [100, 200, 300]  // EventType数组 (可选)
}
```

### 2.3 CommonMessage JSON格式

```json
{
  "msg_category": "common",
  "type": 1,
  "source_id": 12345,
  "target_id": 67890,
  "invoke_id": "req_001",
  "data": "base64编码的二进制数据",
  "is_last_msg": true
}
```

### 2.4 EventMessage JSON格式

```json
{
  "msg_category": "event",
  "event_type": 100,
  "device_uuid": {
    "device_id": 1001,
    "category": 1
  },
  "source_id": 12345,
  "description": "设备状态变化",
  "data": "base64编码的二进制数据"
}
```

### 2.5 系统控制消息格式

```json
{
  "msg_category": "control",
  "action": "subscribe_response",
  "success": true,
  "message": "订阅成功",
  "subscribed_message_types": [1, 2],
  "subscribed_event_types": [100, 200]
}
```

## 3. 系统架构设计

### 3.1 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   TCP Client    │    │   TCP Client    │    │   TCP Client    │
│   (Publisher)   │    │  (Subscriber)   │    │  (Subscriber)   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     TCP Message Bus      │
                    │                          │
                    │  ┌─────────────────────┐ │
                    │  │  Connection Mgr    │ │
                    │  └─────────────────────┘ │
                    │  ┌─────────────────────┐ │
                    │  │  Subscription Mgr  │ │
                    │  └─────────────────────┘ │
                    │  ┌─────────────────────┐ │
                    │  │  Message Router    │ │
                    │  └─────────────────────┘ │
                    │  ┌─────────────────────┐ │
                    │  │  JSON Serializer   │ │
                    │  └─────────────────────┘ │
                    └──────────────────────────┘
```

### 3.2 核心组件说明

| 组件 | 职责 | 实现状态 |
|------|------|----------|
| Connection Mgr | 管理TCP连接生命周期 | ✅ 已实现 |
| Subscription Mgr | 管理客户端订阅信息 | 🔄 开发中 |
| Message Router | 基于订阅规则分发消息 | 🔄 开发中 |
| JSON Serializer | 消息序列化/反序列化 | 🔄 开发中 |

## 4. 详细任务分解

### 4.1 阶段一：基础架构搭建 ✅

#### 任务1.1: 项目结构初始化 ✅
- [x] 创建CMakeLists.txt配置文件
- [x] 设置依赖库（zexuan_base, zexuan_net）
- [x] 建立基本的目录结构

#### 任务1.2: 配置管理模块开发 ✅
- [x] 直接传入路径，成员变量保存配置文件读取的内容
- [x] 支持JSON格式配置文件

#### 任务1.3: 日志系统集成 ✅
- [x] 直接使用spdlog::info之类的接口
- [x] 使用logger_manager进行初始化
- [x] 解决多线程日志析构顺序问题
### 4.2 阶段二：消息协议实现 🔄

#### 任务2.1: JSON序列化模块 🔄
- [ ] 实现CommonMessage与JSON的相互转换
- [ ] 实现EventMessage与JSON的相互转换
- [ ] 实现订阅消息的JSON解析
- [ ] 添加Base64编码/解码支持

#### 任务2.2: 消息协议定义 🔄
- [ ] 定义TCP传输层协议（消息长度+JSON内容）
- [ ] 实现消息打包和解包功能
- [ ] 添加消息完整性校验
### 4.3 阶段三：核心业务逻辑 🔄

#### 任务3.1: 连接管理器 ✅
- [x] 基于muduo实现TCP服务器
- [x] 管理客户端连接的生命周期
- [x] 实现连接状态监控

#### 任务3.2: 订阅管理器 🔄
- [ ] 实现客户端订阅信息存储
- [ ] 支持按MessageType和EventType订阅
- [ ] 实现订阅/取消订阅逻辑

#### 任务3.3: 消息路由器 🔄
- [ ] 实现基于订阅规则的消息分发
- [ ] 支持点对点和广播消息
### 4.4 阶段四：系统集成与测试 📋

#### 任务4.1: 主程序集成 📋
- [ ] 整合各个模块
- [ ] 实现优雅启动和关闭
- [ ] 添加信号处理

#### 任务4.2: 单元测试开发 📋
- [ ] 编写JSON序列化测试
- [ ] 编写订阅管理测试
- [ ] 编写消息路由测试

#### 任务4.3: 集成测试 📋
- [ ] 开发测试客户端程序
- [ ] 测试多客户端并发场景
- [ ] 验证消息路由正确性

### 4.5 阶段五：性能优化与文档 📋

#### 任务5.1: 性能测试与优化 📋
- [ ] 进行压力测试
- [ ] 优化内存使用
- [ ] 优化消息处理性能

#### 任务5.2: 文档编写 📋
- [ ] 编写用户使用手册
- [ ] 编写API文档
- [ ] 编写部署指南

## 5. 项目状态

### 5.1 当前进度
- **总体进度**: 30%
- **已完成**: 基础架构搭建、连接管理
- **进行中**: JSON序列化模块、订阅管理器
- **待开始**: 消息路由器、测试、文档

### 5.2 关键里程碑
- [x] **里程碑1**: 基础架构完成 (已完成)
- [ ] **里程碑2**: 核心功能实现 (进行中)
- [ ] **里程碑3**: 系统集成测试 (待开始)
- [ ] **里程碑4**: 性能优化完成 (待开始)

### 5.3 技术风险与应对
| 风险 | 影响 | 应对措施 | 状态 |
|------|------|----------|------|
| 多线程日志析构顺序问题 | 高 | 修改信号处理逻辑，确保正确的析构顺序 | ✅ 已解决 |
| JSON序列化性能 | 中 | 考虑消息缓存和批量处理 | 🔄 监控中 |
| 高并发连接管理 | 中 | 基于muduo的成熟方案 | ✅ 已解决 |

## 6. 下一步计划

### 6.1 近期目标 (1-2周)
1. 完成JSON序列化模块
2. 实现订阅管理器
3. 开发基础的消息路由功能

### 6.2 中期目标 (3-4周)
1. 完成消息路由器
2. 系统集成测试
3. 基础性能测试

### 6.3 长期目标 (1-2个月)
1. 性能优化
2. 完整的测试覆盖
3. 文档和部署指南
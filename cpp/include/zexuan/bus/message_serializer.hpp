/**
 * @file message_serializer.hpp
 * @brief JSON消息序列化器 - 处理CommonMessage和EventMessage的JSON转换
 * <AUTHOR>
 * @date 2025-08-24
 */

#ifndef ZEXUAN_BUS_MESSAGE_SERIALIZER_HPP
#define ZEXUAN_BUS_MESSAGE_SERIALIZER_HPP

#include <string>
#include <vector>
#include <optional>
#include "nlohmann/json.hpp"
#include "zexuan/base/types/structs.hpp"

namespace zexuan {
namespace bus {

/**
 * @brief 订阅消息结构
 */
struct SubscriptionMessage {
    enum class Action {
        SUBSCRIBE,
        UNSUBSCRIBE
    };
    
    Action action;
    std::vector<int> message_types;
    std::vector<int> event_types;
    
    SubscriptionMessage() = default;
};

/**
 * @brief 系统控制消息结构
 */
struct ControlMessage {
    std::string action;
    bool success{false};
    std::string message;
    std::vector<int> subscribed_message_types;
    std::vector<int> subscribed_event_types;
    
    ControlMessage() = default;
};

/**
 * @brief JSON消息序列化器类
 * 
 * 负责CommonMessage、EventMessage、订阅消息等与JSON的相互转换
 */
class MessageSerializer {
public:
    /**
     * @brief CommonMessage转JSON
     * @param msg CommonMessage对象
     * @return JSON字符串
     */
    static std::string serializeCommonMessage(const zexuan::base::CommonMessage& msg);
    
    /**
     * @brief JSON转CommonMessage
     * @param json_str JSON字符串
     * @return CommonMessage对象，失败返回nullopt
     */
    static std::optional<zexuan::base::CommonMessage> deserializeCommonMessage(const std::string& json_str);
    
    /**
     * @brief EventMessage转JSON
     * @param msg EventMessage对象
     * @return JSON字符串
     */
    static std::string serializeEventMessage(const zexuan::base::EventMessage& msg);
    
    /**
     * @brief JSON转EventMessage
     * @param json_str JSON字符串
     * @return EventMessage对象，失败返回nullopt
     */
    static std::optional<zexuan::base::EventMessage> deserializeEventMessage(const std::string& json_str);
    
    /**
     * @brief 订阅消息转JSON
     * @param msg 订阅消息对象
     * @return JSON字符串
     */
    static std::string serializeSubscriptionMessage(const SubscriptionMessage& msg);
    
    /**
     * @brief JSON转订阅消息
     * @param json_str JSON字符串
     * @return 订阅消息对象，失败返回nullopt
     */
    static std::optional<SubscriptionMessage> deserializeSubscriptionMessage(const std::string& json_str);
    
    /**
     * @brief 控制消息转JSON
     * @param msg 控制消息对象
     * @return JSON字符串
     */
    static std::string serializeControlMessage(const ControlMessage& msg);
    
    /**
     * @brief JSON转控制消息
     * @param json_str JSON字符串
     * @return 控制消息对象，失败返回nullopt
     */
    static std::optional<ControlMessage> deserializeControlMessage(const std::string& json_str);
    
    /**
     * @brief Base64编码
     * @param data 二进制数据
     * @return Base64编码字符串
     */
    static std::string encodeBase64(const std::vector<uint8_t>& data);
    
    /**
     * @brief Base64解码
     * @param encoded Base64编码字符串
     * @return 二进制数据，失败返回空vector
     */
    static std::vector<uint8_t> decodeBase64(const std::string& encoded);

private:
    /**
     * @brief 安全的JSON解析
     * @param json_str JSON字符串
     * @return JSON对象，失败返回nullopt
     */
    static std::optional<nlohmann::json> safeParseJson(const std::string& json_str);
    
    /**
     * @brief 验证JSON是否包含必需字段
     * @param json JSON对象
     * @param required_fields 必需字段列表
     * @return 是否包含所有必需字段
     */
    static bool validateRequiredFields(const nlohmann::json& json, const std::vector<std::string>& required_fields);
};

} // namespace bus
} // namespace zexuan

#endif // ZEXUAN_BUS_MESSAGE_SERIALIZER_HPP

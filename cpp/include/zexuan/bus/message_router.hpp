/**
 * @file message_router.hpp
 * @brief 消息路由器 - 基于订阅规则分发消息
 * <AUTHOR>
 * @date 2025-08-24
 */

#ifndef ZEXUAN_BUS_MESSAGE_ROUTER_HPP
#define ZEXUAN_BUS_MESSAGE_ROUTER_HPP

#include <memory>
#include <atomic>
#include "zexuan/net/tcp_connection.hpp"
#include "zexuan/base/types/structs.hpp"
#include "zexuan/bus/subscription_manager.hpp"
#include "zexuan/bus/message_serializer.hpp"
#include "zexuan/bus/message_protocol.hpp"

namespace zexuan {
namespace bus {

/**
 * @brief 消息路由统计信息
 */
struct RoutingStatistics {
    uint64_t total_messages_routed{0};      ///< 总路由消息数
    uint64_t common_messages_routed{0};     ///< CommonMessage路由数
    uint64_t event_messages_routed{0};      ///< EventMessage路由数
    uint64_t control_messages_sent{0};      ///< 控制消息发送数
    uint64_t routing_errors{0};             ///< 路由错误数
    uint64_t serialization_errors{0};       ///< 序列化错误数
    uint64_t no_subscribers_count{0};       ///< 无订阅者消息数

    /**
     * @brief 重置统计信息
     */
    void reset() {
        total_messages_routed = 0;
        common_messages_routed = 0;
        event_messages_routed = 0;
        control_messages_sent = 0;
        routing_errors = 0;
        serialization_errors = 0;
        no_subscribers_count = 0;
    }
};

/**
 * @brief 消息路由器类
 * 
 * 负责根据订阅规则将消息分发给相应的客户端
 */
class MessageRouter {
public:
    using TcpConnectionPtr = std::shared_ptr<zexuan::net::TcpConnection>;
    
    /**
     * @brief 构造函数
     * @param subscription_manager 订阅管理器
     */
    explicit MessageRouter(std::shared_ptr<SubscriptionManager> subscription_manager);
    
    ~MessageRouter() = default;
    
    // 禁用拷贝构造和赋值
    MessageRouter(const MessageRouter&) = delete;
    MessageRouter& operator=(const MessageRouter&) = delete;
    
    /**
     * @brief 路由CommonMessage
     * @param sender 发送者连接（可以为空，表示系统消息）
     * @param message CommonMessage对象
     * @return 成功路由的客户端数量
     */
    size_t routeCommonMessage(const TcpConnectionPtr& sender, const zexuan::base::CommonMessage& message);
    
    /**
     * @brief 路由EventMessage
     * @param sender 发送者连接（可以为空，表示系统消息）
     * @param message EventMessage对象
     * @return 成功路由的客户端数量
     */
    size_t routeEventMessage(const TcpConnectionPtr& sender, const zexuan::base::EventMessage& message);
    
    /**
     * @brief 发送控制消息给指定客户端
     * @param conn 目标客户端连接
     * @param control_message 控制消息
     * @return 是否发送成功
     */
    bool sendControlMessage(const TcpConnectionPtr& conn, const ControlMessage& control_message);
    
    /**
     * @brief 广播控制消息给所有客户端
     * @param control_message 控制消息
     * @return 成功发送的客户端数量
     */
    size_t broadcastControlMessage(const ControlMessage& control_message);
    
    /**
     * @brief 处理点对点消息（基于target_id）
     * @param sender 发送者连接
     * @param message CommonMessage对象
     * @return 是否找到并发送给目标客户端
     */
    bool routePointToPointMessage(const TcpConnectionPtr& sender, const zexuan::base::CommonMessage& message);
    
    /**
     * @brief 获取路由统计信息
     * @return 统计信息
     */
    RoutingStatistics getStatistics() const;
    
    /**
     * @brief 重置统计信息
     */
    void resetStatistics();
    
    /**
     * @brief 设置是否启用详细日志
     * @param enabled 是否启用
     */
    void setVerboseLogging(bool enabled) { verbose_logging_ = enabled; }

private:
    std::shared_ptr<SubscriptionManager> subscription_manager_;  ///< 订阅管理器

    // 使用原子变量进行统计
    mutable std::atomic<uint64_t> total_messages_routed_{0};
    mutable std::atomic<uint64_t> common_messages_routed_{0};
    mutable std::atomic<uint64_t> event_messages_routed_{0};
    mutable std::atomic<uint64_t> control_messages_sent_{0};
    mutable std::atomic<uint64_t> routing_errors_{0};
    mutable std::atomic<uint64_t> serialization_errors_{0};
    mutable std::atomic<uint64_t> no_subscribers_count_{0};

    bool verbose_logging_{false};                                ///< 是否启用详细日志
    
    /**
     * @brief 发送消息给指定连接
     * @param conn 目标连接
     * @param json_message JSON消息内容
     * @return 是否发送成功
     */
    bool sendMessageToConnection(const TcpConnectionPtr& conn, const std::string& json_message);
    
    /**
     * @brief 发送消息给连接列表
     * @param connections 连接列表
     * @param json_message JSON消息内容
     * @param exclude_sender 是否排除发送者
     * @param sender 发送者连接（用于排除）
     * @return 成功发送的连接数量
     */
    size_t sendMessageToConnections(const std::vector<TcpConnectionPtr>& connections,
                                   const std::string& json_message,
                                   bool exclude_sender = false,
                                   const TcpConnectionPtr& sender = nullptr);
    
    /**
     * @brief 根据target_id查找客户端连接
     * @param target_id 目标ID
     * @return 客户端连接，未找到返回nullptr
     */
    TcpConnectionPtr findConnectionByTargetId(zexuan::base::ObjectId target_id);
    
    /**
     * @brief 记录路由日志
     * @param message_type 消息类型描述
     * @param sender 发送者
     * @param subscriber_count 订阅者数量
     */
    void logRouting(const std::string& message_type, const TcpConnectionPtr& sender, size_t subscriber_count);
};

} // namespace bus
} // namespace zexuan

#endif // ZEXUAN_BUS_MESSAGE_ROUTER_HPP

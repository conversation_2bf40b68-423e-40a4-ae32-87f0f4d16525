/**
 * @file base_types.hpp
 * @brief 现代化 Subject-Observer-Register-Mediator 设计模式基础类型定义
 * <AUTHOR> from NX framework
 * @date 2025-08-16
 */

#ifndef ZEXUAN_BASE_TYPES_HPP
#define ZEXUAN_BASE_TYPES_HPP

// ============================================================================
// 包含所有拆分后的类型定义文件
// ============================================================================

#include "types/basic_types.hpp"
#include "types/enums.hpp"
#include "types/interfaces.hpp"
#include "types/structs.hpp"
#include "types/type_aliases.hpp"

#endif  // ZEXUAN_BASE_TYPES_HPP

/**
 * @file type_aliases.hpp
 * @brief 类型别名、Result类型和回调函数类型定义
 * <AUTHOR> from NX framework
 * @date 2025-08-16
 */

#ifndef ZEXUAN_BASE_TYPE_ALIASES_HPP
#define ZEXUAN_BASE_TYPE_ALIASES_HPP

#include <expected>
#include <functional>
#include <vector>

#include "basic_types.hpp"
#include "enums.hpp"
#include "structs.hpp"

namespace zexuan {
  namespace base {

    // ============================================================================
    // 类型别名
    // ============================================================================
    using EventTypeList = std::vector<EventType>;
    using DeviceList = std::vector<DeviceUUID>;

    // 帧列表类型定义（参考原始 PRO_FRAME_BODY_LIST）
    using ProtocolFrameList = std::vector<ProtocolFrame>;

    // ============================================================================
    // 现代化错误处理类型
    // ============================================================================
    template <typename T> using Result = std::expected<T, ErrorCode>;

    using VoidResult = std::expected<void, ErrorCode>;

    // ============================================================================
    // 现代化回调函数类型定义
    // ============================================================================
    using CommonMessageHandler = std::function<Result<void>(const CommonMessage&)>;
    using EventMessageHandler = std::function<Result<void>(const EventMessage&)>;

  }  // namespace base
}  // namespace zexuan

#endif  // ZEXUAN_BASE_TYPE_ALIASES_HPP

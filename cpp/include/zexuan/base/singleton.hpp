#ifndef ZEXUAN_SINGLETON_H
#define ZEXUAN_SINGLETON_H

#include "noncopyable.hpp"

namespace zexuan {

  template <typename T> class singleton : public base::noncopyable {
  private:
    singleton() = default;
    ~singleton() = default;

  public:
    static T& getInstance() {
      static T instance;
      return instance;
    }

    friend T;
  };

}  // namespace zexuan

#endif  // ZEXUAN_SINGLETON_H
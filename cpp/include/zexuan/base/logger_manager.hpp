/**
 * @file logger_manager.hpp
 * @brief 简化的日志管理器 - 基于配置文件初始化日志
 * <AUTHOR>
 * @date 2025-08-24
 */

#ifndef ZEXUAN_BASE_LOGGER_MANAGER_HPP
#define ZEXUAN_BASE_LOGGER_MANAGER_HPP

#include <string>

namespace zexuan {
namespace base {

/**
 * @brief 简化的日志管理器类
 * 
 * 从配置文件读取日志配置，只需要指定日志文件名即可
 */
class LoggerManager {
public:
    /**
     * @brief 初始化日志系统
     * @param log_filename 日志文件名（不包含路径）
     * @param config_path 配置文件路径
     * @return true 初始化成功，false 初始化失败
     */
    static bool initialize(const std::string& log_filename, 
                          const std::string& config_path);

private:
    
    /**
     * @brief 从配置文件加载日志配置
     */
    static bool load_config_and_init(const std::string& log_filename, const std::string& config_path);
};

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_LOGGER_MANAGER_HPP

#pragma once

#include "zexuan/protocol/transform/asdu_base.hpp"

namespace zexuan {
  namespace protocol {
    namespace transform {
      namespace gw104 {

        /**
         * @brief GW104 ASDU Type1 处理类（遥信变位）
         * 参考原始 TNXEcProAsdu1GWS 实现
         */
        class AsduType1GWS : public AsduBase {
        public:
          AsduType1GWS() = default;
          virtual ~AsduType1GWS() = default;

          // 实现基类纯虚函数

          /**
           * @brief 解析协议帧为CommonMessage
           * @param frame 协议帧
           * @param common_msg 输出的CommonMessage
           * @return 成功返回0，失败返回错误码
           */
          virtual int ParseToCommonMessage(const base::ProtocolFrame& frame,
                                           base::CommonMessage& common_msg) override;

          /**
           * @brief 将CommonMessage转换为协议帧
           * @param common_msg 输入的CommonMessage
           * @param frame 输出的协议帧
           * @return 成功返回0，失败返回错误码
           */
          virtual int ConvertFromCommonMessage(const base::CommonMessage& common_msg,
                                               base::ProtocolFrame& frame) override;

          /**
           * @brief 获取支持的ASDU类型
           * @return ASDU类型标识
           */
          virtual uint8_t GetSupportedType() const override {
            return 1;  // Type 1 = 遥信变位
          }

          /**
           * @brief 获取ASDU类型描述
           * @return 类型描述字符串
           */
          virtual const char* GetTypeDescription() const override {
            return "GW104 ASDU Type1 - Single Point Information with Time Tag";
          }

        private:
          // 简化版本，不需要复杂的私有方法
        };

      }  // namespace gw104
    }  // namespace transform
  }  // namespace protocol
}  // namespace zexuan

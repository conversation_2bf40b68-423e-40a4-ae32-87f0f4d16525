/**
 * @file uuid.h
 * @brief 跨平台 UUID 工具接口
 * <AUTHOR> UUID implementation
 * @date 2025-08-16
 */

#ifndef ZEXUAN_UTILS_UUID_H
#define ZEXUAN_UTILS_UUID_H

#include <array>
#include <cstdint>
#include <optional>
#include <string>

namespace zexuan {
  namespace utils {

    /// @brief UUID 数据结构（128位）
    struct UUID {
      std::array<uint8_t, 16> data{};

      /// @brief 默认构造函数（创建空 UUID）
      UUID() = default;

      /// @brief 从字节数组构造
      explicit UUID(const std::array<uint8_t, 16>& bytes) : data(bytes) {}

      /// @brief 比较运算符
      bool operator==(const UUID& other) const noexcept { return data == other.data; }

      bool operator!=(const UUID& other) const noexcept { return !(*this == other); }

      bool operator<(const UUID& other) const noexcept { return data < other.data; }

      /// @brief 检查是否为空 UUID
      bool IsNull() const noexcept { return *this == UUID{}; }

      /// @brief 清空 UUID
      void Clear() noexcept { data.fill(0); }
    };

    /// @brief UUID 生成器接口
    class UUIDGenerator {
    public:
      /// @brief 虚析构函数
      virtual ~UUIDGenerator() = default;

      /// @brief 生成随机 UUID (Version 4)
      /// @return 生成的 UUID
      virtual UUID GenerateRandom() = 0;

      /// @brief 生成基于时间的 UUID (Version 1)
      /// @return 生成的 UUID
      virtual UUID GenerateTimeBased() = 0;

      /// @brief 检查生成器是否可用
      /// @return true-可用 false-不可用
      virtual bool IsAvailable() const noexcept = 0;
    };

    /// @brief UUID 工具函数
    namespace uuid {

      /// @brief 获取默认的 UUID 生成器
      /// @return UUID 生成器实例
      UUIDGenerator& GetGenerator();

      /// @brief 生成随机 UUID
      /// @return 生成的 UUID
      UUID GenerateRandom();

      /// @brief 生成基于时间的 UUID
      /// @return 生成的 UUID
      UUID GenerateTimeBased();

      /// @brief 将 UUID 转换为标准字符串格式
      /// @param uuid UUID 对象
      /// @return 格式化的字符串 (xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx)
      std::string ToString(const UUID& uuid);

      /// @brief 将 UUID 转换为紧凑字符串格式（无连字符）
      /// @param uuid UUID 对象
      /// @return 紧凑格式的字符串 (xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx)
      std::string ToCompactString(const UUID& uuid);

      /// @brief 从字符串解析 UUID
      /// @param str UUID 字符串（支持标准格式和紧凑格式）
      /// @return 解析成功返回 UUID，失败返回 nullopt
      std::optional<UUID> FromString(const std::string& str);

      /// @brief 生成空 UUID
      /// @return 空 UUID (全零)
      UUID Null();

      /// @brief 检查字符串是否为有效的 UUID 格式
      /// @param str 待检查的字符串
      /// @return true-有效 false-无效
      bool IsValidFormat(const std::string& str);

    }  // namespace uuid
  }  // namespace utils
}  // namespace zexuan

#endif  // ZEXUAN_UTILS_UUID_H

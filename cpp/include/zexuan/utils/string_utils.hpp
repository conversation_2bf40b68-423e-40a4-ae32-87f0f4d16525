#pragma once

#include <algorithm>
#include <cctype>
#include <string>
#include <string_view>
#include <vector>

namespace zexuan::utils {

  class StringUtils {
  public:
    // 分割字符串
    static std::vector<std::string> split(std::string_view str, char delim = ' ');
    static std::vector<std::string> split(std::string_view str, std::string_view delims);

    // 修剪字符串
    static std::string trim(std::string_view str);
    static std::string trimLeft(std::string_view str);
    static std::string trimRight(std::string_view str);

    // 大小写转换
    static std::string toUpper(std::string_view str);
    static std::string toLower(std::string_view str);

    // 替换字符串
    static std::string replace(std::string_view str, std::string_view from, std::string_view to);

    // 检查前缀后缀
    static bool startsWith(std::string_view str, std::string_view prefix);
    static bool endsWith(std::string_view str, std::string_view suffix);

    // 字符串连接
    static std::string join(const std::vector<std::string>& parts, std::string_view delim);

  private:
    // 工具类，禁止实例化
    StringUtils() = delete;
    ~StringUtils() = delete;
    StringUtils(const StringUtils&) = delete;
    StringUtils& operator=(const StringUtils&) = delete;
  };

}  // namespace zexuan::utils

#ifndef ZEXUAN_NET_EVENT_LOOP_HPP
#define ZEXUAN_NET_EVENT_LOOP_HPP

#include <any>
#include <atomic>
#include <chrono>
#include <functional>
#include <mutex>
#include <thread>
#include <vector>

#include "zexuan/net/callbacks.hpp"
#include "zexuan/net/poller.hpp"

namespace zexuan {
  namespace net {

    class Channel;

    class EventLoop {
    public:
      EventLoop();
      ~EventLoop();  // force out-line dtor, for std::unique_ptr members.

      void loop();
      void quit();

      Timestamp pollReturnTime() const { return pollReturnTime_; }

      int64_t iteration() const { return iteration_; }

      void runInLoop(Functor cb);
      void queueInLoop(Functor cb);

      size_t queueSize() const;

      void wakeup();
      void updateChannel(Channel* channel);
      void removeChannel(Channel* channel);
      bool hasChannel(Channel* channel);

      void assertInLoopThread() {
        if (!isInLoopThread()) {
          abortNotInLoopThread();
        }
      }
      bool isInLoopThread() const { return threadId_ == std::this_thread::get_id(); }

      bool eventHandling() const { return eventHandling_; }

      void setContext(const std::any& context) { context_ = context; }

      const std::any& getContext() const { return context_; }

      std::any* getMutableContext() { return &context_; }

      static EventLoop* getEventLoopOfCurrentThread();

    private:
      void abortNotInLoopThread();
      void handleRead(Timestamp);  // waked up
      void doPendingFunctors();

      void printActiveChannels() const;  // DEBUG

      typedef std::vector<Channel*> ChannelList;

      bool looping_; /* atomic */
      std::atomic<bool> quit_;
      bool eventHandling_;          /* atomic */
      bool callingPendingFunctors_; /* atomic */
      int64_t iteration_;
      const std::thread::id threadId_;
      Timestamp pollReturnTime_;
      std::unique_ptr<Poller> poller_;
      int wakeupFd_;
      std::unique_ptr<Channel> wakeupChannel_;
      std::any context_;

      ChannelList activeChannels_;
      Channel* currentActiveChannel_;

      mutable std::mutex mutex_;
      std::vector<Functor> pendingFunctors_;
    };

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_EVENT_LOOP_HPP

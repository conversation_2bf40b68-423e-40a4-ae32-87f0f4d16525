#ifndef ZEXUAN_NET_ADDRESS_HPP
#define ZEXUAN_NET_ADDRESS_HPP

#include <cstdint>
#include <string>

#include "zexuan/net/sockets_ops.hpp"

// 平台特定的头文件包含
#ifndef __linux__
#  include <winsock2.h>
#  include <ws2tcpip.h>
#else
#  include <netinet/in.h>
#  include <sys/socket.h>
#  include <sys/un.h>  // Unix域套接字
#endif

namespace zexuan {
  namespace net {

    // 前向声明
    namespace sockets {
      const struct sockaddr* sockaddr_cast(const struct sockaddr_in6* addr);
    }

    class Address {
    public:
      // 构造函数 - 与原始 InetAddress 完全一致
      explicit Address(uint16_t port = 0, bool loopbackOnly = false, bool ipv6 = false);
      Address(const std::string& ip, uint16_t port, bool ipv6 = false);
      explicit Address(const struct sockaddr_in& addr);
      explicit Address(const struct sockaddr_in6& addr);

      // Unix域套接字构造函数
      explicit Address(const std::string& path);  // Unix域套接字路径
      explicit Address(const struct sockaddr_un& addr);

      // 基本方法 - 与原始 InetAddress 完全一致
      sa_family_t family() const;
      std::string toIp() const;
      std::string toIpPort() const;
      uint16_t port() const;

      // Unix域套接字方法
      std::string toUnixPath() const;  // 获取Unix域套接字路径
      bool isUnixSocket() const { return family() == AF_UNIX; }

      // 系统调用接口 - 与原始 InetAddress 完全一致
      const struct sockaddr* getSockAddr() const;
      socklen_t getSockLen() const;  // 获取地址长度
      void setSockAddrInet6(const struct sockaddr_in6& addr6) { addr6_ = addr6; }
      void setSockAddrUnix(const struct sockaddr_un& addrun) { addrun_ = addrun; }

      // 网络字节序 - 与原始 InetAddress 完全一致
      uint32_t ipv4NetEndian() const;
      uint16_t portNetEndian() const;

      // 静态方法 - 与原始 InetAddress 完全一致
      static bool resolve(const std::string& hostname, Address* result);

      // IPv6 支持 - 与原始 InetAddress 完全一致
      void setScopeId(uint32_t scope_id);

      // 兼容性方法 - 保持现有接口
      struct sockaddr_in toSockAddr() const;
      struct sockaddr_in6 toSockAddr6() const;
      struct sockaddr_un toSockAddrUnix() const;
      static Address fromIpPort(const std::string& ip, uint16_t port);
      static Address fromUnixPath(const std::string& path);
      static Address fromSockAddr(const struct sockaddr& addr);

    private:
      // 使用 union 同时支持 IPv4、IPv6 和 Unix域套接字
      union {
        struct sockaddr_in addr_;
        struct sockaddr_in6 addr6_;
        struct sockaddr_un addrun_;
      };
    };

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_ADDRESS_HPP

#ifndef ZEXUAN_NET_UDS_ACCEPTOR_HPP
#define ZEXUAN_NET_UDS_ACCEPTOR_HPP

#include <functional>
#include <string>

#include "zexuan/net/channel.hpp"
#include "zexuan/net/socket.hpp"

namespace zexuan {
  namespace net {

    class EventLoop;

    ///
    /// Acceptor of incoming UDS connections.
    ///
    class UdsAcceptor {
    public:
      typedef std::function<void(int sockfd, const std::string& peerPath)> NewConnectionCallback;

      UdsAcceptor(EventLoop* loop, const std::string& listenPath);
      ~UdsAcceptor();

      // 禁用拷贝构造和赋值
      UdsAcceptor(const UdsAcceptor&) = delete;
      UdsAcceptor& operator=(const UdsAcceptor&) = delete;

      void setNewConnectionCallback(const NewConnectionCallback& cb) {
        newConnectionCallback_ = cb;
      }

      void listen();

      bool listening() const { return listening_; }

      const std::string& listenPath() const { return listenPath_; }

    private:
      void handleRead(std::chrono::system_clock::time_point);

      EventLoop* loop_;
      std::string listenPath_;
      zexuan::net::Socket acceptSocket_;
      Channel acceptChannel_;
      NewConnectionCallback newConnectionCallback_;
      bool listening_;
      int idleFd_;
    };

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_UDS_ACCEPTOR_HPP

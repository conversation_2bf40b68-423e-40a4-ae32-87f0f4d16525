#ifndef ZEXUAN_NET_POLLER_IMPL_HPP
#define ZEXUAN_NET_POLLER_IMPL_HPP

#include <vector>

#include "poller.hpp"

#ifdef __linux__
struct epoll_event;  // 前向声明，具体定义在实现文件中
#endif

namespace zexuan::net {

  // EPollPoller - Linux专用实现，使用epoll系统调用
  class EPollPoller : public Poller {
  public:
    EPollPoller();
    ~EPollPoller() override;

    Timestamp poll(int timeoutMs, ChannelList* activeChannels) override;
    void updateChannel(Channel* channel) override;
    void removeChannel(Channel* channel) override;
    bool hasChannel(Channel* channel) const override;

  private:
    static const int kInitEventListSize = 16;
    static const char* operationToString(int op);

    void fillActiveChannels(int numEvents, ChannelList* activeChannels) const;
    void update(int operation, Channel* channel);

    using EventList = std::vector<epoll_event>;
    int epollfd_;
    EventList events_;
  };

}  // namespace zexuan::net

#endif  // ZEXUAN_NET_POLLER_IMPL_HPP

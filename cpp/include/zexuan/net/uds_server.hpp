#ifndef ZEXUAN_NET_UDS_SERVER_HPP
#define ZEXUAN_NET_UDS_SERVER_HPP

#include <atomic>
#include <map>
#include <string>

#include "zexuan/net/uds_connection.hpp"
#include "zexuan/net/callbacks.hpp"

namespace zexuan {
  namespace net {

    class UdsAcceptor;
    class EventLoop;
    class EventLoopThreadPool;

    ///
    /// UDS server, supports single-threaded and thread-pool models.
    ///
    /// This is an interface class, so don't expose too much details.
    class UdsServer {
    public:
      typedef std::function<void(EventLoop*)> ThreadInitCallback;
      enum Option {
        kNoReusePort,
        kReusePort,
      };

      UdsServer(EventLoop* loop, const std::string& listenPath, const std::string& nameArg,
                Option option = kNoReusePort);
      ~UdsServer();  // force out-line dtor, for std::unique_ptr members.

      // 禁用拷贝构造和赋值
      UdsServer(const UdsServer&) = delete;
      UdsServer& operator=(const UdsServer&) = delete;

      const std::string& listenPath() const { return listenPath_; }
      const std::string& name() const { return name_; }
      EventLoop* getLoop() const { return loop_; }

      /// Set the number of threads for handling input.
      ///
      /// Always accepts new connection in loop's thread.
      /// Must be called before @c start
      /// @param numThreads
      /// - 0 means all I/O in loop's thread, no thread will created.
      ///   this is the default value.
      /// - 1 means all I/O in another thread.
      /// - N means a thread pool with N threads, new connections
      ///   are assigned on a round-robin basis.
      void setThreadNum(int numThreads);
      void setThreadInitCallback(const ThreadInitCallback& cb) { threadInitCallback_ = cb; }
      /// valid after calling start()
      std::shared_ptr<EventLoopThreadPool> threadPool() { return threadPool_; }

      /// Starts the server if it's not listening.
      ///
      /// It's harmless to call it multiple times.
      /// Thread safe.
      void start();

      /// Set connection callback.
      /// Not thread safe.
      void setConnectionCallback(const UdsConnectionCallback& cb) { connectionCallback_ = cb; }

      /// Set message callback.
      /// Not thread safe.
      void setMessageCallback(const UdsMessageCallback& cb) { messageCallback_ = cb; }

      /// Set write complete callback.
      /// Not thread safe.
      void setWriteCompleteCallback(const UdsWriteCompleteCallback& cb) {
        writeCompleteCallback_ = cb;
      }

    private:
      /// Not thread safe, but in loop
      void newConnection(int sockfd, const std::string& peerPath);
      /// Thread safe.
      void removeConnection(const UdsConnectionPtr& conn);
      /// Not thread safe, but in loop
      void removeConnectionInLoop(const UdsConnectionPtr& conn);

      typedef std::map<std::string, UdsConnectionPtr> ConnectionMap;

      EventLoop* loop_;  // the acceptor loop
      const std::string listenPath_;
      const std::string name_;
      std::unique_ptr<UdsAcceptor> acceptor_;  // avoid revealing UdsAcceptor
      std::shared_ptr<EventLoopThreadPool> threadPool_;
      UdsConnectionCallback connectionCallback_;
      UdsMessageCallback messageCallback_;
      UdsWriteCompleteCallback writeCompleteCallback_;
      ThreadInitCallback threadInitCallback_;
      std::atomic<int> started_;
      // always in loop thread
      int nextConnId_;
      ConnectionMap connections_;
    };

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_UDS_SERVER_HPP

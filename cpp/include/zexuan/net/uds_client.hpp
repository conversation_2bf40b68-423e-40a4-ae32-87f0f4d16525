#ifndef ZEXUAN_NET_UDS_CLIENT_HPP
#define ZEXUAN_NET_UDS_CLIENT_HPP

#include <mutex>
#include <string>

#include "zexuan/net/uds_connection.hpp"
#include "zexuan/net/callbacks.hpp"

namespace zexuan {
  namespace net {

    class UdsConnector;
    typedef std::shared_ptr<UdsConnector> UdsConnectorPtr;

    class UdsClient {
    public:
      UdsClient(EventLoop* loop, const std::string& serverPath,
                const std::string& nameArg);
      ~UdsClient();  // force out-line dtor, for std::unique_ptr members.

      // 禁用拷贝构造和赋值
      UdsClient(const UdsClient&) = delete;
      UdsClient& operator=(const UdsClient&) = delete;

      void connect();
      void disconnect();
      void stop();

      UdsConnectionPtr connection() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return connection_;
      }

      EventLoop* getLoop() const { return loop_; }
      bool retry() const { return retry_; }
      void enableRetry() { retry_ = true; }

      const std::string& name() const { return name_; }
      const std::string& serverPath() const { return serverPath_; }

      /// Set connection callback.
      /// Not thread safe.
      void setConnectionCallback(UdsConnectionCallback cb) { connectionCallback_ = std::move(cb); }

      /// Set message callback.
      /// Not thread safe.
      void setMessageCallback(UdsMessageCallback cb) { messageCallback_ = std::move(cb); }

      /// Set write complete callback.
      /// Not thread safe.
      void setWriteCompleteCallback(UdsWriteCompleteCallback cb) {
        writeCompleteCallback_ = std::move(cb);
      }

    private:
      /// Not thread safe, but in loop
      void newConnection(int sockfd);
      /// Not thread safe, but in loop
      void removeConnection(const UdsConnectionPtr& conn);

      EventLoop* loop_;
      UdsConnectorPtr connector_;  // avoid revealing UdsConnector
      const std::string serverPath_;
      const std::string name_;
      UdsConnectionCallback connectionCallback_;
      UdsMessageCallback messageCallback_;
      UdsWriteCompleteCallback writeCompleteCallback_;
      bool retry_;    // atomic
      bool connect_;  // atomic
      // always in loop thread
      int nextConnId_;
      mutable std::mutex mutex_;
      UdsConnectionPtr connection_;
    };

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_UDS_CLIENT_HPP

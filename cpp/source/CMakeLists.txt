# zexuan_base 库
add_library(zexuan_base SHARED)

file(GLOB_RECURSE BASE_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/zexuan/base/*.cpp"
)

target_sources(zexuan_base
    PRIVATE
        ${BASE_SOURCES}
)

set_target_properties(zexuan_base PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

target_include_directories(zexuan_base
    PUBLIC
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(zexuan_base
    PUBLIC
        spdlog::spdlog
        nlohmann_json::nlohmann_json
)

# zexuan_utils 库
add_library(zexuan_utils SHARED)

file(GLOB_RECURSE UTILS_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/zexuan/utils/*.cpp"
)

target_sources(zexuan_utils
    PRIVATE
        ${UTILS_SOURCES}
)

set_target_properties(zexuan_utils PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

target_include_directories(zexuan_utils
    PUBLIC
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(zexuan_utils
    PUBLIC
        zexuan_base
        nlohmann_json::nlohmann_json
        spdlog::spdlog
)

# zexuan_net 库
add_library(zexuan_net SHARED)

file(GLOB_RECURSE NET_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/zexuan/net/*.cpp"
)

target_sources(zexuan_net
    PRIVATE
        ${NET_SOURCES}
)

set_target_properties(zexuan_net PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

target_include_directories(zexuan_net
    PUBLIC
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(zexuan_net
    PUBLIC
        zexuan_base
        zexuan_utils
        spdlog::spdlog
)

# zexuan_protocol 库
add_library(zexuan_protocol SHARED)

file(GLOB_RECURSE PROTOCOL_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/zexuan/protocol/*.cpp"
)

target_sources(zexuan_protocol
    PRIVATE
        ${PROTOCOL_SOURCES}
)

set_target_properties(zexuan_protocol PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

target_include_directories(zexuan_protocol
    PUBLIC
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(zexuan_protocol
    PUBLIC
        zexuan_base
        zexuan_net
        zexuan_bus
        zexuan_utils
        nlohmann_json::nlohmann_json
        spdlog::spdlog
)

# zexuan_bus 库
add_library(zexuan_bus SHARED)

file(GLOB_RECURSE BUS_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/zexuan/bus/*.cpp"
)

target_sources(zexuan_bus
    PRIVATE
        ${BUS_SOURCES}
)

set_target_properties(zexuan_bus PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

target_include_directories(zexuan_bus
    PUBLIC
        ${CMAKE_SOURCE_DIR}/include
)

target_link_libraries(zexuan_bus
    PUBLIC
        zexuan_base
        zexuan_net
        nlohmann_json::nlohmann_json
        spdlog::spdlog
)

# 安装所有库
install(TARGETS zexuan_base zexuan_utils zexuan_net zexuan_protocol zexuan_bus
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)



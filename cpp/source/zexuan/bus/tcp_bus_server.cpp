/**
 * @file tcp_message_bus.cpp
 * @brief TCP消息总线服务器类实现
 * <AUTHOR>
 * @date 2025-08-24
 */

#include "zexuan/bus/tcp_bus_server.hpp"
#include <spdlog/spdlog.h>
#include <fstream>
#include "zexuan/net/address.hpp"
#include "nlohmann/json.hpp"

namespace zexuan {
namespace bus {

TcpBusServer::TcpBusServer(zexuan::net::EventLoop* event_loop, const std::string& config_path)
    : config_path_(config_path) {
    
    if (!event_loop) {
        throw std::invalid_argument("EventLoop cannot be null");
    }

    // 加载配置
    if (!loadConfig()) {
        throw std::invalid_argument("Failed to load config from: " + config_path);
    }

    // 创建订阅管理器
    subscription_manager_ = std::make_shared<SubscriptionManager>();

    // 创建消息路由器
    message_router_ = std::make_shared<MessageRouter>(subscription_manager_);
    message_router_->setVerboseLogging(verbose_logging_);

    // 创建TCP服务器
    zexuan::net::Address listen_addr(host_, port_);
    tcp_server_ = std::make_unique<zexuan::net::TcpServer>(event_loop, listen_addr, "TcpBusServer");

    // 设置线程池大小
    tcp_server_->setThreadNum(thread_pool_size_);

    // 设置回调函数
    tcp_server_->setConnectionCallback(
        [this](const TcpConnectionPtr& conn) { onConnection(conn); });

    tcp_server_->setMessageCallback(
        [this](const TcpConnectionPtr& conn, zexuan::net::Buffer* buf, zexuan::net::Timestamp time) {
            onMessage(conn, buf, time);
        });

    spdlog::info("TcpBusServer created: {}:{}, threads: {}",
                host_, port_, thread_pool_size_);
}

TcpBusServer::~TcpBusServer() {
    stop();
}

bool TcpBusServer::loadConfig() {
    try {
        std::ifstream config_file(config_path_);
        if (!config_file.is_open()) {
            spdlog::warn("Cannot open config file: {}, using default config", config_path_);
            return true; // 使用默认配置
        }

        nlohmann::json config_json;
        config_file >> config_json;

        // 查找bus配置
        if (config_json.contains("bus") && config_json["bus"].contains("server")) {
            auto bus_config = config_json["bus"]["server"];

            if (bus_config.contains("host") && bus_config["host"].is_string()) {
                host_ = bus_config["host"].get<std::string>();
            }

            if (bus_config.contains("port") && bus_config["port"].is_number_integer()) {
                port_ = bus_config["port"].get<uint16_t>();
            }

            if (bus_config.contains("thread_pool_size") && bus_config["thread_pool_size"].is_number_integer()) {
                thread_pool_size_ = bus_config["thread_pool_size"].get<int>();
            }

            if (bus_config.contains("verbose_logging") && bus_config["verbose_logging"].is_boolean()) {
                verbose_logging_ = bus_config["verbose_logging"].get<bool>();
            }

            if (bus_config.contains("max_connections") && bus_config["max_connections"].is_number_integer()) {
                max_connections_ = bus_config["max_connections"].get<size_t>();
            }
        }

        return true;
    } catch (const std::exception& e) {
        spdlog::error("Failed to load config from {}: {}", config_path_, e.what());
        return false;
    }
}

bool TcpBusServer::start() {
    if (running_.load()) {
        spdlog::warn("TcpBusServer is already running");
        return true;
    }

    try {
        tcp_server_->start();
        running_.store(true);

        spdlog::info("TcpBusServer started successfully on {}:{}", host_, port_);
        return true;

    } catch (const std::exception& e) {
        spdlog::error("Failed to start TcpBusServer: {}", e.what());
        return false;
    }
}

void TcpBusServer::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    
    // 清理所有缓冲区
    {
        std::lock_guard<std::mutex> lock(buffers_mutex_);
        client_buffers_.clear();
    }
    
    spdlog::info("TcpBusServer stopped");
}

size_t TcpBusServer::getConnectionCount() const {
    return subscription_manager_ ? subscription_manager_->getClientCount() : 0;
}

SubscriptionManager::Statistics TcpBusServer::getSubscriptionStatistics() const {
    return subscription_manager_ ? subscription_manager_->getStatistics() : SubscriptionManager::Statistics{};
}

RoutingStatistics TcpBusServer::getRoutingStatistics() const {
    return message_router_ ? message_router_->getStatistics() : RoutingStatistics{};
}

void TcpBusServer::resetStatistics() {
    if (message_router_) {
        message_router_->resetStatistics();
    }
}

size_t TcpBusServer::cleanupInvalidConnections() {
    size_t cleaned = subscription_manager_ ? subscription_manager_->cleanupInvalidConnections() : 0;
    
    // 清理无效的缓冲区
    {
        std::lock_guard<std::mutex> lock(buffers_mutex_);
        auto it = client_buffers_.begin();
        while (it != client_buffers_.end()) {
            if (!it->first || !it->first->connected()) {
                it = client_buffers_.erase(it);
            } else {
                ++it;
            }
        }
    }
    
    return cleaned;
}

size_t TcpBusServer::broadcastSystemMessage(const std::string& message) {
    if (!message_router_) {
        return 0;
    }

    ControlMessage control_msg;
    control_msg.action = "system_message";
    control_msg.success = true;
    control_msg.message = message;

    return message_router_->broadcastControlMessage(control_msg);
}

void TcpBusServer::onConnection(const TcpConnectionPtr& conn) {
    if (!conn) {
        return;
    }
    
    if (conn->connected()) {
        // 检查连接数限制
        if (getConnectionCount() >= max_connections_) {
            spdlog::warn("Connection limit reached ({}), rejecting connection from {}",
                        max_connections_, conn->peerAddress().toIpPort());
            conn->shutdown();
            return;
        }
        
        // 添加客户端
        subscription_manager_->addClient(conn);
        
        // 创建消息缓冲区
        getOrCreateBuffer(conn);
        
        spdlog::info("Client connected: {} (total: {})", 
                    conn->peerAddress().toIpPort(), getConnectionCount());
        
    } else {
        // 移除客户端
        subscription_manager_->removeClient(conn);
        
        // 移除消息缓冲区
        removeBuffer(conn);
        
        spdlog::info("Client disconnected: {} (total: {})", 
                    conn->peerAddress().toIpPort(), getConnectionCount());
    }
}

void TcpBusServer::onMessage(const TcpConnectionPtr& conn,
                             zexuan::net::Buffer* buf,
                             zexuan::net::Timestamp receive_time) {
    if (!conn || !buf) {
        return;
    }
    
    // 获取消息缓冲区
    MessageBuffer* msg_buffer = getOrCreateBuffer(conn);
    if (!msg_buffer) {
        spdlog::error("Failed to get message buffer for connection: {}", conn->name());
        return;
    }
    
    // 将接收到的数据添加到缓冲区
    const char* data = buf->peek();
    size_t data_size = buf->readableBytes();
    
    msg_buffer->appendData(reinterpret_cast<const uint8_t*>(data), data_size);
    buf->retrieveAll();
    
    // 尝试提取完整消息
    std::optional<std::string> json_message;
    while ((json_message = msg_buffer->extractMessage()).has_value()) {
        handleJsonMessage(conn, json_message.value());
    }
}

void TcpBusServer::handleJsonMessage(const TcpConnectionPtr& conn, const std::string& json_message) {
    try {
        nlohmann::json json = nlohmann::json::parse(json_message);

        std::string msg_category = getMessageCategory(json_message);

        if (msg_category == "subscription") {
            handleSubscriptionMessage(conn, json_message);
        } else if (msg_category == "common") {
            handleCommonMessage(conn, json_message);
        } else if (msg_category == "event") {
            handleEventMessage(conn, json_message);
        } else {
            spdlog::warn("Unknown message category: {} from {}", msg_category, conn->peerAddress().toIpPort());
            sendErrorResponse(conn, "Unknown message category: " + msg_category);
        }

    } catch (const std::exception& e) {
        spdlog::error("Failed to handle JSON message from {}: {}", conn->peerAddress().toIpPort(), e.what());
        sendErrorResponse(conn, "Invalid JSON message: " + std::string(e.what()));
    }
}

void TcpBusServer::handleSubscriptionMessage(const TcpConnectionPtr& conn, const std::string& json_str) {
    auto subscription_msg = MessageSerializer::deserializeSubscriptionMessage(json_str);
    if (!subscription_msg) {
        spdlog::error("Failed to deserialize subscription message from {}", conn->peerAddress().toIpPort());
        sendErrorResponse(conn, "Invalid subscription message format");
        return;
    }

    // 处理订阅请求
    ControlMessage response = subscription_manager_->handleSubscription(conn, subscription_msg.value());

    // 发送响应
    if (!message_router_->sendControlMessage(conn, response)) {
        spdlog::error("Failed to send subscription response to {}", conn->peerAddress().toIpPort());
    }
}

void TcpBusServer::handleCommonMessage(const TcpConnectionPtr& conn, const std::string& json_str) {
    auto common_msg = MessageSerializer::deserializeCommonMessage(json_str);
    if (!common_msg) {
        spdlog::error("Failed to deserialize common message from {}", conn->peerAddress().toIpPort());
        sendErrorResponse(conn, "Invalid common message format");
        return;
    }

    // 路由消息
    size_t routed_count = message_router_->routeCommonMessage(conn, common_msg.value());

    if (verbose_logging_) {
        spdlog::debug("Routed CommonMessage from {} to {} subscribers",
                     conn->peerAddress().toIpPort(), routed_count);
    }
}

void TcpBusServer::handleEventMessage(const TcpConnectionPtr& conn, const std::string& json_str) {
    auto event_msg = MessageSerializer::deserializeEventMessage(json_str);
    if (!event_msg) {
        spdlog::error("Failed to deserialize event message from {}", conn->peerAddress().toIpPort());
        sendErrorResponse(conn, "Invalid event message format");
        return;
    }

    // 路由消息
    size_t routed_count = message_router_->routeEventMessage(conn, event_msg.value());

    if (verbose_logging_) {
        spdlog::debug("Routed EventMessage from {} to {} subscribers",
                     conn->peerAddress().toIpPort(), routed_count);
    }
}

MessageBuffer* TcpBusServer::getOrCreateBuffer(const TcpConnectionPtr& conn) {
    if (!conn) {
        return nullptr;
    }

    std::lock_guard<std::mutex> lock(buffers_mutex_);

    auto it = client_buffers_.find(conn);
    if (it != client_buffers_.end()) {
        return it->second.get();
    }

    // 创建新的缓冲区
    auto buffer = std::make_unique<MessageBuffer>();
    MessageBuffer* buffer_ptr = buffer.get();
    client_buffers_[conn] = std::move(buffer);

    return buffer_ptr;
}

void TcpBusServer::removeBuffer(const TcpConnectionPtr& conn) {
    if (!conn) {
        return;
    }

    std::lock_guard<std::mutex> lock(buffers_mutex_);
    client_buffers_.erase(conn);
}

void TcpBusServer::sendErrorResponse(const TcpConnectionPtr& conn, const std::string& error_message) {
    if (!conn || !message_router_) {
        return;
    }

    ControlMessage error_response;
    error_response.action = "error";
    error_response.success = false;
    error_response.message = error_message;

    message_router_->sendControlMessage(conn, error_response);
}

std::string TcpBusServer::getMessageCategory(const std::string& json_str) {
    try {
        nlohmann::json json = nlohmann::json::parse(json_str);

        // 检查是否为订阅消息
        if (json.contains("action") &&
            (json["action"] == "subscribe" || json["action"] == "unsubscribe")) {
            return "subscription";
        }

        // 检查是否有msg_category字段
        if (json.contains("msg_category")) {
            return json["msg_category"].get<std::string>();
        }

        // 根据字段推断消息类型
        if (json.contains("type") && json.contains("source_id") && json.contains("target_id")) {
            return "common";
        }

        if (json.contains("event_type") && json.contains("device_uuid")) {
            return "event";
        }

        return "unknown";

    } catch (const std::exception&) {
        return "unknown";
    }
}

} // namespace bus
} // namespace zexuan

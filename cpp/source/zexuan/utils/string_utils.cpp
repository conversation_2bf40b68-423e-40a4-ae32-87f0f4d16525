#include "zexuan/utils/string_utils.hpp"

#include <sstream>

namespace zexuan::utils {

  std::vector<std::string> StringUtils::split(std::string_view str, char delim) {
    std::vector<std::string> result;
    std::string::size_type start = 0;
    std::string::size_type end = str.find(delim);

    while (end != std::string_view::npos) {
      if (end != start) {  // 避免空字符串
        result.emplace_back(str.substr(start, end - start));
      }
      start = end + 1;
      end = str.find(delim, start);
    }

    if (start < str.length()) {  // 添加最后一部分
      result.emplace_back(str.substr(start));
    }

    return result;
  }

  std::vector<std::string> StringUtils::split(std::string_view str, std::string_view delims) {
    std::vector<std::string> result;
    std::string::size_type start = 0;
    std::string::size_type end = str.find_first_of(delims);

    while (end != std::string_view::npos) {
      if (end != start) {  // 避免空字符串
        result.emplace_back(str.substr(start, end - start));
      }
      start = end + 1;
      end = str.find_first_of(delims, start);
    }

    if (start < str.length()) {  // 添加最后一部分
      result.emplace_back(str.substr(start));
    }

    return result;
  }

  std::string StringUtils::trim(std::string_view str) { return trimRight(trimLeft(str)); }

  std::string StringUtils::trimLeft(std::string_view str) {
    auto start = std::find_if_not(str.begin(), str.end(),
                                  [](unsigned char ch) { return std::isspace(ch); });
    return std::string(start, str.end());
  }

  std::string StringUtils::trimRight(std::string_view str) {
    auto end = std::find_if_not(str.rbegin(), str.rend(), [](unsigned char ch) {
                 return std::isspace(ch);
               }).base();
    return std::string(str.begin(), end);
  }

  std::string StringUtils::toUpper(std::string_view str) {
    std::string result(str);
    std::transform(result.begin(), result.end(), result.begin(),
                   [](unsigned char ch) { return std::toupper(ch); });
    return result;
  }

  std::string StringUtils::toLower(std::string_view str) {
    std::string result(str);
    std::transform(result.begin(), result.end(), result.begin(),
                   [](unsigned char ch) { return std::tolower(ch); });
    return result;
  }

  std::string StringUtils::replace(std::string_view str, std::string_view from,
                                   std::string_view to) {
    if (from.empty()) return std::string(str);

    std::string result(str);
    size_t pos = 0;
    while ((pos = result.find(from, pos)) != std::string::npos) {
      result.replace(pos, from.length(), to);
      pos += to.length();
    }
    return result;
  }

  bool StringUtils::startsWith(std::string_view str, std::string_view prefix) {
    return str.length() >= prefix.length() && str.substr(0, prefix.length()) == prefix;
  }

  bool StringUtils::endsWith(std::string_view str, std::string_view suffix) {
    return str.length() >= suffix.length() && str.substr(str.length() - suffix.length()) == suffix;
  }

  std::string StringUtils::join(const std::vector<std::string>& parts, std::string_view delim) {
    if (parts.empty()) return "";

    std::ostringstream result;
    auto it = parts.begin();
    result << *it++;

    while (it != parts.end()) {
      result << delim << *it++;
    }

    return result.str();
  }

}  // namespace zexuan::utils

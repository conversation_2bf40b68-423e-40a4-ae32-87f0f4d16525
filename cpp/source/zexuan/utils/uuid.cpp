#include "zexuan/utils/uuid.hpp"

#include <algorithm>
#include <cctype>
#include <chrono>
#include <fstream>
#include <iomanip>
#include <random>
#include <sstream>
#ifdef __linux__
#  include <sys/random.h>
#  include <unistd.h>

namespace zexuan {
  namespace utils {

    /// @brief Linux 平台的 UUID 生成器实现
    class LinuxUUIDGenerator : public UUIDGenerator {
    public:
      LinuxUUIDGenerator() {
        // 检查系统是否支持 getrandom 系统调用
        uint8_t test_byte;
        available_ = (getrandom(&test_byte, 1, GRND_NONBLOCK) >= 0);

        // 如果 getrandom 不可用，尝试 /dev/urandom
        if (!available_) {
          std::ifstream urandom("/dev/urandom", std::ios::binary);
          available_ = urandom.good();
        }
      }

      UUID GenerateRandom() override {
        UUID uuid;

        // 优先使用 getrandom 系统调用
        if (getrandom(uuid.data.data(), 16, 0) == 16) {
          // 设置版本号为 4 (随机 UUID)
          uuid.data[6] = (uuid.data[6] & 0x0F) | 0x40;
          // 设置变体位
          uuid.data[8] = (uuid.data[8] & 0x3F) | 0x80;
          return uuid;
        }

        // 备用方案：使用 /dev/urandom
        std::ifstream urandom("/dev/urandom", std::ios::binary);
        if (urandom.read(reinterpret_cast<char*>(uuid.data.data()), 16)) {
          // 设置版本号为 4 (随机 UUID)
          uuid.data[6] = (uuid.data[6] & 0x0F) | 0x40;
          // 设置变体位
          uuid.data[8] = (uuid.data[8] & 0x3F) | 0x80;
          return uuid;
        }

        // 最后备用方案：使用 C++ 随机数生成器
        return GenerateRandomFallback();
      }

      UUID GenerateTimeBased() override {
        UUID uuid;

        // 获取当前时间（100纳秒为单位，从1582年10月15日开始）
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        auto nanos = std::chrono::duration_cast<std::chrono::nanoseconds>(duration).count();

        // UUID v1 时间戳：从 1582-10-15 00:00:00 UTC 开始的 100ns 间隔数
        constexpr uint64_t UUID_EPOCH_OFFSET = 0x01B21DD213814000ULL;
        uint64_t timestamp = (nanos / 100) + UUID_EPOCH_OFFSET;

        // 时间戳低32位
        uuid.data[0] = (timestamp >> 24) & 0xFF;
        uuid.data[1] = (timestamp >> 16) & 0xFF;
        uuid.data[2] = (timestamp >> 8) & 0xFF;
        uuid.data[3] = timestamp & 0xFF;

        // 时间戳中16位
        uuid.data[4] = (timestamp >> 40) & 0xFF;
        uuid.data[5] = (timestamp >> 32) & 0xFF;

        // 时间戳高12位 + 版本号(4位)
        uuid.data[6] = ((timestamp >> 56) & 0x0F) | 0x10;  // 版本1
        uuid.data[7] = (timestamp >> 48) & 0xFF;

        // 时钟序列 + 变体位
        static uint16_t clock_seq = GetClockSequence();
        uuid.data[8] = ((clock_seq >> 8) & 0x3F) | 0x80;  // 变体位
        uuid.data[9] = clock_seq & 0xFF;

        // 节点ID（MAC地址或随机数）
        GetNodeId(&uuid.data[10]);

        return uuid;
      }

      bool IsAvailable() const noexcept override { return available_; }

    private:
      bool available_ = false;

      UUID GenerateRandomFallback() {
        static std::random_device rd;
        static std::mt19937_64 gen(rd());
        static std::uniform_int_distribution<uint64_t> dis;

        UUID uuid;
        uint64_t* data64 = reinterpret_cast<uint64_t*>(uuid.data.data());
        data64[0] = dis(gen);
        data64[1] = dis(gen);

        // 设置版本号和变体位
        uuid.data[6] = (uuid.data[6] & 0x0F) | 0x40;
        uuid.data[8] = (uuid.data[8] & 0x3F) | 0x80;

        return uuid;
      }

      uint16_t GetClockSequence() {
        static uint16_t clock_seq = 0;
        if (clock_seq == 0) {
          // 使用随机数初始化时钟序列
          std::random_device rd;
          clock_seq = rd() & 0x3FFF;
        }
        return clock_seq++;
      }

      void GetNodeId(uint8_t* node) {
        // 简化实现：使用随机数作为节点ID
        // 实际实现可以尝试获取 MAC 地址
        static bool initialized = false;
        static uint8_t node_id[6];

        if (!initialized) {
          std::random_device rd;
          std::mt19937 gen(rd());
          std::uniform_int_distribution<uint8_t> dis;

          for (int i = 0; i < 6; ++i) {
            node_id[i] = dis(gen);
          }
          // 设置多播位，表示这不是真实的MAC地址
          node_id[0] |= 0x01;
          initialized = true;
        }

        std::copy(node_id, node_id + 6, node);
      }
    };

    // 全局实例
    static LinuxUUIDGenerator g_generator;

    // UUID 工具函数实现
    namespace uuid {

      UUIDGenerator& GetGenerator() { return g_generator; }

      UUID GenerateRandom() { return g_generator.GenerateRandom(); }

      UUID GenerateTimeBased() { return g_generator.GenerateTimeBased(); }

      std::string ToString(const UUID& uuid) {
        std::ostringstream oss;
        oss << std::hex << std::setfill('0');

        // xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
        for (size_t i = 0; i < 16; ++i) {
          if (i == 4 || i == 6 || i == 8 || i == 10) {
            oss << '-';
          }
          oss << std::setw(2) << static_cast<unsigned>(uuid.data[i]);
        }

        return oss.str();
      }

      std::string ToCompactString(const UUID& uuid) {
        std::ostringstream oss;
        oss << std::hex << std::setfill('0');

        for (uint8_t byte : uuid.data) {
          oss << std::setw(2) << static_cast<unsigned>(byte);
        }

        return oss.str();
      }

      std::optional<UUID> FromString(const std::string& str) {
        if (!IsValidFormat(str)) {
          return std::nullopt;
        }

        std::string clean_str = str;
        // 移除连字符
        clean_str.erase(std::remove(clean_str.begin(), clean_str.end(), '-'), clean_str.end());

        if (clean_str.length() != 32) {
          return std::nullopt;
        }

        UUID uuid;
        for (size_t i = 0; i < 16; ++i) {
          std::string byte_str = clean_str.substr(i * 2, 2);
          try {
            uuid.data[i] = static_cast<uint8_t>(std::stoul(byte_str, nullptr, 16));
          } catch (...) {
            return std::nullopt;
          }
        }

        return uuid;
      }

      UUID Null() { return UUID{}; }

      bool IsValidFormat(const std::string& str) {
        // 检查标准格式: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx (36字符)
        if (str.length() == 36) {
          if (str[8] != '-' || str[13] != '-' || str[18] != '-' || str[23] != '-') {
            return false;
          }
          // 检查其他字符是否为十六进制
          for (size_t i = 0; i < str.length(); ++i) {
            if (i == 8 || i == 13 || i == 18 || i == 23) continue;
            if (!std::isxdigit(str[i])) return false;
          }
          return true;
        }

        // 检查紧凑格式: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (32字符)
        if (str.length() == 32) {
          return std::all_of(str.begin(), str.end(), [](char c) { return std::isxdigit(c); });
        }

        return false;
      }

    }  // namespace uuid
  }  // namespace utils
}  // namespace zexuan
#endif
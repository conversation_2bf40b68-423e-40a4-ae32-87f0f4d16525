#include "zexuan/protocol/transform/protocol_transform.hpp"

#include "zexuan/base/base_types.hpp"
#include "zexuan/protocol/transform/gw104/gw104_transform.hpp"
namespace zexuan {
  namespace protocol {
    namespace transform {

      // 工厂方法实现
      std::unique_ptr<ProtocolTransform> ProtocolTransformFactory::CreateTransform(
          ProtocolType type) {
        switch (type) {
          case ProtocolType::gw104:
            return std::make_unique<gw104::GW104Transform>();
          default:
            return nullptr;
        }
      }

    }  // namespace transform
  }  // namespace protocol
}  // namespace zexuan

#include "zexuan/protocol/transform/gw104/asdu_type2_gws.hpp"

#include <spdlog/spdlog.h>

namespace zexuan {
  namespace protocol {
    namespace transform {
      namespace gw104 {

        int AsduType2GWS::ParseToCommonMessage(const base::ProtocolFrame& frame,
                                               base::CommonMessage& common_msg) {
          spdlog::debug("ASDU Type2: Parsing ProtocolFrame to CommonMessage (Simple Test Mode)");

          // 简单的测试转换：直接复制数据
          common_msg.type = base::MessageType::COMMAND;
          common_msg.source_id = frame.frame_id;
          common_msg.target_id = base::SERVICE_SUBJECT_ID;

          // 直接复制frame的数据到CommonMessage
          common_msg.data = frame.data;

          spdlog::debug("ASDU Type2: Successfully converted frame to CommonMessage, data_size={}",
                        frame.data.size());
          return 0;
        }

        int AsduType2GWS::ConvertFromCommonMessage(const base::CommonMessage& common_msg,
                                                   base::ProtocolFrame& frame) {
          spdlog::debug("ASDU Type2: Converting CommonMessage to ProtocolFrame (Simple Test Mode)");

          // 简单的测试转换：直接复制数据
          frame.data = common_msg.data;
          frame.type = GetSupportedType();
          frame.frame_id = common_msg.target_id;

          spdlog::debug("ASDU Type2: Successfully converted CommonMessage to frame, data_size={}",
                        frame.data.size());
          return 0;
        }

        // 简化版本，不需要复杂的解析逻辑

      }  // namespace gw104
    }  // namespace transform
  }  // namespace protocol
}  // namespace zexuan

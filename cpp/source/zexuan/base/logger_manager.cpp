/**
 * @file logger_manager.cpp
 * @brief 简化的日志管理器实现
 * <AUTHOR>
 * @date 2025-08-24
 */

#include "zexuan/base/logger_manager.hpp"

#include <filesystem>
#include <iostream>
#include <fstream>
#include <algorithm>

#include "spdlog/sinks/rotating_file_sink.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/spdlog.h"
#include "nlohmann/json.hpp"

namespace zexuan {
namespace base {

bool LoggerManager::initialize(const std::string& log_filename, const std::string& config_path) {
    return load_config_and_init(log_filename, config_path);
}

bool LoggerManager::load_config_and_init(const std::string& log_filename, const std::string& config_path) {
    try {
        // 读取配置文件
        std::ifstream config_file(config_path);
        if (!config_file.is_open()) {
            std::cerr << "Failed to open config file: " << config_path << std::endl;
            return false;
        }

        nlohmann::json config;
        config_file >> config;

        // 获取spdlog配置，如果不存在则使用默认值
        auto spdlog_config = config.value("spdlog", nlohmann::json::object());
        
        // 读取配置参数，提供默认值
        std::string log_level = spdlog_config.value("level", "info");
        std::string log_pattern = spdlog_config.value("pattern", "[%Y-%m-%d %H:%M:%S.%e] [thread %t] [%^%l%$] %v");
        size_t max_file_size = spdlog_config.value("max_file_size", 10 * 1024 * 1024); // 10MB
        size_t max_files = spdlog_config.value("max_files", 5);
        bool flush_on_debug = spdlog_config.value("flush_on_debug", true);
        std::string log_dir = spdlog_config.value("log_directory", "logs");

        // 构建完整的日志文件路径
        std::string log_file_path = log_dir + "/" + log_filename;

        // 确保日志目录存在
        std::filesystem::create_directories(log_dir);

        // 创建rotating file logger
        auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            log_file_path, max_file_size, max_files);
        
        auto logger = std::make_shared<spdlog::logger>("default", file_sink);
        
        // 设置日志级别
        spdlog::level::level_enum level = spdlog::level::info;
        std::string lower_level = log_level;
        std::transform(lower_level.begin(), lower_level.end(), lower_level.begin(), ::tolower);
        
        if (lower_level == "trace") level = spdlog::level::trace;
        else if (lower_level == "debug") level = spdlog::level::debug;
        else if (lower_level == "info") level = spdlog::level::info;
        else if (lower_level == "warn" || lower_level == "warning") level = spdlog::level::warn;
        else if (lower_level == "error") level = spdlog::level::err;
        else if (lower_level == "critical") level = spdlog::level::critical;
        else if (lower_level == "off") level = spdlog::level::off;
        
        logger->set_level(level);
        
        // 设置日志格式
        logger->set_pattern(log_pattern);
        
        // 设置刷新策略
        if (flush_on_debug) {
            logger->flush_on(spdlog::level::debug);
        }

        // 设置为默认logger
        spdlog::set_default_logger(logger);

        // 记录初始化成功
        spdlog::info("Logger initialized successfully with file: {}", log_file_path);

        return true;

    } catch (const nlohmann::json::exception& ex) {
        std::cerr << "JSON parsing error in config file " << config_path 
                  << ": " << ex.what() << std::endl;
        
        // 尝试创建控制台日志器作为备用
        try {
            auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
            auto console_logger = std::make_shared<spdlog::logger>("default", console_sink);
            console_logger->set_level(spdlog::level::info);
            console_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] %v");
            spdlog::set_default_logger(console_logger);

            spdlog::warn("Using console logger as fallback due to config error");
            return true;
        } catch (...) {
            std::cerr << "Failed to create fallback console logger" << std::endl;
            return false;
        }
        
    } catch (const std::exception& ex) {
        std::cerr << "Failed to initialize logger: " << ex.what() << std::endl;
        return false;
    }
}

} // namespace base
} // namespace zexuan

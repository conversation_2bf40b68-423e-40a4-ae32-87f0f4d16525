/**
 * @file register_object.cpp
 * @brief 注册对象基类实现 - 现代化版本
 * <AUTHOR> from CNXEcRegisterObject
 * @date 2025-08-16
 */

#include "zexuan/base/register_object.hpp"

#include <algorithm>
#include <format>
#include <iostream>
#include <stdexcept>

#include "zexuan/base/mediator.hpp"

namespace zexuan {
  namespace base {

    RegisterObject::RegisterObject(ObjectId object_id, std::string class_name)
        : class_name_(std::move(class_name)) {
      reg_obj_.object_id = object_id;
    }

    RegisterObject::~RegisterObject() noexcept {
      try {
        if (initialized_.load()) {
          Exit();
        }
      } catch (...) {
        // 析构函数中不应该抛出异常
      }
    }

    VoidResult RegisterObject::SetRegObjDesc(std::string description) noexcept {
      try {
        std::lock_guard<std::mutex> lock(state_mutex_);
        reg_obj_.description = std::move(description);
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    const RegisterObjectInfo& RegisterObject::GetRegObjProperty() const noexcept {
      return reg_obj_;
    }

    VoidResult RegisterObject::Init() {
      bool expected = false;
      if (!initialized_.compare_exchange_strong(expected, true)) {
        return {};  // 已经初始化
      }

      try {
        // 检查中介者是否已设置
        if (!mediator_) {
          initialized_.store(false);
          return std::unexpected(ErrorCode::MEDIATOR_NOT_AVAILABLE);
        }

        // 设置对象指针
        reg_obj_.set_object_ptr(shared_from_this());

        // 注册到服务中介
        auto result = RegisterToMediator();
        if (!result) {
          initialized_.store(false);
          return result;
        }

        return {};
      } catch (const std::exception&) {
        initialized_.store(false);
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    VoidResult RegisterObject::Exit() noexcept {
      bool expected = true;
      if (!initialized_.compare_exchange_strong(expected, false)) {
        return {};  // 已经退出或未初始化
      }

      try {
        // 从服务中介注销
        CancelFromMediator();

        // 清理资源
        mediator_.reset();
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    bool RegisterObject::IsCareEventInfo(EventType event_type) const noexcept {
      if (!reg_obj_.has_events()) {
        return false;
      }

      const auto& event_types = reg_obj_.event_types.value();
      return std::ranges::find(event_types, event_type) != event_types.end();
    }

    bool RegisterObject::IsCareDevInfo(const DeviceUUID& device_id) const noexcept {
      if (!reg_obj_.has_devices()) {
        return false;
      }

      const auto& devices = reg_obj_.devices.value();
      return std::ranges::find(devices, device_id) != devices.end();
    }

    void RegisterObject::SetMediator(std::shared_ptr<Mediator> mediator) noexcept {
      std::lock_guard<std::mutex> lock(state_mutex_);
      mediator_ = std::move(mediator);
    }

    std::shared_ptr<Mediator> RegisterObject::GetMediator() const noexcept {
      std::lock_guard<std::mutex> lock(state_mutex_);
      return mediator_;
    }

    VoidResult RegisterObject::RegisterToMediator() {
      try {
        // 检查中介者是否已设置
        auto mediator = GetMediator();
        if (!mediator) {
          return std::unexpected(ErrorCode::MEDIATOR_NOT_AVAILABLE);
        }

        // 调用派生类特定的注册逻辑
        auto result = DoRegister();
        if (!result) {
          return result;
        }

        std::cout << std::format("RegisterToMediator() 注册对象成功，ID={}, 类型={}\n",
                                 reg_obj_.object_id, class_name_);
        return {};
      } catch (const std::exception&) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    VoidResult RegisterObject::CancelFromMediator() noexcept {
      try {
        auto mediator = GetMediator();
        if (!mediator) {
          return {};  // 中介者为空，认为已经注销
        }

        // 调用派生类特定的注销逻辑
        auto result = DoUnregister();
        if (!result) {
          return result;
        }

        std::cout << std::format("CancelFromMediator() 注销对象成功，ID={}, 类型={}\n",
                                 reg_obj_.object_id, class_name_);
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

  }  // namespace base
}  // namespace zexuan

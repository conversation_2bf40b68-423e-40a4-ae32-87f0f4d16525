#include "zexuan/net/event_loop.hpp"

#include <signal.h>
#include <spdlog/spdlog.h>
#include <sys/eventfd.h>
#include <unistd.h>

#include <cassert>
#include <cstring>

#include "zexuan/net/channel.hpp"

using namespace zexuan;
using namespace zexuan::net;

namespace {
  __thread EventLoop* t_loopInThisThread = 0;

  const int kPollTimeMs = 10000;

  int createEventfd() {
    int evtfd = ::eventfd(0, EFD_NONBLOCK | EFD_CLOEXEC);
    if (evtfd < 0) {
      spdlog::error("Failed to create eventfd: {}", strerror(errno));
      abort();
    }
    return evtfd;
  }

#pragma GCC diagnostic ignored "-Wold-style-cast"
  class IgnoreSigPipe {
  public:
    IgnoreSigPipe() { ::signal(SIGPIPE, SIG_IGN); }
  };
#pragma GCC diagnostic error "-Wold-style-cast"

  IgnoreSigPipe initObj;
}  // namespace

EventLoop* EventLoop::getEventLoopOfCurrentThread() { return t_loopInThisThread; }

EventLoop::EventLoop()
    : looping_(false),
      quit_(false),
      eventHandling_(false),
      callingPendingFunctors_(false),
      iteration_(0),
      threadId_(std::this_thread::get_id()),
      poller_(Poller::createDefaultPoller()),
      wakeupFd_(createEventfd()),
      wakeupChannel_(new Channel(this, wakeupFd_)),
      currentActiveChannel_(NULL) {
  spdlog::debug("EventLoop created {}", static_cast<void*>(this));
  if (t_loopInThisThread) {
    spdlog::critical("Another EventLoop {} exists in this thread",
                     static_cast<void*>(t_loopInThisThread));
  } else {
    t_loopInThisThread = this;
  }

  wakeupChannel_->setReadCallback(
      [this](std::chrono::system_clock::time_point ts) { handleRead(ts); });
  // we are always reading the wakeupfd
  wakeupChannel_->enableReading();
}

EventLoop::~EventLoop() {
  spdlog::debug("EventLoop {} destructs", static_cast<void*>(this));
  wakeupChannel_->disableAll();
  wakeupChannel_->remove();
  ::close(wakeupFd_);
  t_loopInThisThread = NULL;
}

void EventLoop::loop() {
  assert(!looping_);
  assertInLoopThread();
  looping_ = true;
  quit_ = false;  // FIXME: what if someone calls quit() before loop() ?
  spdlog::trace("EventLoop {} start looping", static_cast<void*>(this));

  while (!quit_) {
    activeChannels_.clear();
    pollReturnTime_ = poller_->poll(kPollTimeMs, &activeChannels_);
    ++iteration_;
    spdlog::trace("EventLoop {} poll {} channels", static_cast<void*>(this),
                  activeChannels_.size());

    // TODO sort channel by priority
    eventHandling_ = true;
    for (Channel* channel : activeChannels_) {
      currentActiveChannel_ = channel;
      currentActiveChannel_->handleEvent(pollReturnTime_);
    }
    currentActiveChannel_ = NULL;
    eventHandling_ = false;
    doPendingFunctors();
  }

  spdlog::trace("EventLoop {} stop looping", static_cast<void*>(this));
  looping_ = false;
}

void EventLoop::quit() {
  quit_ = true;
  // There is a chance that loop() just executed while(!quit_) and exited,
  // then EventLoop destructs, then we are accessing an invalid object.
  // Can be fixed using mutex_ in both places.
  if (!isInLoopThread()) {
    wakeup();
  }
}

void EventLoop::runInLoop(Functor cb) {
  if (isInLoopThread()) {
    cb();
  } else {
    queueInLoop(std::move(cb));
  }
}

void EventLoop::queueInLoop(Functor cb) {
  {
    std::lock_guard<std::mutex> lock(mutex_);
    pendingFunctors_.push_back(std::move(cb));
  }

  if (!isInLoopThread() || callingPendingFunctors_) {
    wakeup();
  }
}

size_t EventLoop::queueSize() const {
  std::lock_guard<std::mutex> lock(mutex_);
  return pendingFunctors_.size();
}

void EventLoop::wakeup() {
  uint64_t one = 1;
  ssize_t n = ::write(wakeupFd_, &one, sizeof one);
  if (n != sizeof one) {
    spdlog::error("EventLoop::wakeup() writes {} bytes instead of 8", n);
  }
}

void EventLoop::handleRead(std::chrono::system_clock::time_point) {
  uint64_t one = 1;
  ssize_t n = ::read(wakeupFd_, &one, sizeof one);
  if (n != sizeof one) {
    spdlog::error("EventLoop::handleRead() reads {} bytes instead of 8", n);
  }
}

void EventLoop::doPendingFunctors() {
  std::vector<Functor> functors;
  callingPendingFunctors_ = true;

  {
    std::lock_guard<std::mutex> lock(mutex_);
    functors.swap(pendingFunctors_);
  }

  for (const Functor& functor : functors) {
    functor();
  }
  callingPendingFunctors_ = false;
}

void EventLoop::updateChannel(Channel* channel) {
  assert(channel->ownerLoop() == this);
  assertInLoopThread();
  poller_->updateChannel(channel);
}

void EventLoop::removeChannel(Channel* channel) {
  assert(channel->ownerLoop() == this);
  assertInLoopThread();
  if (eventHandling_) {
    assert(currentActiveChannel_ == channel
           || std::find(activeChannels_.begin(), activeChannels_.end(), channel)
                  == activeChannels_.end());
  }
  poller_->removeChannel(channel);
}

bool EventLoop::hasChannel(Channel* channel) {
  assert(channel->ownerLoop() == this);
  assertInLoopThread();
  return poller_->hasChannel(channel);
}

void EventLoop::abortNotInLoopThread() { spdlog::critical("EventLoop::abortNotInLoopThread"); }

void EventLoop::printActiveChannels() const {
  for (const Channel* channel : activeChannels_) {
    spdlog::trace("  {}", channel->reventsToString());
  }
}

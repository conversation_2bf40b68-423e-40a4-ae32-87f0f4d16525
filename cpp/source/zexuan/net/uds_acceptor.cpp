#include "zexuan/net/uds_acceptor.hpp"

#include <fcntl.h>
#include <spdlog/spdlog.h>
#include <unistd.h>

#include <cassert>

#include "zexuan/net/event_loop.hpp"
#include "zexuan/net/sockets_ops.hpp"

using namespace zexuan;
using namespace zexuan::net;

UdsAcceptor::UdsAcceptor(EventLoop* loop, const std::string& listenPath)
    : loop_(loop),
      listenPath_(listenPath),
      acceptSocket_(zexuan::net::sockets::createUdsNonblockingOrDie()),
      acceptChannel_(loop, acceptSocket_.fd()),
      listening_(false),
      idleFd_(::open("/dev/null", O_RDONLY | O_CLOEXEC)) {
  assert(idleFd_ >= 0);
  
  // 绑定到Unix域套接字路径
  acceptSocket_.bindUdsAddress(listenPath_);
  
  acceptChannel_.setReadCallback([this](std::chrono::system_clock::time_point) {
    handleRead(std::chrono::system_clock::now());
  });
}

UdsAcceptor::~UdsAcceptor() {
  acceptChannel_.disableAll();
  acceptChannel_.remove();
  ::close(idleFd_);
  
  // 清理Unix socket文件
  if (!listenPath_.empty()) {
    ::unlink(listenPath_.c_str());
  }
}

void UdsAcceptor::listen() {
  loop_->assertInLoopThread();
  listening_ = true;
  acceptSocket_.listen();
  acceptChannel_.enableReading();
}

void UdsAcceptor::handleRead(std::chrono::system_clock::time_point) {
  loop_->assertInLoopThread();
  std::string peerPath;
  
  // 使用UDS特定的accept方法
  int connfd = acceptSocket_.acceptUds(&peerPath);
  if (connfd >= 0) {
    if (newConnectionCallback_) {
      newConnectionCallback_(connfd, peerPath);
    } else {
      zexuan::net::sockets::close(connfd);
    }
  } else {
    spdlog::error("in UdsAcceptor::handleRead");
    // 处理文件描述符耗尽的情况
    // 这个技巧来自libev文档，由Marc Lehmann提出
    if (errno == EMFILE) {
      ::close(idleFd_);
      idleFd_ = ::accept(acceptSocket_.fd(), NULL, NULL);
      ::close(idleFd_);
      idleFd_ = ::open("/dev/null", O_RDONLY | O_CLOEXEC);
    }
  }
}

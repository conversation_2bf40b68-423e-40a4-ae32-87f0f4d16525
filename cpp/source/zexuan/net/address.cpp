#include "zexuan/net/address.hpp"

#include "spdlog/spdlog.h"

#ifdef __linux__
#  include <arpa/inet.h>
#  include <endian.h>
#  include <netdb.h>

#  include <cassert>
#  include <cstring>

namespace zexuan {
  namespace net {

    using namespace sockets;

    // 静态断言确保内存布局正确
    static_assert(sizeof(Address) >= sizeof(struct sockaddr_in6),
                  "Address is at least as large as sockaddr_in6");
    static_assert(sizeof(Address) >= sizeof(struct sockaddr_un),
                  "Address is at least as large as sockaddr_un");
    static_assert(offsetof(sockaddr_in, sin_family) == 0, "sin_family offset 0");
    static_assert(offsetof(sockaddr_in6, sin6_family) == 0, "sin6_family offset 0");
    static_assert(offsetof(sockaddr_un, sun_family) == 0, "sun_family offset 0");
    static_assert(offsetof(sockaddr_in, sin_port) == 2, "sin_port offset 2");
    static_assert(offsetof(sockaddr_in6, sin6_port) == 2, "sin6_port offset 2");

    // 与原始实现一致的常量定义
    static const in_addr_t kInaddrAny = INADDR_ANY;
    static const in_addr_t kInaddrLoopback = INADDR_LOOPBACK;

    Address::Address(uint16_t portArg, bool loopbackOnly, bool ipv6) {
      static_assert(offsetof(Address, addr6_) == 0, "addr6_ offset 0");
      static_assert(offsetof(Address, addr_) == 0, "addr_ offset 0");

      if (ipv6) {
        std::memset(&addr6_, 0, sizeof addr6_);
        addr6_.sin6_family = AF_INET6;
        in6_addr ip = loopbackOnly ? in6addr_loopback : in6addr_any;
        addr6_.sin6_addr = ip;
        addr6_.sin6_port = htobe16(portArg);
      } else {
        std::memset(&addr_, 0, sizeof addr_);
        addr_.sin_family = AF_INET;
        in_addr_t ip = loopbackOnly ? kInaddrLoopback : kInaddrAny;
        addr_.sin_addr.s_addr = htobe32(ip);
        addr_.sin_port = htobe16(portArg);
      }
    }

    Address::Address(const std::string& ip, uint16_t portArg, bool ipv6) {
      if (ipv6 || ip.find(':') != std::string::npos) {
        std::memset(&addr6_, 0, sizeof addr6_);
        sockets::fromIpPort(ip.c_str(), portArg, &addr6_);
      } else {
        std::memset(&addr_, 0, sizeof addr_);
        sockets::fromIpPort(ip.c_str(), portArg, &addr_);
      }
    }

    Address::Address(const struct sockaddr_in& addr) : addr_(addr) {}

    Address::Address(const struct sockaddr_in6& addr) : addr6_(addr) {}

    Address::Address(const std::string& path) {
      std::memset(&addrun_, 0, sizeof addrun_);
      addrun_.sun_family = AF_UNIX;
      if (path.length() >= sizeof(addrun_.sun_path)) {
        spdlog::error("Unix socket path too long: {}", path);
        return;
      }
      std::strncpy(addrun_.sun_path, path.c_str(), sizeof(addrun_.sun_path) - 1);
    }

    Address::Address(const struct sockaddr_un& addr) : addrun_(addr) {}

    sa_family_t Address::family() const {
      return addrun_.sun_family;  // union中所有成员的family字段都在相同位置
    }

    const struct sockaddr* Address::getSockAddr() const {
      return reinterpret_cast<const struct sockaddr*>(&addrun_);
    }

    socklen_t Address::getSockLen() const {
      switch (family()) {
        case AF_INET:
          return sizeof(struct sockaddr_in);
        case AF_INET6:
          return sizeof(struct sockaddr_in6);
        case AF_UNIX:
          return sizeof(struct sockaddr_un);
        default:
          return sizeof(struct sockaddr_in6);  // 默认返回最大的
      }
    }

    std::string Address::toIpPort() const {
      if (family() == AF_UNIX) {
        return toUnixPath();
      }
      char buf[64] = "";
      sockets::toIpPort(buf, sizeof buf, getSockAddr());
      return buf;
    }

    std::string Address::toIp() const {
      if (family() == AF_UNIX) {
        return toUnixPath();
      }
      char buf[64] = "";
      sockets::toIp(buf, sizeof buf, getSockAddr());
      return buf;
    }

    std::string Address::toUnixPath() const {
      if (family() == AF_UNIX) {
        return std::string(addrun_.sun_path);
      }
      return "";
    }

    uint32_t Address::ipv4NetEndian() const {
      assert(family() == AF_INET);
      return addr_.sin_addr.s_addr;
    }

    uint16_t Address::port() const {
      if (family() == AF_UNIX) {
        return 0;  // Unix域套接字没有端口概念
      }
      return be16toh(portNetEndian());
    }

    uint16_t Address::portNetEndian() const {
      if (family() == AF_UNIX) {
        return 0;  // Unix域套接字没有端口概念
      }
      return addr_.sin_port;
    }

    // 线程本地存储用于域名解析
    static __thread char t_resolveBuffer[64 * 1024];

    bool Address::resolve(const std::string& hostname, Address* out) {
      assert(out != NULL);
      struct hostent hent;
      struct hostent* he = NULL;
      int herrno = 0;
      std::memset(&hent, 0, sizeof(hent));

      int ret = gethostbyname_r(hostname.c_str(), &hent, t_resolveBuffer, sizeof t_resolveBuffer,
                                &he, &herrno);
      if (ret == 0 && he != NULL) {
        assert(he->h_addrtype == AF_INET && he->h_length == sizeof(uint32_t));
        out->addr_.sin_addr = *reinterpret_cast<struct in_addr*>(he->h_addr);
        return true;
      } else {
        if (ret) {
          spdlog::error("Address::resolve failed: {}", strerror(ret));
        }
        return false;
      }
    }

    void Address::setScopeId(uint32_t scope_id) {
      if (family() == AF_INET6) {
        addr6_.sin6_scope_id = scope_id;
      }
    }

    // 兼容性方法
    struct sockaddr_in Address::toSockAddr() const { return addr_; }

    struct sockaddr_in6 Address::toSockAddr6() const { return addr6_; }

    struct sockaddr_un Address::toSockAddrUnix() const { return addrun_; }

    Address Address::fromIpPort(const std::string& ip, uint16_t port) { return Address(ip, port); }

    Address Address::fromUnixPath(const std::string& path) { return Address(path); }

    Address Address::fromSockAddr(const struct sockaddr& addr) {
      if (addr.sa_family == AF_INET) {
        const struct sockaddr_in* addr4 = reinterpret_cast<const struct sockaddr_in*>(&addr);
        return Address(*addr4);
      } else if (addr.sa_family == AF_INET6) {
        const struct sockaddr_in6* addr6 = reinterpret_cast<const struct sockaddr_in6*>(&addr);
        return Address(*addr6);
      } else if (addr.sa_family == AF_UNIX) {
        const struct sockaddr_un* addrun = reinterpret_cast<const struct sockaddr_un*>(&addr);
        return Address(*addrun);
      } else {
        // 默认返回 IPv4 地址
        return Address(0);
      }
    }

  }  // namespace net
}  // namespace zexuan

#endif  // (__linux__)

#include "zexuan/net/socket.hpp"

#include "spdlog/spdlog.h"

#ifdef __linux__

#  include <netinet/in.h>
#  include <netinet/tcp.h>
#  include <stdio.h>  // snprintf

#  include <cstring>  // std::memset

using namespace zexuan;
using namespace zexuan::net;
using namespace zexuan::net::sockets;

Socket::~Socket() { sockets::close(sockfd_); }

bool Socket::getTcpInfo(struct tcp_info* tcpi) const {
  socklen_t len = sizeof(*tcpi);
  std::memset(tcpi, 0, len);
  return ::getsockopt(sockfd_, SOL_TCP, TCP_INFO, tcpi, &len) == 0;
}

bool Socket::getTcpInfoString(char* buf, int len) const {
  struct tcp_info tcpi;
  bool ok = getTcpInfo(&tcpi);
  if (ok) {
    snprintf(buf, len,
             "unrecovered=%u "
             "rto=%u ato=%u snd_mss=%u rcv_mss=%u "
             "lost=%u retrans=%u rtt=%u rttvar=%u "
             "sshthresh=%u cwnd=%u total_retrans=%u",
             tcpi.tcpi_retransmits,  // Number of unrecovered [RTO] timeouts
             tcpi.tcpi_rto,          // Retransmit timeout in usec
             tcpi.tcpi_ato,          // Predicted tick of soft clock in usec
             tcpi.tcpi_snd_mss, tcpi.tcpi_rcv_mss,
             tcpi.tcpi_lost,     // Lost packets
             tcpi.tcpi_retrans,  // Retransmitted packets out
             tcpi.tcpi_rtt,      // Smoothed round trip time in usec
             tcpi.tcpi_rttvar,   // Medium deviation
             tcpi.tcpi_snd_ssthresh, tcpi.tcpi_snd_cwnd,
             tcpi.tcpi_total_retrans);  // Total retransmits for entire connection
  }
  return ok;
}

void Socket::bindAddress(const Address& addr) { sockets::bindOrDie(sockfd_, addr.getSockAddr()); }

void Socket::listen() { sockets::listenOrDie(sockfd_); }

int Socket::accept(Address* peeraddr) {
  // 使用sockaddr_storage来容纳任何类型的地址
  struct sockaddr_storage addr;
  std::memset(&addr, 0, sizeof addr);
  socklen_t addrlen = sizeof(addr);

  int connfd = ::accept4(sockfd_, reinterpret_cast<struct sockaddr*>(&addr), &addrlen,
                         SOCK_NONBLOCK | SOCK_CLOEXEC);
  if (connfd >= 0) {
    if (addr.ss_family == AF_INET) {
      peeraddr->setSockAddrInet6(*reinterpret_cast<struct sockaddr_in6*>(&addr));
    } else if (addr.ss_family == AF_INET6) {
      peeraddr->setSockAddrInet6(*reinterpret_cast<struct sockaddr_in6*>(&addr));
    } else if (addr.ss_family == AF_UNIX) {
      peeraddr->setSockAddrUnix(*reinterpret_cast<struct sockaddr_un*>(&addr));
    }
  }
  return connfd;
}

void Socket::shutdownWrite() { sockets::shutdownWrite(sockfd_); }

void Socket::setTcpNoDelay(bool on) {
  int optval = on ? 1 : 0;
  ::setsockopt(sockfd_, IPPROTO_TCP, TCP_NODELAY, &optval, static_cast<socklen_t>(sizeof optval));
  // FIXME CHECK
}

void Socket::setReuseAddr(bool on) {
  int optval = on ? 1 : 0;
  ::setsockopt(sockfd_, SOL_SOCKET, SO_REUSEADDR, &optval, static_cast<socklen_t>(sizeof optval));
  // FIXME CHECK
}

void Socket::setReusePort(bool on) {
#  ifdef SO_REUSEPORT
  int optval = on ? 1 : 0;
  int ret = ::setsockopt(sockfd_, SOL_SOCKET, SO_REUSEPORT, &optval,
                         static_cast<socklen_t>(sizeof optval));
  if (ret < 0 && on) {
    spdlog::error("SO_REUSEPORT failed: {}", strerror(errno));
  }
#  else
  if (on) {
    spdlog::error("SO_REUSEPORT is not supported");
  }
#  endif
}

void Socket::setKeepAlive(bool on) {
  int optval = on ? 1 : 0;
  ::setsockopt(sockfd_, SOL_SOCKET, SO_KEEPALIVE, &optval, static_cast<socklen_t>(sizeof optval));
  // FIXME CHECK
}

// UDS特定方法实现
void Socket::bindUdsAddress(const std::string& path) {
  sockets::bindUdsOrDie(sockfd_, path.c_str());
}

int Socket::acceptUds(std::string* peerPath) {
  struct sockaddr_un addr;
  std::memset(&addr, 0, sizeof addr);
  int connfd = sockets::acceptUds(sockfd_, &addr);
  if (connfd >= 0 && peerPath) {
    *peerPath = std::string(addr.sun_path);
  }
  return connfd;
}

bool Socket::isUnixSocket() const {
  struct sockaddr_storage addr;
  socklen_t addrlen = sizeof(addr);
  if (::getsockname(sockfd_, reinterpret_cast<struct sockaddr*>(&addr), &addrlen) == 0) {
    return addr.ss_family == AF_UNIX;
  }
  return false;
}

#endif  //

# Changelog

## [1.0.2] - 2025-08-24
### Added
- bus
    - 增加了bus层，增加protocol、router、serializer、manager、server、client层

- protocol
    - protocol_service增加单独总线客户端线程

### Deleted
- protocol
    - protocol_service所有的消息生成全部删除，逻辑全部移植到tcp_bus_client中

### Fixed
- tcp_connector
    - 修复自动重连的问题

## [1.0.1] - 2025-08-22
### Added
- net
    - address、socket、socket_ops等底层架构增加了对Unix Domain Socket的支持
    - 增加了uds_acceptor、uds_connector、uds_connection、uds_server、uds_client等高层组件

### Fixed
- protocol
    - service层没有对obs-sub进行exit()，导致shared无法析构

## [1.0.0] - 2025-08-21
### Added
- base
    - obs-med-sub架构
    - 消息模型
    - 各种常用基类
- utils
    - uuid封装
    - invokeid、string_uuid、file_utils封装
- net
    - 实现事件循环与网络栈
- protocol
    - 实现网络栈103规约到本地信息的转换，分server、gateway、transform、service四层

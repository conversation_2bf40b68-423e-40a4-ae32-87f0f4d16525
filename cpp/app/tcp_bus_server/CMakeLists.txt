# TCP消息总线服务器CMakeLists.txt

# 创建可执行文件
add_executable(tcp_bus_server main.cpp)

# 链接库
target_link_libraries(tcp_bus_server
    PRIVATE
        zexuan_base
        zexuan_net
        zexuan_bus
        nlohmann_json::nlohmann_json
        spdlog::spdlog
)

# 设置C++标准
target_compile_features(tcp_bus_server PRIVATE cxx_std_17)

# 包含头文件目录
target_include_directories(tcp_bus_server
    PRIVATE
        ${CMAKE_SOURCE_DIR}/cpp/include
)

# 设置输出目录
set_target_properties(tcp_bus_server PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 安装可执行文件
install(TARGETS tcp_bus_server
    RUNTIME DESTINATION bin
    COMPONENT applications
)

# 创建主应用程序
add_executable(protocol_server main.cpp)

# 设置可执行文件输出目录
set_target_properties(protocol_server PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 链接库
target_link_libraries(protocol_server
    PRIVATE
        zexuan_base
        zexuan_utils
        zexuan_net
        zexuan_protocol
)

# 设置包含目录
target_include_directories(protocol_server
    PRIVATE
        ${CMAKE_SOURCE_DIR}/include
)
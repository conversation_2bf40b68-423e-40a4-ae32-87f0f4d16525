#include <doctest/doctest.h>
#include "zexuan/net/sockets_ops.hpp"
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>
#include <cstring>
#include <string>

using namespace zexuan::net::sockets;

TEST_SUITE("UDS Sockets Operations Tests") {
    
    TEST_CASE("UDS socket creation") {
        SUBCASE("createUdsNonblockingOrDie") {
            int sockfd = createUdsNonblockingOrDie();
            CHECK(sockfd >= 0);
            
            // 验证socket是Unix域套接字
            struct sockaddr_storage addr;
            socklen_t addrlen = sizeof(addr);
            int ret = getsockname(sockfd, reinterpret_cast<struct sockaddr*>(&addr), &addrlen);
            if (ret == 0) {
                // 新创建的socket可能还没有绑定地址，这是正常的
            }
            
            ::close(sockfd);
        }
        
        SUBCASE("createNonblockingOrDie with AF_UNIX") {
            int sockfd = createNonblockingOrDie(AF_UNIX);
            CHECK(sockfd >= 0);
            ::close(sockfd);
        }
    }
    
    TEST_CASE("UDS address conversion functions") {
        struct sockaddr_un un_addr;
        std::memset(&un_addr, 0, sizeof(un_addr));
        un_addr.sun_family = AF_UNIX;
        std::strcpy(un_addr.sun_path, "/tmp/conversion_test.sock");
        
        SUBCASE("sockaddr_cast for sockaddr_un") {
            const struct sockaddr* addr = sockaddr_cast(&un_addr);
            CHECK(addr != nullptr);
            CHECK(addr->sa_family == AF_UNIX);
            
            struct sockaddr_un* mutable_addr = &un_addr;
            struct sockaddr* mutable_sock_addr = sockaddr_cast(mutable_addr);
            CHECK(mutable_sock_addr != nullptr);
            CHECK(mutable_sock_addr->sa_family == AF_UNIX);
        }
        
        SUBCASE("sockaddr_un_cast") {
            const struct sockaddr* sock_addr = sockaddr_cast(&un_addr);
            const struct sockaddr_un* converted_back = sockaddr_un_cast(sock_addr);
            
            CHECK(converted_back != nullptr);
            CHECK(converted_back->sun_family == AF_UNIX);
            CHECK(std::string(converted_back->sun_path) == "/tmp/conversion_test.sock");
        }
    }
    
    TEST_CASE("UDS bind and connect operations") {
        const char* test_path = "/tmp/bind_test.sock";
        
        // 清理可能存在的旧文件
        unlink(test_path);
        
        SUBCASE("bindUdsOrDie") {
            int sockfd = createUdsNonblockingOrDie();
            REQUIRE(sockfd >= 0);
            
            // 绑定应该成功
            bindUdsOrDie(sockfd, test_path);
            
            // 验证绑定成功
            struct sockaddr_un addr;
            socklen_t addrlen = sizeof(addr);
            int ret = getsockname(sockfd, reinterpret_cast<struct sockaddr*>(&addr), &addrlen);
            CHECK(ret == 0);
            CHECK(addr.sun_family == AF_UNIX);
            CHECK(std::string(addr.sun_path) == test_path);
            
            ::close(sockfd);
            unlink(test_path);
        }
        
        SUBCASE("unlinkUdsPath") {
            // 创建一个文件
            int sockfd = createUdsNonblockingOrDie();
            bindUdsOrDie(sockfd, test_path);
            ::close(sockfd);
            
            // 验证文件存在
            CHECK(access(test_path, F_OK) == 0);
            
            // 删除文件
            unlinkUdsPath(test_path);
            
            // 验证文件已删除
            CHECK(access(test_path, F_OK) != 0);
        }
        
        SUBCASE("connectUds") {
            // 创建服务器socket
            int server_fd = createUdsNonblockingOrDie();
            bindUdsOrDie(server_fd, test_path);
            listenOrDie(server_fd);
            
            // 创建客户端socket
            int client_fd = createUdsNonblockingOrDie();
            
            // 连接（可能会因为非阻塞而返回EINPROGRESS，这是正常的）
            int ret = connectUds(client_fd, test_path);
            // 对于非阻塞socket，连接可能立即成功或返回EINPROGRESS
            CHECK((ret == 0 || errno == EINPROGRESS));
            
            ::close(client_fd);
            ::close(server_fd);
            unlink(test_path);
        }
    }
    
    TEST_CASE("UDS accept operations") {
        const char* test_path = "/tmp/accept_test.sock";
        unlink(test_path);
        
        SUBCASE("acceptUds basic functionality") {
            int server_fd = createUdsNonblockingOrDie();
            bindUdsOrDie(server_fd, test_path);
            listenOrDie(server_fd);
            
            // 对于非阻塞socket，accept可能会立即返回EAGAIN
            struct sockaddr_un client_addr;
            int client_fd = acceptUds(server_fd, &client_addr);
            
            // 非阻塞accept在没有连接时应该返回-1并设置errno为EAGAIN
            if (client_fd < 0) {
                CHECK(errno == EAGAIN);
            } else {
                // 如果有连接，应该是有效的文件描述符
                CHECK(client_fd >= 0);
                ::close(client_fd);
            }
            
            ::close(server_fd);
            unlink(test_path);
        }
    }
    
    TEST_CASE("Error handling") {
        SUBCASE("bindUdsOrDie with invalid path") {
            int sockfd = createUdsNonblockingOrDie();
            
            // 使用过长的路径（应该在日志中记录错误但不崩溃）
            std::string long_path(200, 'a');
            bindUdsOrDie(sockfd, long_path.c_str());
            
            ::close(sockfd);
        }
        
        SUBCASE("connectUds with non-existent path") {
            int sockfd = createUdsNonblockingOrDie();
            
            int ret = connectUds(sockfd, "/tmp/non_existent_socket.sock");
            CHECK(ret < 0);  // 应该失败
            
            ::close(sockfd);
        }
        
        SUBCASE("unlinkUdsPath with non-existent file") {
            // 删除不存在的文件应该不会崩溃
            unlinkUdsPath("/tmp/definitely_non_existent_file.sock");
            // 如果没有崩溃就算通过
            CHECK(true);
        }
    }
}

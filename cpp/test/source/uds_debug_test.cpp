#include <doctest/doctest.h>
#include "zexuan/net/sockets_ops.hpp"
#include "zexuan/net/address.hpp"
#include "zexuan/net/socket.hpp"
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>
#include <cstring>
#include <string>
#include <cerrno>

using namespace zexuan::net::sockets;
using namespace zexuan::net;

TEST_SUITE("UDS Debug Tests") {
    
    TEST_CASE("Direct UDS bind test") {
        const char* test_path = "/tmp/direct_test.sock";
        unlink(test_path);
        
        // 创建socket
        int sockfd = createUdsNonblockingOrDie();
        CHECK(sockfd >= 0);
        
        // 绑定
        bindUdsOrDie(sockfd, test_path);
        
        // 验证绑定
        struct sockaddr_un bound_addr;
        socklen_t addrlen = sizeof(bound_addr);
        std::memset(&bound_addr, 0, sizeof(bound_addr));
        
        int ret = getsockname(sockfd, reinterpret_cast<struct sockaddr*>(&bound_addr), &addrlen);
        
        INFO("getsockname return: " << ret);
        if (ret == 0) {
            INFO("Address length: " << addrlen);
            INFO("Family: " << bound_addr.sun_family);
            INFO("Path: '" << bound_addr.sun_path << "'");
            INFO("Expected: '" << test_path << "'");
            INFO("Path length: " << strlen(bound_addr.sun_path));
        } else {
            INFO("getsockname failed: " << strerror(errno));
        }
        
        CHECK(ret == 0);
        if (ret == 0) {
            CHECK(bound_addr.sun_family == AF_UNIX);
            CHECK(std::string(bound_addr.sun_path) == test_path);
        }
        
        ::close(sockfd);
        unlink(test_path);
    }
    
    TEST_CASE("Address class UDS test") {
        const std::string test_path = "/tmp/addr_class_test.sock";
        unlink(test_path.c_str());
        
        // 使用Address类
        Address addr(test_path);
        
        INFO("Address family: " << addr.family());
        INFO("Address path: '" << addr.toUnixPath() << "'");
        INFO("Expected path: '" << test_path << "'");
        
        CHECK(addr.family() == AF_UNIX);
        CHECK(addr.toUnixPath() == test_path);
        
        // 创建socket并绑定
        int sockfd = createUdsNonblockingOrDie();
        CHECK(sockfd >= 0);
        
        // 使用通用的bindOrDie
        bindOrDie(sockfd, addr.getSockAddr());
        
        // 验证绑定
        struct sockaddr_un bound_addr;
        socklen_t addrlen = sizeof(bound_addr);
        std::memset(&bound_addr, 0, sizeof(bound_addr));
        
        int ret = getsockname(sockfd, reinterpret_cast<struct sockaddr*>(&bound_addr), &addrlen);
        
        INFO("getsockname return: " << ret);
        if (ret == 0) {
            INFO("Address length: " << addrlen);
            INFO("Family: " << bound_addr.sun_family);
            INFO("Path: '" << bound_addr.sun_path << "'");
            INFO("Expected: '" << test_path << "'");
        } else {
            INFO("getsockname failed: " << strerror(errno));
        }
        
        CHECK(ret == 0);
        if (ret == 0) {
            CHECK(bound_addr.sun_family == AF_UNIX);
            CHECK(std::string(bound_addr.sun_path) == test_path);
        }
        
        ::close(sockfd);
        unlink(test_path.c_str());
    }

    TEST_CASE("Socket class bind test") {
        const std::string test_path = "/tmp/socket_bind_test.sock";
        unlink(test_path.c_str());

        // 使用Socket类
        int sockfd = createUdsNonblockingOrDie();
        Socket socket(sockfd);
        Address addr(test_path);

        INFO("Before bind - Address family: " << addr.family());
        INFO("Before bind - Address path: '" << addr.toUnixPath() << "'");

        // 绑定
        socket.bindAddress(addr);

        // 验证绑定
        struct sockaddr_un bound_addr;
        socklen_t addrlen = sizeof(bound_addr);
        std::memset(&bound_addr, 0, sizeof(bound_addr));

        int ret = getsockname(sockfd, reinterpret_cast<struct sockaddr*>(&bound_addr), &addrlen);

        INFO("getsockname return: " << ret);
        if (ret == 0) {
            INFO("Address length: " << addrlen);
            INFO("Family: " << bound_addr.sun_family);
            INFO("Path: '" << bound_addr.sun_path << "'");
            INFO("Expected: '" << test_path << "'");
            INFO("Path length: " << strlen(bound_addr.sun_path));
        } else {
            INFO("getsockname failed: " << strerror(errno));
        }

        CHECK(ret == 0);
        if (ret == 0) {
            CHECK(bound_addr.sun_family == AF_UNIX);
            CHECK(std::string(bound_addr.sun_path) == test_path);
        }

        unlink(test_path.c_str());
    }

    TEST_CASE("Reproduce integration test failure") {
        const std::string socket_path = "/tmp/error_test.sock";
        unlink(socket_path.c_str());

        // 第一个socket绑定
        {
            int fd1 = createUdsNonblockingOrDie();
            Socket socket1(fd1);
            Address addr1(socket_path);

            INFO("First socket - Address family: " << addr1.family());
            INFO("First socket - Address path: '" << addr1.toUnixPath() << "'");

            socket1.bindAddress(addr1);

            // 验证第一个socket绑定
            struct sockaddr_un bound_addr1;
            socklen_t addrlen1 = sizeof(bound_addr1);
            std::memset(&bound_addr1, 0, sizeof(bound_addr1));
            int ret1 = getsockname(fd1, reinterpret_cast<struct sockaddr*>(&bound_addr1), &addrlen1);

            INFO("First socket getsockname return: " << ret1);
            if (ret1 == 0) {
                INFO("First socket path: '" << bound_addr1.sun_path << "'");
            }

            INFO("First socket bound successfully");
            // socket1在作用域结束时自动关闭
        }

        bool file_exists = (access(socket_path.c_str(), F_OK) == 0);
        INFO("First socket closed, file exists: " << (file_exists ? "yes" : "no"));

        // 第二个socket尝试绑定到同一路径
        int fd2 = createUdsNonblockingOrDie();
        Socket socket2(fd2);
        Address addr2(socket_path);

        INFO("Second socket - Address family: " << addr2.family());
        INFO("Second socket - Address path: '" << addr2.toUnixPath() << "'");
        INFO("About to bind second socket");

        socket2.bindAddress(addr2);
        INFO("Second socket bound");

        // 验证第二个socket成功绑定
        struct sockaddr_un bound_addr;
        socklen_t addrlen = sizeof(bound_addr);
        std::memset(&bound_addr, 0, sizeof(bound_addr));

        int ret = getsockname(fd2, reinterpret_cast<struct sockaddr*>(&bound_addr), &addrlen);

        INFO("getsockname return: " << ret);
        if (ret == 0) {
            INFO("Address length: " << addrlen);
            INFO("Family: " << bound_addr.sun_family);
            INFO("Path: '" << bound_addr.sun_path << "'");
            INFO("Expected: '" << socket_path << "'");
            INFO("Path length: " << strlen(bound_addr.sun_path));

            // 检查文件是否存在
            bool file_exists_after = (access(socket_path.c_str(), F_OK) == 0);
            INFO("Socket file exists: " << (file_exists_after ? "yes" : "no"));
        } else {
            INFO("getsockname failed: " << strerror(errno));
        }

        CHECK(ret == 0);
        if (ret == 0) {
            CHECK(bound_addr.sun_family == AF_UNIX);
            CHECK(std::string(bound_addr.sun_path) == socket_path);
        }

        unlink(socket_path.c_str());
    }
}

#include <doctest/doctest.h>
#include "zexuan/net/socket.hpp"
#include "zexuan/net/address.hpp"
#include "zexuan/net/sockets_ops.hpp"
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>
#include <cstring>

using namespace zexuan::net;

TEST_SUITE("UDS Socket Tests") {
    
    TEST_CASE("Socket UDS construction and basic operations") {
        SUBCASE("Create UDS socket") {
            int sockfd = sockets::createUdsNonblockingOrDie();
            Socket socket(sockfd);
            
            CHECK(socket.fd() == sockfd);
            CHECK(socket.isUnixSocket() == true);
        }
        
        SUBCASE("Create regular TCP socket") {
            int sockfd = sockets::createNonblockingOrDie(AF_INET);
            Socket socket(sockfd);
            
            CHECK(socket.fd() == sockfd);
            CHECK(socket.isUnixSocket() == false);
        }
    }
    
    TEST_CASE("UDS bind operations") {
        const std::string test_path = "/tmp/sock_bind.sock";
        
        // 清理可能存在的旧文件
        unlink(test_path.c_str());
        
        SUBCASE("bindUdsAddress") {
            int sockfd = sockets::createUdsNonblockingOrDie();
            Socket socket(sockfd);
            
            socket.bindUdsAddress(test_path);
            
            // 验证绑定成功
            struct sockaddr_un addr;
            socklen_t addrlen = sizeof(addr);
            int ret = getsockname(sockfd, reinterpret_cast<struct sockaddr*>(&addr), &addrlen);
            CHECK(ret == 0);
            CHECK(addr.sun_family == AF_UNIX);
            CHECK(std::string(addr.sun_path) == test_path);
            
            unlink(test_path.c_str());
        }
        
        SUBCASE("bindAddress with Unix Address") {
            int sockfd = sockets::createUdsNonblockingOrDie();
            Socket socket(sockfd);
            Address unix_addr(test_path);
            
            socket.bindAddress(unix_addr);
            
            // 验证绑定成功
            struct sockaddr_un addr;
            socklen_t addrlen = sizeof(addr);
            int ret = getsockname(sockfd, reinterpret_cast<struct sockaddr*>(&addr), &addrlen);
            CHECK(ret == 0);
            CHECK(addr.sun_family == AF_UNIX);
            CHECK(std::string(addr.sun_path) == test_path);
            
            unlink(test_path.c_str());
        }
    }
    
    TEST_CASE("UDS accept operations") {
        const std::string test_path = "/tmp/sock_accept.sock";
        unlink(test_path.c_str());
        
        SUBCASE("acceptUds") {
            int sockfd = sockets::createUdsNonblockingOrDie();
            Socket socket(sockfd);
            
            socket.bindUdsAddress(test_path);
            socket.listen();
            
            std::string peer_path;
            int client_fd = socket.acceptUds(&peer_path);
            
            // 非阻塞accept在没有连接时应该返回-1
            if (client_fd < 0) {
                CHECK(errno == EAGAIN);
            } else {
                CHECK(client_fd >= 0);
                ::close(client_fd);
            }
            
            unlink(test_path.c_str());
        }
        
        SUBCASE("accept with Unix Address") {
            int sockfd = sockets::createUdsNonblockingOrDie();
            Socket socket(sockfd);
            Address unix_addr(test_path);
            
            socket.bindAddress(unix_addr);
            socket.listen();
            
            Address peer_addr;
            int client_fd = socket.accept(&peer_addr);
            
            // 非阻塞accept在没有连接时应该返回-1
            if (client_fd < 0) {
                CHECK(errno == EAGAIN);
            } else {
                CHECK(client_fd >= 0);
                CHECK(peer_addr.family() == AF_UNIX);
                ::close(client_fd);
            }
            
            unlink(test_path.c_str());
        }
    }
    
    TEST_CASE("Mixed socket type operations") {
        SUBCASE("TCP socket operations still work") {
            int tcp_sockfd = sockets::createNonblockingOrDie(AF_INET);
            Socket tcp_socket(tcp_sockfd);
            
            CHECK(tcp_socket.isUnixSocket() == false);
            
            Address tcp_addr("127.0.0.1", 0);  // 端口0让系统分配
            tcp_socket.bindAddress(tcp_addr);
            tcp_socket.listen();
            
            // TCP特定操作应该仍然工作
            tcp_socket.setReuseAddr(true);
            tcp_socket.setTcpNoDelay(true);
        }
        
        SUBCASE("UDS socket doesn't support TCP-specific operations") {
            int uds_sockfd = sockets::createUdsNonblockingOrDie();
            Socket uds_socket(uds_sockfd);
            
            CHECK(uds_socket.isUnixSocket() == true);
            
            // TCP特定操作对UDS socket可能无效，但不应该崩溃
            // 这些调用可能会失败，但应该优雅地处理
            uds_socket.setTcpNoDelay(true);  // 对UDS无效，但不应崩溃
        }
    }
    
    TEST_CASE("Socket lifecycle management") {
        const std::string test_path = "/tmp/lifecycle.sock";
        unlink(test_path.c_str());
        
        SUBCASE("Socket destructor cleanup") {
            int sockfd;
            {
                sockfd = sockets::createUdsNonblockingOrDie();
                Socket socket(sockfd);
                socket.bindUdsAddress(test_path);
                
                // socket在作用域结束时应该自动关闭文件描述符
            }
            
            // 验证文件描述符已关闭（这个测试可能不够可靠，因为fd可能被重用）
            // 但至少验证socket文件仍然存在
            CHECK(access(test_path.c_str(), F_OK) == 0);
            
            unlink(test_path.c_str());
        }
    }
    
    TEST_CASE("Error handling") {
        SUBCASE("bindUdsAddress with invalid path") {
            int sockfd = sockets::createUdsNonblockingOrDie();
            Socket socket(sockfd);
            
            // 使用过长的路径
            std::string long_path(200, 'a');
            socket.bindUdsAddress(long_path);
            
            // 应该不会崩溃，错误应该在日志中记录
            CHECK(true);
        }
        
        SUBCASE("Operations on closed socket") {
            int sockfd = sockets::createUdsNonblockingOrDie();
            ::close(sockfd);  // 手动关闭
            
            Socket socket(sockfd);
            
            // 对已关闭的socket进行操作应该失败但不崩溃
            std::string peer_path;
            int result = socket.acceptUds(&peer_path);
            CHECK(result < 0);  // 应该失败
        }
    }
}

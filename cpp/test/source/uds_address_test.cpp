#include <doctest/doctest.h>
#include "zexuan/net/address.hpp"
#include <sys/socket.h>
#include <sys/un.h>
#include <cstring>

using namespace zexuan::net;

TEST_SUITE("UDS Address Tests") {
    
    TEST_CASE("Unix domain socket address construction") {
        SUBCASE("Construct from path") {
            std::string path = "/tmp/test.sock";
            Address addr(path);
            
            CHECK(addr.family() == AF_UNIX);
            CHECK(addr.isUnixSocket() == true);
            CHECK(addr.toUnixPath() == path);
            CHECK(addr.port() == 0);  // Unix socket没有端口概念
        }
        
        SUBCASE("Construct from sockaddr_un") {
            struct sockaddr_un un_addr;
            std::memset(&un_addr, 0, sizeof(un_addr));
            un_addr.sun_family = AF_UNIX;
            std::strcpy(un_addr.sun_path, "/tmp/test2.sock");
            
            Address addr(un_addr);
            
            CHECK(addr.family() == AF_UNIX);
            CHECK(addr.isUnixSocket() == true);
            CHECK(addr.toUnixPath() == "/tmp/test2.sock");
        }
        
        SUBCASE("Long path handling") {
            // 创建一个超长路径
            std::string long_path(200, 'a');
            long_path = "/tmp/" + long_path + ".sock";
            
            Address addr(long_path);
            // 应该能处理长路径（可能截断或报错，但不应该崩溃）
            CHECK(addr.family() == AF_UNIX);
        }
    }
    
    TEST_CASE("Address conversion methods") {
        std::string path = "/tmp/conversion_test.sock";
        Address addr(path);
        
        SUBCASE("getSockAddr and getSockLen") {
            const struct sockaddr* sock_addr = addr.getSockAddr();
            CHECK(sock_addr != nullptr);
            CHECK(sock_addr->sa_family == AF_UNIX);
            
            socklen_t len = addr.getSockLen();
            CHECK(len == sizeof(struct sockaddr_un));
        }
        
        SUBCASE("toSockAddrUnix") {
            struct sockaddr_un un_addr = addr.toSockAddrUnix();
            CHECK(un_addr.sun_family == AF_UNIX);
            CHECK(std::string(un_addr.sun_path) == path);
        }
        
        SUBCASE("toIp and toIpPort for Unix socket") {
            // Unix socket的IP相关方法应该返回路径
            CHECK(addr.toIp() == path);
            CHECK(addr.toIpPort() == path);
        }
    }
    
    TEST_CASE("Static factory methods") {
        SUBCASE("fromUnixPath") {
            std::string path = "/tmp/factory_test.sock";
            Address addr = Address::fromUnixPath(path);
            
            CHECK(addr.family() == AF_UNIX);
            CHECK(addr.toUnixPath() == path);
        }
        
        SUBCASE("fromSockAddr with Unix socket") {
            struct sockaddr_un un_addr;
            std::memset(&un_addr, 0, sizeof(un_addr));
            un_addr.sun_family = AF_UNIX;
            std::strcpy(un_addr.sun_path, "/tmp/fromsockaddr_test.sock");
            
            Address addr = Address::fromSockAddr(
                *reinterpret_cast<struct sockaddr*>(&un_addr));
            
            CHECK(addr.family() == AF_UNIX);
            CHECK(addr.toUnixPath() == "/tmp/fromsockaddr_test.sock");
        }
    }
    
    TEST_CASE("Mixed address type handling") {
        SUBCASE("IPv4 address still works") {
            Address ipv4_addr("127.0.0.1", 8080);
            CHECK(ipv4_addr.family() == AF_INET);
            CHECK(ipv4_addr.isUnixSocket() == false);
            CHECK(ipv4_addr.port() == 8080);
        }
        
        SUBCASE("IPv6 address still works") {
            Address ipv6_addr("::1", 8080, true);
            CHECK(ipv6_addr.family() == AF_INET6);
            CHECK(ipv6_addr.isUnixSocket() == false);
            CHECK(ipv6_addr.port() == 8080);
        }
        
        SUBCASE("Unix socket is different from IP addresses") {
            Address unix_addr("/tmp/mixed_test.sock");
            Address ipv4_addr("127.0.0.1", 8080);
            
            CHECK(unix_addr.family() != ipv4_addr.family());
            CHECK(unix_addr.isUnixSocket() == true);
            CHECK(ipv4_addr.isUnixSocket() == false);
        }
    }
}

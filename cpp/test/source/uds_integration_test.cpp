#include <doctest/doctest.h>
#include "zexuan/net/address.hpp"
#include "zexuan/net/socket.hpp"
#include "zexuan/net/sockets_ops.hpp"
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>
#include <thread>
#include <chrono>
#include <cstring>

using namespace zexuan::net;

TEST_SUITE("UDS Integration Tests") {
    
    TEST_CASE("Complete UDS client-server communication") {
        const std::string socket_path = "/tmp/integ_test.sock";
        unlink(socket_path.c_str());
        
        SUBCASE("Basic server-client setup") {
            // 创建服务器
            int server_fd = sockets::createUdsNonblockingOrDie();
            Socket server_socket(server_fd);
            Address server_addr(socket_path);
            
            server_socket.bindAddress(server_addr);
            server_socket.listen();
            
            // 验证服务器设置
            CHECK(server_socket.isUnixSocket() == true);
            CHECK(access(socket_path.c_str(), F_OK) == 0);
            
            // 创建客户端
            int client_fd = sockets::createUdsNonblockingOrDie();
            Socket client_socket(client_fd);
            
            CHECK(client_socket.isUnixSocket() == true);
            
            // 尝试连接（非阻塞可能立即返回EINPROGRESS）
            Address client_addr(socket_path);
            int connect_result = sockets::connect(client_fd, client_addr.getSockAddr());
            
            // 对于Unix域套接字，连接通常会立即成功或失败
            if (connect_result == 0) {
                // 连接成功，尝试accept
                Address peer_addr;
                int accepted_fd = server_socket.accept(&peer_addr);
                
                if (accepted_fd >= 0) {
                    CHECK(peer_addr.family() == AF_UNIX);
                    ::close(accepted_fd);
                }
            }
            
            unlink(socket_path.c_str());
        }
    }
    
    TEST_CASE("Address consistency across operations") {
        const std::string socket_path = "/tmp/addr_test.sock";
        unlink(socket_path.c_str());
        
        SUBCASE("Address information preservation") {
            // 创建Address对象
            Address original_addr(socket_path);
            CHECK(original_addr.family() == AF_UNIX);
            CHECK(original_addr.toUnixPath() == socket_path);
            CHECK(original_addr.isUnixSocket() == true);
            
            // 通过Socket使用Address
            int sockfd = sockets::createUdsNonblockingOrDie();
            Socket socket(sockfd);
            socket.bindAddress(original_addr);
            
            // 验证绑定后的地址信息
            struct sockaddr_un bound_addr;
            socklen_t addrlen = sizeof(bound_addr);
            int ret = getsockname(sockfd, reinterpret_cast<struct sockaddr*>(&bound_addr), &addrlen);
            CHECK(ret == 0);
            CHECK(bound_addr.sun_family == AF_UNIX);
            CHECK(std::string(bound_addr.sun_path) == socket_path);
            
            // 通过fromSockAddr重建Address
            Address reconstructed_addr = Address::fromSockAddr(
                *reinterpret_cast<struct sockaddr*>(&bound_addr));
            
            CHECK(reconstructed_addr.family() == AF_UNIX);
            CHECK(reconstructed_addr.toUnixPath() == socket_path);
            CHECK(reconstructed_addr.isUnixSocket() == true);
            
            unlink(socket_path.c_str());
        }
    }
    
    TEST_CASE("Socket operations with different address types") {
        SUBCASE("Mixed address type handling") {
            // Unix域套接字
            const std::string unix_path = "/tmp/mixed.sock";
            unlink(unix_path.c_str());
            
            int unix_fd = sockets::createUdsNonblockingOrDie();
            Socket unix_socket(unix_fd);
            Address unix_addr(unix_path);
            
            unix_socket.bindAddress(unix_addr);
            CHECK(unix_socket.isUnixSocket() == true);
            
            // TCP套接字
            int tcp_fd = sockets::createNonblockingOrDie(AF_INET);
            Socket tcp_socket(tcp_fd);
            Address tcp_addr("127.0.0.1", 0);
            
            tcp_socket.bindAddress(tcp_addr);
            CHECK(tcp_socket.isUnixSocket() == false);
            
            // 验证两种socket类型的区别
            CHECK(unix_addr.family() != tcp_addr.family());
            CHECK(unix_addr.isUnixSocket() == true);
            CHECK(tcp_addr.isUnixSocket() == false);
            
            unlink(unix_path.c_str());
        }
    }
    
    TEST_CASE("Error scenarios and recovery") {
        const std::string socket_path = "/tmp/error_test.sock";
        
        SUBCASE("Bind to existing socket file") {
            unlink(socket_path.c_str());

            // 第一个socket绑定
            {
                int fd1 = sockets::createUdsNonblockingOrDie();
                Socket socket1(fd1);
                Address addr1(socket_path);
                socket1.bindAddress(addr1);

                // 验证第一个socket绑定成功
                struct sockaddr_un bound_addr1;
                socklen_t addrlen1 = sizeof(bound_addr1);
                int ret1 = getsockname(fd1, reinterpret_cast<struct sockaddr*>(&bound_addr1), &addrlen1);
                CHECK(ret1 == 0);
                CHECK(std::string(bound_addr1.sun_path) == socket_path);

                // socket1在作用域结束时自动关闭
            }

            // 第二个socket尝试绑定到同一路径
            int fd2 = sockets::createUdsNonblockingOrDie();
            Socket socket2(fd2);
            Address addr2(socket_path);

            // 这应该成功，因为bindUdsOrDie会先unlink旧文件
            socket2.bindAddress(addr2);

            // 验证第二个socket成功绑定
            struct sockaddr_un bound_addr;
            socklen_t addrlen = sizeof(bound_addr);
            std::memset(&bound_addr, 0, sizeof(bound_addr));
            int ret = getsockname(fd2, reinterpret_cast<struct sockaddr*>(&bound_addr), &addrlen);

            // 添加调试信息
            if (ret != 0) {
                INFO("getsockname failed with errno: " << errno << " (" << strerror(errno) << ")");
            } else {
                INFO("getsockname returned length: " << addrlen);
                INFO("bound_addr.sun_family: " << bound_addr.sun_family);
                INFO("bound_addr.sun_path: '" << bound_addr.sun_path << "'");
                INFO("expected path: '" << socket_path << "'");
                INFO("path length: " << strlen(bound_addr.sun_path));
            }

            CHECK(ret == 0);
            if (ret == 0) {
                CHECK(std::string(bound_addr.sun_path) == socket_path);
            }

            unlink(socket_path.c_str());
        }
        
        SUBCASE("Connect to non-existent socket") {
            const std::string non_existent_path = "/tmp/definitely_does_not_exist.sock";
            unlink(non_existent_path.c_str());
            
            int client_fd = sockets::createUdsNonblockingOrDie();
            Socket client_socket(client_fd);
            
            int result = sockets::connectUds(client_fd, non_existent_path.c_str());
            CHECK(result < 0);  // 应该失败
            CHECK(errno == ENOENT);  // 文件不存在
        }
    }
    
    TEST_CASE("Performance and resource management") {
        SUBCASE("Multiple socket creation and cleanup") {
            const int num_sockets = 10;
            std::vector<std::unique_ptr<Socket>> sockets;
            std::vector<std::string> paths;
            
            // 创建多个UDS socket
            for (int i = 0; i < num_sockets; ++i) {
                std::string path = "/tmp/multi_" + std::to_string(i) + ".sock";
                paths.push_back(path);
                unlink(path.c_str());
                
                int sockfd = sockets::createUdsNonblockingOrDie();
                auto socket = std::make_unique<Socket>(sockfd);
                Address addr(path);
                
                socket->bindAddress(addr);
                CHECK(socket->isUnixSocket() == true);
                
                sockets.push_back(std::move(socket));
            }
            
            // 验证所有socket都正确创建
            CHECK(sockets.size() == num_sockets);
            
            // 清理
            sockets.clear();
            for (const auto& path : paths) {
                unlink(path.c_str());
            }
        }
    }
}

# 动态绑定
```
row r;
sql << "select * from some_table", into(r);

std::ostringstream doc;
doc << "<row>" << std::endl;
for(std::size_t i = 0; i != r.size(); ++i)
{
    const column_properties & props = r.get_properties(i);

    doc << '<' << props.get_name() << '>';

    switch(props.get_db_type())
    {
    case db_string:
        doc << r.get<std::string>(i);
        break;
    case db_double:
        doc << r.get<double>(i);
        break;
    case db_int8:
        doc << r.get<int8_t>(i);
        break;
    case db_uint8:
        doc << r.get<uint8_t>(i);
        break;
    case db_int16:
        doc << r.get<int16_t>(i);
        break;
    case db_uint16:
        doc << r.get<uint16_t>(i);
        break;
    case db_int32:
        doc << r.get<int32_t>(i);
        break;
    case db_uint32:
        doc << r.get<uint32_t>(i);
        break;
    case db_int64:
        doc << r.get<int64_t>(i);
        break;
    case db_uint64:
        doc << r.get<uint64_t>(i);
        break;
    case db_date:
        std::tm when = r.get<std::tm>(i);
        doc << asctime(&when);
        break;
    }

    doc << "</" << props.get_name() << '>' << std::endl;
}
doc << "</row>";
```
## 对象关系映射

```
struct Person
{
    int id;
    std::string firstName;
    std::string lastName;
    std::string gender;
};

namespace soci
{
    template<>
    struct type_conversion<Person>
    {
        typedef values base_type;

        static void from_base(values const & v, indicator /* ind */, Person & p)
        {
            p.id = v.get<int>("ID");
            p.firstName = v.get<std::string>("FIRST_NAME");
            p.lastName = v.get<std::string>("LAST_NAME");

            // p.gender will be set to the default value "unknown"
            // when the column is null:
            p.gender = v.get<std::string>("GENDER", "unknown");

            // alternatively, the indicator can be tested directly:
            // if (v.indicator("GENDER") == i_null)
            // {
            //     p.gender = "unknown";
            // }
            // else
            // {
            //     p.gender = v.get<std::string>("GENDER");
            // }
        }

        static void to_base(const Person & p, values & v, indicator & ind)
        {
            v.set("ID", p.id);
            v.set("FIRST_NAME", p.firstName);
            v.set("LAST_NAME", p.lastName);
            v.set("GENDER", p.gender, p.gender.empty() ? i_null : i_ok);
            ind = i_ok;
        }
    };
}



session sql(oracle, "service=db1 user=scott password=tiger");

Person p;
p.id = 1;
p.lastName = "Smith";
p.firstName = "Pat";
sql << "insert into person(id, first_name, last_name) "
        "values(:ID, :FIRST_NAME, :LAST_NAME)", use(p);

Person p1;
sql << "select * from person", into(p1);
assert(p1.id == 1);
assert(p1.firstName + p.lastName == "PatSmith");
assert(p1.gender == "unknown");

p.firstName = "Patricia";
sql << "update person set first_name = :FIRST_NAME "
        "where id = :ID", use(p);
```


# tips
插入用
Bulk operations  批量操作
When using some databases, further performance improvements may be possible by having the underlying database API group operations together to reduce network roundtrips. SOCI makes such bulk operations possible by supporting std::vector based types.
使用某些数据库时，可以通过将底层数据库 API 的操作分组以减少网络往返次数来进一步提升性能。SOCI 通过支持基于 std::vector 的类型来实现此类批量操作。

The following example presents how to insert 100 records in 4 batches. It is also important to note, that size of vector remains equal in every batch interaction. This ensures vector is not reallocated and, what's crucial for the bulk trick, new data should be pushed to the vector before every call to statement::execute:
以下示例演示了如何分 4 个批次插入 100 条记录。同样需要注意的是，向量的大小在每次批次操作中都保持相等。这确保了向量不会被重新分配，而且对于批量操作来说至关重要的是，每次调用 statement::execute 之前都应该将新数据推送到向量中：

// Example 3.
void fill_ids(std::vector<int>& ids)
{
    for (std::size_t i = 0; i < ids.size(); ++i)
        ids[i] = i; // mimics source of a new ID
}

const int BATCH_SIZE = 25;
std::vector<int> ids(BATCH_SIZE);

statement st = (sql.prepare << "insert into numbers(value) values(:val)", use(ids));
for (int i = 0; i != 4; ++i)
{
    fill_ids(ids);
    st.execute(true);
}


查询用
int i;
statement st = (sql.prepare << "select value from numbers order by value", into(i));
st.execute();
while (st.fetch())
{
    cout << i << '\n';
}
### 复杂就用rowset
// person table has 4 columns

rowset<row> rs = (sql.prepare << "select id, firstname, lastname, gender from person");

// iteration through the resultset:
for (rowset<row>::const_iterator it = rs.begin(); it != rs.end(); ++it)
{
    row const& row = *it;

    // dynamic data extraction from each row:
    cout << "Id: " << row.get<int>(0) << '\n'
            << "Name: " << row.get<string>(1) << " " << row.get<string>(2) << '\n'
            << "Gender: " << row.get<string>(3) << endl;
}
如果需要使用带有 rowset 的 Core 接口，以下示例显示如何操作：

row r;

statement st(sql);
st.alloc();
st.prepare("select values from numbers");
st.define_and_bind();

// after define_and_bind and before execute
st.exchange_for_rowset(into(r));

st.execute(false);

rowset_iterator<row> it(st, r);
rowset_iterator<row> end;
for (; it != end; ++it) {
    // ... access *it
}
# BLOBs
Binary (BLOBs)  二进制（BLOB）
The SOCI library provides also an interface for basic operations on large objects (BLOBs - Binary Large OBjects).
SOCI 库还提供了对大对象（BLOB - 二进制大对象）进行基本操作的接口。

Selecting a BLOB from a table:
从表中选择一个 BLOB：

blob b(sql); // sql is a session object
sql << "select mp3 from mymusic where id = 123", into(b);
Inserting a BLOB from a table:
从表中插入 BLOB：

blob b(sql); // sql is a session object
b.write_from_start(data.data(), data.size()); // data is e.g. a std::vector< char >
sql << "insert into mymusic mp3, id VALUES(:mp3, 124)", use(b);
The following functions are provided in the blob interface, mimicking the file-like operations:
blob 接口中提供了以下函数，模拟文件类操作：

std::size_t get_len();
std::size_t read_from_start(char * buf, std::size_t toRead, std::size_t offset = 0);
std::size_t write_from_start(const char * buf, std::size_t toWrite, std::size_t offset = 0);
std::size_t append(char const *buf, std::size_t toWrite);
void trim(std::size_t newLen);
The offset parameter is always counted from the beginning of the BLOB's data. read_from_start and write_from_start and append return the amount of read or written bytes.
offset 参数始终从 BLOB 数据的开头开始计算。read_from_start 和 write_from_start append read_from_start 读取或写入的字节数。

Notes  笔记
As with empty files (but contrary to e.g. std::vector) reading from the beginning of an empty blob is a valid operation (effectively a no-op), e.g. it won't throw or error otherwise.
与空文件一样（但与 std::vector 相反），从空 blob 的开头读取是一个有效操作（实际上是无操作），例如，否则它不会抛出或出错。
It is possible to default-construct blob objects. Default-constructed blobs are in an invalid state and must not be accessed other than to query their validity (is_valid()) or to initialize them (initialize(session &session)) in order to bring them into a valid state.
可以默认构造 blob 对象。默认构造的 blob 处于无效状态，除查询其有效性 ( is_valid() ) 或初始化 ( initialize(session &session) ) 使其处于有效状态外，不得对其进行访问。
Portability  可移植性
The way to define BLOB table columns and create or destroy BLOB objects in the database varies between different database engines. Please see the SQL documentation relevant for the given server to learn how this is actually done. The test programs provided with the SOCI library can be also a simple source of full working examples.
定义 BLOB 表列以及在数据库中创建或销毁 BLOB 对象的方式因数据库引擎而异。请参阅与特定服务器相关的 SQL 文档，了解实际操作方法。SOCI 库提供的测试程序也可以作为完整工作示例的简单来源。
BLOBs are currently not implemented for all supported backends. Backends missing an implementation are ODBC and DB2.
目前并非所有支持的后端都支持 BLOB。缺少支持的后端包括 ODBC 和 DB2 。
The plain read(...) and write(...) functions use offsets in a backend-specific format (some start at zero, some at one). They are retained only for backwards compatibility. Don't use them in new code!
普通的 read(...) 和 write(...) 函数使用特定于后端格式的偏移量（有些从零开始，有些从一开始）。保留它们只是为了向后兼容。请勿在新代码中使用它们！
Some backends (e.g. PostgreSQL) support BLOBs only while a transaction is active. Using a soci::blob object outside of a transaction in these cases is undefined behavior. In order to write portable code, you should always ensure to start a transaction before working with BLOBs and end it only after you are done with the BLOB object.
某些后端（例如 PostgreSQL）仅在事务处于活动状态时支持 BLOB。在这种情况下，在事务之外使用 soci::blob 对象是未定义的行为。为了编写可移植的代码，应始终确保在处理 BLOB 之前启动事务，并在处理完 BLOB 对象后才结束事务。
Long strings and XML
长字符串和 XML
The SOCI library recognizes the fact that long string values are not handled portably and in some databases long string values need to be stored as a different data type. Similar concerns relate to the use of XML values, which are essentially strings at the application level, but can be stored in special database-level field types.
SOCI 库认识到长字符串值无法移植，在某些数据库中，长字符串值需要存储为其他数据类型。类似的问题也与 XML 值的使用有关，XML 值在应用程序级别本质上是字符串，但可以存储在特殊的数据库级别字段类型中。

In order to facilitate handling of long strings and XML values the following wrapper types are defined:
为了方便处理长字符串和 XML 值，定义了以下包装器类型：

struct xml_type
{
    std::string value;
};

struct long_string
{
    std::string value;
};
Values of these wrapper types can be used with into and use elements with the database target type that is specifically intended to handle XML and long strings data types.
这些包装器类型的值可以与 into 元素一起使用，并 use 专门用于处理 XML 和长字符串数据类型的数据库目标类型的元素。

For Oracle, these database-side types are, respectively:
对于 Oracle，这些数据库端类型分别是：

XMLType,  XMLType ，
CLOB
For PostgreSQL, these types are:
对于 PostgreSQL，这些类型是：

XML
text
For Firebird, there is no special XML support, but BLOB SUB_TYPE TEXT can be used for storing it, as well as long strings.
对于 Firebird，没有特殊的 XML 支持，但可以使用 BLOB SUB_TYPE TEXT 来存储它，以及长字符串。

For ODBC backend, these types depend on the type of the database connected to. In particularly important special case of Microsoft SQL Server, these types are:
对于 ODBC 后端，这些类型取决于所连接的数据库类型。在 Microsoft SQL Server 这一特别重要的特殊情况下，这些类型包括：

xml
text
When using ODBC backend to connect to a PostgreSQL database, please be aware that by default PostgreSQL ODBC driver truncates all "unknown" types, such as XML, to maximal varchar type size which is just 256 bytes and so is often insufficient for XML values in practice. It is advised to set the UnknownsAsLongVarchar connection option to 1 to avoid truncating XML strings or use PostgreSQL ODBC driver 9.6.300 or later, which allows the backend to set this option to 1 automatically on connection.
使用 ODBC 后端连接到 PostgreSQL 数据库时，请注意 默认情况下，PostgreSQL ODBC 驱动程序会截断所有“未知”类型，例如 XML，最大 varchar 类型大小仅为 256 字节，因此通常 在实践中，XML 值不够。建议设置 UnknownsAsLongVarchar 连接选项设置为 1 以避免截断 XML 字符串或使用 PostgreSQL ODBC 驱动程序 9.6.300 或更高版本，这允许后端在连接时自动将此选项设置为 1。

# Prepared statement  准备好的声明
Consider the following examples:
请考虑以下示例：

// Example 1.
for (int i = 0; i != 100; ++i)
{
    sql << "insert into numbers(value) values(" << i << ")";
}

// Example 2.
for (int i = 0; i != 100; ++i)
{
    sql << "insert into numbers(value) values(:val)", use(i);
}
Both examples will populate the table numbers with the values from 0 to 99.
这两个示例都将使用 0 到 99 之间的值填充表格 numbers 。

The problem is that in both examples, not only the statement execution is repeated 100 times, but also the statement parsing and preparation. This means unnecessary overhead, even if some of the database servers are likely to optimize the second case. In fact, more complicated queries are likely to suffer in terms of lower performance, because finding the optimal execution plan is quite expensive and here it would be needlessly repeated.
问题在于，在这两个示例中，不仅语句执行重复了100次，语句解析和准备也重复了100次。这意味着不必要的开销，即使某些数据库服务器可能会优化第二种情况。事实上，更复杂的查询可能会在性能方面受到影响，因为查找最佳执行计划的成本相当高，而且在这里会不必要地重复执行。

Statement preparation  声明准备
The following example uses the class statement explicitly, by preparing the statement only once and repeating its execution with changing data (note the use of prepare member of session class):
下面的示例明确地使用了类 statement ，通过仅准备一次语句并使用更改的数据重复执行（注意使用 session 类的 prepare 成员）：

int i;
statement st = (sql.prepare <<
                "insert into numbers(value) values(:val)",
                use(i));
for (i = 0; i != 100; ++i)
{
    st.execute(true);
}
The true parameter given to the execute method indicates that the actual data exchange is wanted, so that the meaning of the whole example is
传递给 execute 方法的 true 参数表示想要进行实际的数据交换，因此整个示例的含义是

"prepare the statement and exchange the data for each value of variable i".
“准备语句并交换变量 i 的每个值的数据”。

Portability note:  可移植性注意事项：
The above syntax is supported for all backends, even if some database server does not actually provide this functionality - in which case the library will internally execute the query in a single phase, without really separating the statement preparation from execution.
所有后端都支持上述语法，即使某些数据库服务器实际上不提供此功能 - 在这种情况下，库将在单个阶段内部执行查询，而不会真正将语句准备与执行分开。

Portability note:  可移植性注意事项：
The above syntax is supported for all backends, even if some database server does not actually provide this functionality - in which case the library will internally execute the query in a single phase, without really separating the statement preparation from execution.
所有后端都支持上述语法，即使某些数据库服务器实际上不提供此功能 - 在这种情况下，库将在单个阶段内部执行查询，而不会真正将语句准备与执行分开。

Rowset and iterator  行集和迭代器
The rowset class provides an alternative means of executing queries and accessing results using STL-like iterator interface.
rowset 类提供了使用类似 STL 的迭代器接口执行查询和访问结果的另一种方法。

The rowset_iterator type is compatible with requirements defined for input iterator category and is available via iterator and const_iterator definitions in the rowset class.
rowset_iterator 类型与为输入迭代器类别定义的要求兼容，并且可通过 rowset 类中的 iterator 和 const_iterator 定义获得。

The rowset itself can be used only with select queries.
rowset 本身只能用于选择查询。

The following example creates an instance of the rowset class and binds query results into elements of int type - in this query only one result column is expected. After executing the query the code iterates through the query result using rowset_iterator:
以下示例创建了一个 rowset 类的实例，并将查询结果绑定到 int 类型的元素中——此查询仅预期一个结果列。执行查询后，代码使用 rowset_iterator 遍历查询结果：

rowset<int> rs = (sql.prepare << "select values from numbers");

for (rowset<int>::const_iterator it = rs.begin(); it != rs.end(); ++it)
{
        cout << *it << '\n';
}
Another example shows how to retrieve more complex results, where rowset elements are of type row and therefore use dynamic bindings:
另一个示例显示如何检索更复杂的结果，其中 rowset 元素属于 row 类型，因此使用动态绑定 ：

// person table has 4 columns

rowset<row> rs = (sql.prepare << "select id, firstname, lastname, gender from person");

// iteration through the resultset:
for (rowset<row>::const_iterator it = rs.begin(); it != rs.end(); ++it)
{
    row const& row = *it;

    // dynamic data extraction from each row:
    cout << "Id: " << row.get<int>(0) << '\n'
            << "Name: " << row.get<string>(1) << " " << row.get<string>(2) << '\n'
            << "Gender: " << row.get<string>(3) << endl;
}
The rowset_iterator can be used with standard algorithms as well:
rowset_iterator 也可以与标准算法一起使用：

rowset<string> rs = (sql.prepare << "select firstname from person");

std::copy(rs.begin(), rs.end(), std::ostream_iterator<std::string>(std::cout, "\n"));
Above, the query result contains a single column which is bound to rowset element of type of std::string. All records are sent to standard output using the std::copy algorithm.
上面，查询结果包含一个绑定到 std::string 类型的 rowset 元素的列。所有记录都使用 std::copy 算法发送到标准输出。

If you need to use the Core interface with rowset, the following example shows how:
如果需要使用带有 rowset 的 Core 接口，以下示例显示如何操作：

row r;

statement st(sql);
st.alloc();
st.prepare("select values from numbers");
st.define_and_bind();

// after define_and_bind and before execute
st.exchange_for_rowset(into(r));

st.execute(false);

rowset_iterator<row> it(st, r);
rowset_iterator<row> end;
for (; it != end; ++it) {
    // ... access *it
}
Bulk operations  批量操作
When using some databases, further performance improvements may be possible by having the underlying database API group operations together to reduce network roundtrips. SOCI makes such bulk operations possible by supporting std::vector based types.
使用某些数据库时，可以通过将底层数据库 API 的操作分组以减少网络往返次数来进一步提升性能。SOCI 通过支持基于 std::vector 的类型来实现此类批量操作。

The following example presents how to insert 100 records in 4 batches. It is also important to note, that size of vector remains equal in every batch interaction. This ensures vector is not reallocated and, what's crucial for the bulk trick, new data should be pushed to the vector before every call to statement::execute:
以下示例演示了如何分 4 个批次插入 100 条记录。同样需要注意的是，向量的大小在每次批次操作中都保持相等。这确保了向量不会被重新分配，而且对于批量操作来说至关重要的是，每次调用 statement::execute 之前都应该将新数据推送到向量中：

// Example 3.
void fill_ids(std::vector<int>& ids)
{
    for (std::size_t i = 0; i < ids.size(); ++i)
        ids[i] = i; // mimics source of a new ID
}

const int BATCH_SIZE = 25;
std::vector<int> ids(BATCH_SIZE);

statement st = (sql.prepare << "insert into numbers(value) values(:val)", use(ids));
for (int i = 0; i != 4; ++i)
{
    fill_ids(ids);
    st.execute(true);
}
Given batch size is 25, this example should insert 4 x 25 = 100 records.
假设批量大小为 25，此示例应插入 4 x 25 = 100 条记录。

(Of course, the size of the vector that will achieve optimum performance will vary, depending on many environmental factors, such as network speed.)
（当然，实现最佳性能的向量的大小会有所不同，这取决于许多环境因素，例如网络速度。）

It is also possible to read all the numbers written in the above examples:
也可以读出上述例子中写的所有数字：

int i;
statement st = (sql.prepare << "select value from numbers order by value", into(i));
st.execute();
while (st.fetch())
{
    cout << i << '\n';
}
In the above example, the execute method is called with the default parameter false. This means that the statement should be executed, but the actual data exchange will be performed later.
在上面的例子中， execute 方法被调用时使用了默认参数 false 。这意味着该语句应该被执行，但实际的数据交换将在稍后进行。

Further fetch calls perform the actual data retrieval and cursor traversal. The end-of-cursor condition is indicated by the fetch function returning false.
后续的 fetch 调用将执行实际的数据检索和游标遍历。如果 fetch 函数返回 false ，则表示已达到游标结束条件。

The above code example should be treated as an idiomatic way of reading many rows of data, one at a time.
上面的代码示例应被视为一次读取多行数据的惯用方法。

It is further possible to select records in batches into std::vector based types, with the size of the vector specifying the number of records to retrieve in each round trip:
还可以将记录分批选择到基于 std::vector 的类型中，向量的大小指定每次往返要检索的记录数：

std::vector<int> valsOut(100);
sql << "select val from numbers", into(valsOut);
Above, the value 100 indicates that no more values should be retrieved, even if it would be otherwise possible. If there are less rows than asked for, the vector will be appropriately down-sized.
上面的值 100 表示即使可以检索，也不应该再检索更多值。如果行数少于要求的行数，向量将适当缩小。

The statement::execute() and statement::fetch() functions can also be used to repeatedly select all rows returned by a query into a vector based type:
statement::execute() 和 statement::fetch() 函数也可用于重复选择查询返回的所有行到基于向量的类型中：

const int BATCH_SIZE = 30;
std::vector<int> valsOut(BATCH_SIZE);
statement st = (sql.prepare <<
                "select value from numbers",
                into(valsOut));
st.execute();
while (st.fetch())
{
    std::vector<int>::iterator pos;
    for(pos = valsOut.begin(); pos != valsOut.end(); ++pos)
    {
        cout << *pos << '\n';
    }

    valsOut.resize(BATCH_SIZE);
}
Assuming there are 100 rows returned by the query, the above code will retrieve and print all of them. Since the output vector was created with size 30, it will take (at least) 4 calls to fetch() to retrieve all 100 values. Each call to fetch() can potentially resize the vector to a size less than its initial size - how often this happens depends on the underlying database implementation. This explains why the resize(BATCH_SIZE) operation is needed - it is there to ensure that each time the fetch() is called, the vector is ready to accept the next bunch of values. Without this operation, the vector might be getting smaller with subsequent iterations of the loop, forcing more iterations to be performed (because all rows will be read anyway), than really needed.
假设查询返回 100 行数据，上述代码将检索并打印所有数据。由于输出向量的大小为 30，因此至少需要调用 fetch() 四次才能检索全部 100 个值。每次调用 fetch() 都可能将向量的大小调整到小于其初始大小——这种情况发生的频率取决于底层数据库的实现。这解释了为什么需要 resize(BATCH_SIZE) 操作——它是为了确保每次调用 fetch() 时，向量都已准备好接受下一批值。如果没有此操作，向量可能会随着循环的后续迭代而变得越来越小，从而迫使执行比实际需要更多的迭代次数（因为无论如何都会读取所有行）。

Note the following details about the above examples:
请注意上述示例的以下细节：

After performing fetch(), the vector's size might be less than requested, but fetch() returning true means that there was at least one row retrieved.
执行 fetch() 后，向量的大小可能小于请求的大小，但 fetch() 返回 true 意味着至少检索了一行。
It is forbidden to manually resize the vector to the size higher than it was initially (this can cause the vector to reallocate its internal buffer and the library can lose track of it).
禁止手动将向量的大小调整为高于其初始大小（这可能导致向量重新分配其内部缓冲区，并且库可能会失去对它的跟踪）。
Taking these points under consideration, the above code example should be treated as an idiomatic way of reading many rows by bunches of requested size.
考虑到这些要点，上述代码示例应被视为按请求的大小读取多行的惯用方法。

Portability note  可移植性说明
Actually, all supported backends guarantee that the requested number of rows will be read with each fetch and that the vector will never be down-sized, unless for the last fetch, when the end of rowset condition is met. This means that the manual vector resizing is in practice not needed - the vector will keep its size until the end of rowset. The above idiom, however, is provided with future backends in mind, where the constant size of the vector might be too expensive to guarantee and where allowing fetch to down-size the vector even before reaching the end of rowset might buy some performance gains.
实际上，所有受支持的后端都保证每次读取时都会读取请求的行数，并且向量永远不会缩小，除非在最后一次读取时满足了行集的末尾条件。这意味着实际上不需要手动调整向量大小——向量会保持其大小直到行集末尾。然而，上述习惯用法是为未来的后端设计的，在这种情况下，保证向量的恒定大小可能过于昂贵，而允许在到达行集末尾之前通过 fetch 缩小向量大小可能会带来一些性能提升。

Statement caching  语句缓存
Some backends have some facilities to improve statement parsing and compilation to limit overhead when creating commonly used query. But for backends that does not support this kind optimization you can keep prepared statement and use it later with new references. To do such, prepare a statement as usual, you have to use exchange to bind new variables to statement object, then execute statement and finish by cleaning bound references with bind_clean_up.
一些后端提供了一些功能来改进语句的解析和编译，以在创建常用查询时限制开销。但是对于不支持此类优化的后端，您可以保留准备好的语句，并在以后将其与新的引用一起使用。为此，请像往常一样准备一个语句，然后使用 exchange 将新变量绑定到语句对象，然后 execute 语句，最后使用 bind_clean_up 清理绑定的引用。

sql << "CREATE TABLE test(a INTEGER)";

{
    // prepare statement
    soci::statement stmt = (sql.prepare << "INSERT INTO numbers(value) VALUES(:val)");

    {
        // first insert
        int a0 = 0;

        // update reference
        stmt.exchange(soci::use(a0));

        stmt.define_and_bind();
        stmt.execute(true);
        stmt.bind_clean_up();
    }

    {
        // come later, second insert
        int a1 = 1;

        // update reference
        stmt.exchange(soci::use(a1));

        stmt.define_and_bind();
        stmt.execute(true);
        stmt.bind_clean_up();
    }
}

{
    std::vector<int> v(10);
    sql << "SELECT value FROM numbers", soci::into(v);
    for (int i = 0; i < v.size(); ++i)
        std::cout << "value " << i << ": " << v[i] << std::endl;
}
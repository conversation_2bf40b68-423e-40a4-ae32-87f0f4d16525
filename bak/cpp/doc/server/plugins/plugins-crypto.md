## 插件：Crypto（加解密）

### 功能与设计
- 接收 COT=ACTIVATION 的消息，根据 FUN 选择算法（aes/rsa/base64/sha256）
- 根据 INF 执行 encrypt(OPERATION_EXECUTE) 或 decrypt(OPERATION_REVERSE)
- text=输入文件/目录路径
- 自动进行密钥生成/保存/加载（config/keys/<algo>.key）

### 输入/输出
- 输入：Message.text=路径；FUN/INF 指示操作
- 输出：
  - 加密：输入 -> <path>_crypto
  - 解密：输入 -> <path>_uncrypto
  - 日志 plugin/crypto_plugin

### 错误处理
- 不支持算法/密钥缺失/文件不存在：记录错误并返回

### 性能标准
- 关键路径串行执行并加互斥，避免并发读写冲突

### 接口（I/O 定义）
- Input Message:
  - COT=0x06, FUN={0x01..0x04}, INF=0x01(加密)/0x02(解密), text=路径


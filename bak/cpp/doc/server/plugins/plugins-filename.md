## 插件：Filename（批量重命名）

### 功能与设计
- 接收 COT=ACTIVATION, FUN=FILENAME_RENAME 的消息
- text=目录路径；遍历文件并并行重命名
- 规则：创建时间 + UUID + 原扩展名

### 输入/输出
- 输入：Message.text = 目录路径
- 输出：重命名后的文件；日志 plugin/Filename_plugin

### 错误处理
- 非法目录/无文件：warn 或 error
- 单个文件失败不影响其他文件；统计 processed/success/failed

### 性能标准
- 线程池大小默认 32；重命名过程避免长时间持锁
- 确保重名冲突处理（尾部 _<n>）

### 接口（I/O 定义）
- Input Message:
  - COT=0x06, FUN=0x01, text=路径
- Output Message:
  - 可选向 server/客户端发送 INFO_RESPONSE（查询能力时）


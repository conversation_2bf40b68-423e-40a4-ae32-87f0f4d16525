## 插件：Unzip（解压）

### 功能与设计
- 接收 COT=ACTIVATION, FUN=UNZIP_EXTRACT 的消息
- 可变结构文本为目录路径：扫描其中所有 .zip 文件
- 使用线程池并行解压到 <dir>/extracted/<zipname>/
- 解压成功后向 filename_plugin 发送重命名消息

### 输入/输出
- 输入：Message.text = 目录路径
- 输出：
  - 解压后的文件树
  - 发送给 filename_plugin 的 Message（text=解压目录）
  - 日志：plugin/unzip_plugin

### 错误处理
- 目录不存在/不可读：记录 error 并返回
- zip 打开失败：记录 error 并计数 failedExtracts_
- 任务异常：捕获并记录，不影响其他任务

### 性能标准
- 支持并行解压；默认线程池大小 16（见源码）
- 单文件缓冲 8KB；I/O 顺序写

### 接口（I/O 定义）
- Input Message:
  - COT=0x06, FUN=0x01, text=路径
- Output Message:
  - 发送到 filename_plugin：TYP=0x01, VSQ=0x81, COT=0x06, FUN=0x01, INF=0x00, text=提取目录


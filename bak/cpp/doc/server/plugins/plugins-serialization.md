## 插件：Serialization（XML/JSON 解析）

### 功能与设计
- 接收 COT=ACTIVATION, FUN=SERIALIZE_XML/JSON 的消息
- text=文件路径；解析并汇总键值对
- 当 format=XML 时，根据 items 中的 file.trans.type、encrypt 决策联动流程：
  - 发送加密消息到 crypto_plugin：fun=算法，inf=OPERATION_EXECUTE，text=config/message
  - 发送传输消息到 transfer_plugin：fun=传输协议，text=config/message_crypto

### 输入/输出
- 输入：Message.text=文件路径
- 输出：解析摘要日志；可能的后续消息到 crypto/transfer

### 错误处理
- 不支持格式/解析失败：记录错误并返回

### 性能标准
- 解析在插件内部同步完成（数据量大时可改造为异步）

### 接口（I/O 定义）
- Input Message:
  - COT=0x06, FUN=0x01(xml)/0x02(json), text=文件路径
- Output Message:
  - 到 crypto_plugin：COT=0x06, FUN={aes|rsa|...}, INF=0x01, text=config/message
  - 到 transfer_plugin：COT=0x06, FUN={scp|sftp}, text=config/message_crypto


## 配置与运行参数（Configuration）

### 目录与文件
- cpp/config/config.json / config.xml：示例配置
- config/keys：加密密钥保存目录

### 服务器端关键项（建议）
- network:
  - host: 0.0.0.0
  - port: 3414
  - io_threads: 1
- plugins:
  - name: filename_plugin / unzip_plugin / serialization_plugin / transfer_plugin / crypto_plugin
  - id: 插件ID（与客户端/消息路由一致）
  - path: 动态库路径
  - enabled: true/false

### 客户端关键项（可选）
- server_host / server_port
- 交互模式默认打开，可通过命令行覆盖

### 变更管理
- 修改插件清单需重启服务端（或实现热重载）
- 配置文件建议进行 schema 校验


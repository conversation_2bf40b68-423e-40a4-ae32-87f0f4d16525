## 消息协议与编码规范（Protocol & Message）

本系统基于 IEC 60870-5-103 ASDU 抽象，统一以 Message 类表示。参考：cpp/core/include/zexuan/base/message.hpp 与 message_types.hpp。

### 帧结构

- START: 0x68（内部处理）
- LENGTH_L / LENGTH_H（内部处理）
- TYP: Type Identification（默认 0x01）
- VSQ: Variable Structure Qualifier（默认 0x81 单信息）
- COT: Cause of Transmission（见枚举）
- SOURCE: 源地址（客户端或插件ID）
- TARGET: 目标地址（插件ID或客户端ID）
- FUN: 功能类型（按插件定义）
- INF: 信息序号（操作子类型）
- Variable Structure: 变长区（常用为文本，如路径/JSON）

Message API 支持：序列化/反序列化、setTextContent/getTextContent、swapSourceTarget 等。

### 关键枚举（唯一权威定义）

- CauseOfTransmission
  - INFO_REQUEST=0x01, INFO_RESPONSE=0x02, SPONTANEOUS=0x03,
    ACTIVATION=0x06, CONFIRMATION=0x07, DEACTIVATION=0x08, TERMINATION=0x0A
- FunctionType（按插件分组）
  - filename: FILENAME_RENAME=0x01
  - unzip: UNZIP_EXTRACT=0x01
  - crypto: CRYPTO_AES=0x01, CRYPTO_RSA=0x02, CRYPTO_BASE64=0x03, CRYPTO_SHA256=0x04
  - transfer: TRANSFER_SCP=0x01, TRANSFER_SFTP=0x02
  - serialization: SERIALIZE_XML=0x01, SERIALIZE_JSON=0x02
- InformationNumber
  - STATUS_REQUEST=0x00, OPERATION_EXECUTE=0x01, OPERATION_REVERSE=0x02

注意：source/target 为路由依据；插件回复时需调用 swapSourceTarget()。

### 输入与输出（I/O）

- 输入：
  - 来自 TCP 网络的字节流，由 MessageRouter 解析为 Message
  - 插件间通过 Mediator 发送的 Message（text 常为文件路径或 JSON）
- 输出：
  - 发送到客户端或其他插件的 Message
  - 通过 Logger 写入的模块化日志

### 兼容与扩展

- 新增插件时，在 message_types.hpp 中增加 FUN 常量
- VSQ 可扩展为多信息帧（目前统一使用 0x81 单信息）
- Variable Structure 建议使用 UTF-8 文本；二进制可使用 base64 编码


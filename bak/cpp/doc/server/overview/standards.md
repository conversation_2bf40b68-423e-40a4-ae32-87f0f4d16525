## 技术标准与规范（Standards）

### 代码与风格
- 语言标准：C++17
- 头/源组织：public 头在 core/include、插件对外头在各插件 include；源放在 core/src、plugins/*/src
- 命名：命名空间 zexuan::{base, net, plugin, server, client}
- 文件编码：UTF-8，无BOM
- 跨平台：主要针对 Linux（epoll/poll），支持 gcc/clang

### 日志
- 统一使用 zexuan::Logger（spdlog 封装）；按组件输出到 cpp/logs 下的分类文件
- 日志级别：trace/debug/info/warn/error；默认 info，测试/调试可提升到 debug
- 关键路径需记录输入参数、状态转移、错误码与耗时

### 配置
- 配置文件位于 cpp/config：支持 json/xml；server 与 client 可分别读取
- 路径规范：文件/目录由消息 variable structure 传递时，应为绝对或相对工作目录的路径

### 插件
- 必须继承 PluginBase，并实现：
  - initialize()/shutdown()
  - processMessage(const base::Message&)
- 使用 ZEXUAN_PLUGIN_EXPORT(ClassName) 导出 create_plugin/destroy_plugin
- 插件对外能力与 FUN/INF 值在 message_types.hpp 中登记

### 错误模型
- 失败时返回 false 或负值；异常用于不可恢复错误，需捕获并记录
- 插件间通信失败应记录错误并不中断宿主服务

### 安全
- 文件读写前检查存在性与权限
- 网络输入严格按协议解析；长度字段校验；上限保护（防止超大帧）

### 性能
- IO 线程避免阻塞；CPU 密集操作用线程池/任务队列
- 日志异步落盘，控制单条日志大小

### 构建与依赖
- 使用 CMake + Conan；不要手改依赖文件，使用包管理命令维护


## 系统架构设计（Architecture）

本项目是一个基于消息驱动的可插拔式C++系统，核心由以下层次组成：

- 接口层（interface）：定义统一的插件基类与导出规范
- 核心库（core）：
  - base：消息模型、观察者/主题模式、Mediator、动态库加载
  - net：基于 Reactor 的事件循环与 TCP 网络栈
  - utils：通用工具（文件/字符串）、线程池、日志
- 应用层（app）：
  - server：SystemManager 统筹网络、插件与业务；PluginManager 动态加载插件；MessageRouter 负责网络->业务的消息编解码与路由；MessageHandler 处理业务逻辑
  - client：MessageClient 作为测试/交互客户端
- 插件层（plugins）：以共享库形式提供具体功能（unzip、filename、serialization、transfer、crypto）

### 设计过程与关键决策

1) 消息为中枢：采用精简的 IEC 60870-5-103 ASDU 抽象（typ/vsq/cot/source/target/fun/inf + 可变结构）。
2) 中介者 + 观察者：插件对外统一继承 PluginBase（Observer+Subject），通过 Mediator 进行解耦通信。
3) 网络与业务解耦：MessageRouter 负责网络帧与 Message 对象间的编解码与分发，业务侧仅处理 Message。
4) 插件可热插拔：PluginManager 使用 DynamicLibraryLoader 加载 .so，统一的 extern "C" create_plugin/destroy_plugin 导出由宏生成。
5) 高并发与可扩展：net 基于 Reactor（Poll/EPoll），配合线程池在计算密集任务中异步化；日志基于 spdlog，模块化文件落盘。

### 组件与边界

- interface
  - plugin_base.hpp：定义 PluginBase 生命周期、注册/注销、消息处理入口 processMessage()
  - plugin_export.hpp：ZEXUAN_PLUGIN_EXPORT 宏生成 C 接口导出
- core/base
  - message.hpp：消息结构，提供序列化/反序列化与文本变量区便捷接口
  - message_types.hpp：TYP/VSQ/COT/FUN/INF 的唯一权威定义
  - mediator.hpp/observer.hpp/subject.hpp：典型观察者模式 + Mediator 路由
  - dynamic_library_loader.hpp：封装 dlopen/dlsym/dlclose
- core/net
  - EventLoop/TcpServer/TcpClient/Channel/Poller/Timer...：事件驱动网络栈
- core/utils
  - thread_pool.hpp：通用线程池
  - file_utils.hpp/string_utils.hpp
  - logger.hpp：统一 spdlog 封装，提供分类文件日志
- app/server
  - SystemManager：初始化/启动/停止、单例注册、信号处理
  - PluginManager：从配置加载插件，创建/生命周期管理
  - MessageRouter：把 TCP 原始数据解析为 Message 并路由到业务，反向发送
  - MessageHandler：业务逻辑聚合（插件交互、策略）
- app/client
  - MessageClient：面向用户/测试的交互式客户端，支持插件信息查询与操作
- plugins/*
  - unzip/filename/serialization/transfer/crypto：各自实现 processMessage，按 FUN/INF 和文本变量执行具体功能

### 运行时数据流（高层）

1. Client 构造 Message（填充 target=插件ID，variableStructure=文本/路径/JSON），经 TCP 发送到 Server。
2. Server 的网络层把字节流交给 MessageRouter 解析为 Message，交给 MessageHandler。
3. MessageHandler 根据 target 将消息投递到 Mediator，Mediator 通知对应 Plugin（Observer）。
4. Plugin 在 processMessage() 中执行业务，必要时通过 sendPluginMessage(ToTarget) 再次经 Mediator/Router 发送到指定目标（含服务端/其他插件/客户端）。

### 部署形态

- 服务端：systemd 管理（zexuan-server.service），二进制 bin/server-<ver>
- 客户端：命令行交互工具 bin/client-<ver>
- 插件：libs/plugin_*.so（通过配置开启/禁用）

### 非功能性目标

- 可维护性：统一规范/日志/错误模型/配置
- 可测试性：丰富的单元与双向联通（client-server）集成测试
- 可扩展性：PluginBase + Factory 模式扩展新算法/协议/格式


## 构建与部署（Build & Deploy）

### 依赖与环境
- 操作系统：Linux（x86_64）
- 编译器：gcc/clang（C++17）
- 构建系统：CMake 3.20+
- 依赖管理：Conan（见 cpp/conanfile.txt）
- 第三方库：spdlog、libzip 等（通过 Conan/系统包提供）

### 构建
- 顶层脚本：cpp/build.sh（封装常用构建流程）
- 典型步骤：
  1) conan install . -of build -s build_type=Release
  2) cmake -B build -S . -DCMAKE_BUILD_TYPE=Release
  3) cmake --build build -j
- 产物：
  - bin/server-<version>, bin/client-<version>
  - libs/libcore.so, libs/libplugin_interface.so
  - plugins 构建出的 *.so

### 运行
- 配置文件：位于 cpp/config（json/xml）
- 服务端：
  - ./bin/server <配置文件路径>
  - systemd: 使用 cpp/zexuan-server.service（需按环境适配路径）
- 客户端：
  - ./bin/client [config.json 127.0.0.1 3414]

### 插件部署
- 将 plugin_*.so 放置在配置指向的位置（通常 cpp/libs/plugins 或系统目录）
- 使用配置开关启用/禁用插件

### 日志与数据
- 日志目录：cpp/logs 下分模块产生日志文件
- 临时/输出：
  - 解压输出：<输入目录>/extracted
  - 加解密：config/message_crypto, config/message_uncrypto
  - 密钥：config/keys/<algo>.key

### 升级/回滚建议
- 将插件与核心二进制分发为独立包
- 版本化配置与 schema；灰度启用插件


## 核心库：net（事件循环与TCP栈）

### 模块职责
- Reactor/Proactor 风格事件循环（EventLoop, Channel, Poller/EPollPoller, Timer/TimerQueue）
- TCP 端点（TcpServer, TcpClient, TcpConnection, Acceptor, Connector, InetAddress, Buffer）

### 设计要点
- 单线程 EventLoop 驱动 IO；EventLoopThread/Pool 支持多线程
- TcpServer 负责监听与连接管理；TcpClient 管理客户端连接生命周期
- Poller 抽象以适配 poll/epoll；DefaultPoller 动态选择
- Buffer 提供高效字节缓存；Codec 可按需扩展应用层协议编解码

### 输入/输出
- 输入：socket 事件（可读/可写/错误/关闭）
- 输出：写缓冲发送；回调到上层（MessageRouter）

### 关键回调
- TcpConnection:
  - setConnectionCallback, setMessageCallback, setWriteCompleteCallback
- TcpServer/TcpClient 与上层集成：MessageRouter::handleNetworkData()

### 性能与可靠性
- 非阻塞套接字 + 边缘触发（epoll）
- 严格避免在 IO 回调中做重活；CPU 密集任务丢到线程池
- 连接关闭/错误路径稳定处理，防止 fd 泄漏


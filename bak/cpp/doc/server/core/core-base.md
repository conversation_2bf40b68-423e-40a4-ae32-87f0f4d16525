## 核心库：base（消息/观察者/Mediator/动态库）

### 模块职责
- message.hpp：IEC 60870-5-103 抽象；序列化/反序列化；可变结构文本便捷接口
- message_types.hpp：TYP/VSQ/COT/FUN/INF 常量与注释，唯一权威来源
- mediator.hpp/observer.hpp/subject.hpp：基于观察者模式的事件分发；Mediator 维持名称<->ID 映射和路由
- dynamic_library_loader.hpp：dlopen/dlsym 封装并记录日志

### 设计要点
- Message 以 source/target 路由；插件回复先 swapSourceTarget()
- 可变结构体变量区建议 UTF-8 文本；API：setTextContent()/getTextContent()
- 插件通过 Mediator 进行解耦通信；PluginBase 在构造时自动注册

### 输入/输出
- 输入：
  - 底层网络来的二进制帧（经 MessageRouter 解析为 Message）
  - 插件间消息（PluginBase::sendPluginMessage*）
- 输出：
  - Message::serialize() 输出到网络
  - 交由 Mediator 的通知流

### 接口摘要
- base::Message
  - 字段：typ, vsq, cot, source, target, fun, inf, variable
  - 方法：serialize/deserialize, setTextContent/getTextContent, swapSourceTarget
- base::Mediator
  - registerObserver/unregisterObserver, route(message)
- base::DynamicLibraryLoader
  - open(path), load(symbol), close()

### 约束
- 长度字段自动维护；反序列化时校验长度与起始字符
- 异常捕获：对外 C 接口禁止抛出异常


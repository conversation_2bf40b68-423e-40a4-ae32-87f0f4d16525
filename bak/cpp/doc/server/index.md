## Zexuan C++ 项目设计文档索引（按文件夹分类）

本目录采用分层文件夹组织文档，便于维护与导航。

- overview/ — 架构与规范
  - overview/architecture.md — 系统架构与设计过程
  - overview/protocol-message.md — 消息协议与编码规范
  - overview/standards.md — 编码/日志/配置等技术规范
  - overview/build-and-deploy.md — 构建与部署
  - overview/configuration.md — 配置与运行参数

- core/ — 核心库
  - core/core-base.md — 观察者/主题、Mediator、Message 设计
  - core/core-net.md — 事件循环与网络栈（TcpServer/Client 等）
  - core/core-utils.md — 线程池、日志、工具库

- app/ — 应用层
  - app/app-server.md — 服务器端（SystemManager、PluginManager、MessageRouter 等）
  - app/app-client.md — 客户端（MessageClient）

- plugins/ — 插件
  - plugins/plugins-unzip.md — 解压插件
  - plugins/plugins-filename.md — 文件名重命名插件
  - plugins/plugins-serialization.md — 序列化/反序列化插件
  - plugins/plugins-transfer.md — 传输插件（SCP/SFTP）
  - plugins/plugins-crypto.md — 加解密插件

- quality/ — 质量与保障
  - quality/error-handling.md — 错误处理机制
  - quality/performance.md — 性能要求与基准
  - quality/testing.md — 测试策略与用例

说明：当前保留了旧的平铺文档以避免破坏引用；如需删除旧文件以仅保留分层结构，请明确告知。
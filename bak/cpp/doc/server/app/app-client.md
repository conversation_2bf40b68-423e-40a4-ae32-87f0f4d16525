## 应用层：Client（MessageClient）

### 职责
- 建立到服务器的 TcpClient 连接
- 交互式构造消息：选择插件/操作，填写文本变量（路径/JSON）
- 维护插件信息缓存（INFO_REQUEST/INFO_RESPONSE 往返）

### 关键流程
- run(): 初始化 -> 连接 -> 事件循环线程 -> 用户输入线程
- queryPluginInfo(pluginId): 发送 COT=INFO_REQUEST，收到 INFO_RESPONSE 存入缓存
- sendMessage(message): 序列化为字节流并发送

### 输入/输出
- 输入：用户命令、服务器回包
- 输出：消息到服务器、客户端日志

### 错误处理
- 连接断开自动重试（可选）；非法输入给出提示

### 性能
- 事件循环与输入分线程执行；避免 UI 阻塞 IO


## 应用层：Server（SystemManager / PluginManager / MessageRouter）

### SystemManager（system_manager.hpp）
- 职责：
  - 初始化日志、信号处理、配置加载
  - 构造 Mediator、网络管理器（NetworkManager）、PluginManager、MessageHandler、MessageRouter
  - 维护运行状态，提供 start()/stop()/waitForStop()
- 输入：配置路径、OS 信号
- 输出：组件初始化、控制台与文件日志

### PluginManager（plugin_manager.hpp）
- 生命周期：loadFromConfig() -> initializePlugins() -> startPlugins() -> stopPlugins() -> unloadPlugins()
- 动态库：DynamicLibraryLoader 加载 .so，使用宏导出 create_plugin/destroy_plugin
- 插件信息：记录 id/name/path/enabled/loaded/instance
- I/O：
  - 输入：配置中的插件清单
  - 输出：实例化并调用 initialize()/shutdown()

### MessageRouter（message_router.hpp）
- 职责：网络数据 -> Message 解析；Message -> 网络字节流
- 回调：
  - setMessageHandler(handler) 把解析后的 Message 投递给业务层
  - setNetworkSendCallback(cb) 提供反向发送能力
- I/O：
  - 输入：TcpConnection 收到的字节流
  - 输出：调用 MessageHandlerCallback；或 sendMessage() 写回客户端

### MessageHandler（message_handler.hpp）
- 业务层聚合：将 Message 分派到 Mediator；或根据 COT/FUN/INF 做系统级处理

### 配置（config.hpp）
- 端口/线程池大小/插件清单等运行参数

### 与网络层集成
- NetworkManager 负责建立 TcpServer/客户端连接；onMessage -> MessageRouter::handleNetworkData()

### 故障与恢复
- 插件初始化失败：记录并跳过，保持宿主存活
- 动态库符号缺失：详细日志 + 退避


## 测试策略

### 层次划分
- 单元测试：Message 序列化/反序列化；utils；各插件工厂与边界条件
- 组件测试：net 连接/收发，MessageRouter 解析/编码
- 集成测试：client-server 往返；插件之间联动（XML->Crypto->Transfer；Unzip->Filename）

### 目录建议
- test/
  - unit/*
  - integration/*
  - e2e/*

### 重点场景（结合用户偏好：双向通信及错误场景）
- Client->Server：发送 ACTIVATION 到各插件，验证目标 side-effect 与日志
- 插件 INFO_REQUEST/INFO_RESPONSE 往返：缓存与展示菜单
- 错误：
  - 不存在路径/不可读文件
  - 不支持算法/格式
  - 目标插件名未注册

### 自动化
- CTest + GTest（建议）；运行脚本 build.sh
- CI：并行构建、产生日志归档


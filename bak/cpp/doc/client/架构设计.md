## 客户端分层架构设计（与服务端一致的分层风格）

### 设计目标与总体思路
- 与服务端在“层”的划分和职责边界上保持一致：配置/系统管理（SystemManager 等价物）、网络层（TcpClient 封装）、消息路由层（序列化/反序列化/路由）、应用/业务层（消息构造与交互）、插件/扩展（如将来需要客户端侧扩展）。
- 充分复用 core 库（base/net/logger 等），避免重复实现。
- 明确分层依赖自上而下单向：App(业务/UI) -> MessageRouter(编解码/路由) -> Network(连接/IO)；横切关注如日志、配置通过注入方式提供。
- 目录结构与 CMake 目标命名风格与服务端 mirror。

### 与服务端的分层对应关系
- Server/SystemManager ↔ Client/SystemManager（新增）：负责初始化日志、加载配置、组装 NetworkManager/MessageRouter/MessageHandler（客户端侧命名略有不同）。
- Server/NetworkManager(TcpServer) ↔ Client/NetworkManager(TcpClient)：只做连接与字节流 IO，回调上送，不做业务。
- Server/MessageRouter ↔ Client/MessageRouter：统一负责 Message 的 serialize/deserialize 与路由（上送给业务层处理、下发到网络层）。
- Server/MessageHandler ↔ Client/MessageHandler：业务聚合层，处理用户命令、状态、对收到的 Message 做分类处理。
- Server/PluginManager ↔ Client/扩展点：短期不需要客户端插件，但保留扩展点（如“客户端工具模块”）。

### 分层职责
1) Presentation/Application 层（app）
- 入口 main，解析命令行；
- SystemManager（客户端）：生命周期管理：initialize/start/stop/waitForStop；
- MessageHandler（客户端）：
  - 聚合业务：命令行/交互式 UI 状态机；
  - 发送业务请求：构造 base::Message（TYP/VSQ/COT/FUN/INF/variable）并调用 Router 下发；
  - 处理来自服务端的响应/通知，维护客户端侧缓存（如插件信息）。

2) MessageRouter 层（router）
- 入站：从 Network 层接收字节流，增量解析为 base::Message，提取 clientId（客户端固定自报 ID）并调用上层回调；
- 出站：接收上层的 base::Message，序列化为字节流，调用 Network 层发送；
- 隔离编解码细节，便于将来切换协议或做批量/粘包处理。

3) Network 层（network）
- 封装 zexuan::net::TcpClient 与 EventLoop；
- 负责连接管理、自动重连（可选）、读写回调；
- 不理解业务帧，仅把 Buffer 字节传给 Router。

4) Core/Infra（依赖）
- 复用 core/base（Message、Mediator 可选）、core/net、logger；
- 配置模块：最小化客户端配置（地址/端口/日志级别等）。

### 层间依赖关系
- Application -> MessageRouter -> Network（自上而下）；
- Application 仅通过 Router 发送/接收 Message，不直接操作 Network；
- Router 持有两个回调：MessageHandlerCallback（上送业务）、NetworkSendCallback（下发网络）。

### 目录结构重组建议
当前：
- cpp/app/client/
  - include/message_client.hpp
  - src/main.cpp, src/message_client.cpp

建议重组为（与 server 对齐）：
- cpp/app/client/
  - include/
    - system_manager.hpp
    - network_manager.hpp
    - message_router.hpp
    - message_handler.hpp
    - config.hpp
  - src/
    - system_manager.cpp
    - network_manager.cpp
    - message_router.cpp
    - message_handler.cpp
    - config.cpp
    - main.cpp
  - CMakeLists.txt（目标仍为 client，可细分静态库/对象库可选）

迁移与命名建议：
- 现有 MessageClient 拆分：
  - 交互/业务相关方法 → message_handler.[hpp/cpp]
  - 序列化/发送/收包处理 → message_router.[hpp/cpp]
  - TcpClient 管理/事件循环 → network_manager.[hpp/cpp]
  - 参数解析/整体启动流程（run/stop） → system_manager.[hpp/cpp]
- main.cpp 精简为：解析参数 → SystemManager.initialize/start → waitForStop。

### CMake 对齐
- 参考 server 的 CMake，将 client 目标包含 include 目录与 core/include；
- 后续可将 client 按组件拆分对象库以便单测：client_network, client_router, client_app；
- 链接依赖：core, nlohmann_json, pthread。

### 客户端 Message 流程（示意）
- 出站：MessageHandler::onUserCommand → 构造 base::Message → Router::sendMessage → NetworkManager::send(bytes)
- 入站：NetworkManager::onMessage(bytes) → Router::handleNetworkData(parse) → MessageHandler::onMessage(message)

### 与服务端风格一致性的要点
- 命名/文件夹与 server 镜像化；Manager/Router/Handler 的职责边界与 server 对称；
- 使用回调注入形成松耦合：SystemManager.setupCallbacks() 中完成双向回调绑定；
- 统一日志风格与异常处理策略；
- 配置文件结构与键名尽量与服务端一致（端口/线程/日志级别等）。

### 迭代计划与兼容性
- 第一阶段：仅做目录与代码拆分重构，不改变外部行为与命令行参数；
- 第二阶段：引入 SystemManager 与最小 Config；
- 第三阶段：视需要扩展 UI 或客户端插件机制（可选）。

### 附：与服务端的对应清单
- server/include/system_manager.hpp ↔ client/include/system_manager.hpp（新增）
- server/include/network_manager.hpp ↔ client/include/network_manager.hpp（新增）
- server/include/message_router.hpp ↔ client/include/message_router.hpp（新增）
- server/include/message_handler.hpp ↔ client/include/message_handler.hpp（新增）
- server/include/config.hpp ↔ client/include/config.hpp（可选简化）

[Unit]
Description=Zexuan Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/root/zexuan/cpp
ExecStart=/root/zexuan/cpp/bin/server /root/zexuan/cpp/config/config.json
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=3
KillSignal=SIGTERM
TimeoutStopSec=10
Environment="LD_LIBRARY_PATH=/root/zexuan/cpp/libs:/root/zexuan/cpp/libs/plugins"

[Install]
WantedBy=multi-user.target
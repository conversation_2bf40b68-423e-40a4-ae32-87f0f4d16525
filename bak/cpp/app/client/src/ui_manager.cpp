/**
 * @file ui_manager.cpp
 * @brief UI管理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "../include/ui_manager.hpp"
#include <iostream>
#include <limits>

namespace zexuan {
namespace client {

UiManager::UiManager(const std::string& serverAddr)
    : serverAddr_(serverAddr)
    , running_(false)
    , uiState_(UiState::COMMAND_MODE)
    , logger_(zexuan::Logger::getFileLogger("UiManager")) {
    
    logger_->info("UiManager created for server: {}", serverAddr);
}

UiManager::~UiManager() {
    stop();
    logger_->info("UiManager destroyed");
}

void UiManager::start() {
    if (running_) {
        logger_->warn("UiManager already running");
        return;
    }

    running_ = true;
    uiState_ = UiState::COMMAND_MODE;

    // 启动显示线程
    displayThread_ = std::make_unique<std::thread>(&UiManager::displayThread, this);
    
    // 启动输入线程
    inputThread_ = std::make_unique<std::thread>(&UiManager::inputThread, this);

    logger_->info("UiManager started");

    // 显示欢迎信息
    displayMessage("=== IEC 60870-5-103 消息客户端 ===");
    displayMessage("连接到服务器: " + serverAddr_);
    showHelp();
}

void UiManager::stop() {
    if (!running_) {
        return;
    }

    running_ = false;

    // 通知显示线程退出
    {
        std::lock_guard<std::mutex> lock(queueMutex_);
        messageQueue_.push(""); // 空消息作为退出信号
    }
    queueCv_.notify_all();

    // 等待线程结束
    if (displayThread_ && displayThread_->joinable()) {
        displayThread_->join();
    }
    if (inputThread_ && inputThread_->joinable()) {
        inputThread_->join();
    }

    logger_->info("UiManager stopped");
}

void UiManager::displayMessage(const std::string& message) {
    {
        std::lock_guard<std::mutex> lock(queueMutex_);
        messageQueue_.push(message);
    }
    queueCv_.notify_one();
}

void UiManager::updatePluginMenu(uint8_t pluginId, const nlohmann::json& pluginInfo) {
    std::lock_guard<std::mutex> lock(menuMutex_);
    
    try {
        currentMenu_.pluginId = pluginId;
        currentMenu_.pluginInfo = pluginInfo;
        
        auto& available = pluginInfo["available_messages"];
        currentMenu_.choiceCount = available.is_array() ? available.size() : 0;
        currentMenu_.valid = currentMenu_.choiceCount > 0;
        
        if (currentMenu_.valid) {
            uiState_ = UiState::MENU_MODE;
            
            std::string pluginName = pluginInfo.value("plugin_name", std::to_string(pluginId));
            displayMessage("\n插件 [" + pluginName + "] 可用操作：");
            
            for (size_t i = 0; i < currentMenu_.choiceCount; ++i) {
                const auto& item = pluginInfo["available_messages"][i];
                std::string name = item.value("name", "未知操作");
                std::string desc = item.value("description", "");
                
                std::string menuItem = "  " + std::to_string(i + 1) + ". " + name;
                if (!desc.empty()) {
                    menuItem += " - " + desc;
                }
                displayMessage(menuItem);
            }
            displayMessage("请选择操作 (1-" + std::to_string(currentMenu_.choiceCount) + "): ");
        } else {
            displayMessage("插件信息无可用操作");
        }
    } catch (const std::exception& e) {
        logger_->error("Failed to update plugin menu: {}", e.what());
        displayMessage("插件信息解析错误");
        currentMenu_.valid = false;
    }
}

void UiManager::setQueryCallback(const QueryCallback& callback) {
    queryCallback_ = callback;
}

void UiManager::setOperationCallback(const OperationCallback& callback) {
    operationCallback_ = callback;
}

void UiManager::setCustomMessageCallback(const CustomMessageCallback& callback) {
    customMessageCallback_ = callback;
}

void UiManager::inputThread() {
    logger_->info("UI input thread started");

    std::string line;
    while (running_) {
        std::cout << "\n> ";
        if (!std::getline(std::cin, line)) {
            break; // EOF
        }
        
        if (line.empty()) continue;

        // 根据状态机分支处理
        auto state = uiState_.load();
        if (state == UiState::MENU_MODE) {
            try {
                int choice = std::stoi(line);
                
                std::lock_guard<std::mutex> lock(menuMutex_);
                if (!currentMenu_.valid || choice < 1 || (size_t)choice > currentMenu_.choiceCount) {
                    displayMessage("无效选择，请输入 1-" + std::to_string(currentMenu_.choiceCount));
                    continue;
                }

                size_t operationIndex = choice - 1;
                
                // 检查是否需要输入参数
                const auto& selected = currentMenu_.pluginInfo["available_messages"][operationIndex];
                std::string content = "";
                
                if (selected.contains("variable_parts") && selected["variable_parts"].is_array()) {
                    std::cout << "\n请输入消息内容: ";
                    std::getline(std::cin, content);
                    
                    content.erase(0, content.find_first_not_of(" \t"));
                    content.erase(content.find_last_not_of(" \t") + 1);
                }

                // 调用操作回调
                if (operationCallback_) {
                    operationCallback_(currentMenu_.pluginId, operationIndex, content);
                }

                uiState_ = UiState::COMMAND_MODE;
                continue;
                
            } catch (...) {
                displayMessage("无效输入，请输入数字选项");
                continue;
            }
        }

        // 命令模式处理
        if (line == "quit" || line == "exit" || line == "q") {
            break;
        } else if (line == "help" || line == "h") {
            showHelp();
        } else if (line.length() >= 6 && line.substr(0, 6) == "query ") {
            // 查询插件信息: query <plugin_id>
            try {
                uint8_t pluginId = static_cast<uint8_t>(std::stoi(line.substr(6)));
                if (queryCallback_) {
                    queryCallback_(pluginId);
                    displayMessage("已发送插件信息查询请求");
                }
            } catch (...) {
                displayMessage("错误: 无效的插件ID");
            }
        } else if (line == "send" || line == "new" || line == "m") {
            createCustomMessage();
        } else {
            displayMessage("未知命令: " + line);
            displayMessage("输入 'help' 查看可用命令");
        }
    }

    displayMessage("用户交互结束");
    logger_->info("UI input thread ended");
}

void UiManager::displayThread() {
    logger_->info("UI display thread started");

    while (running_) {
        std::unique_lock<std::mutex> lock(queueMutex_);
        queueCv_.wait(lock, [this] { return !messageQueue_.empty() || !running_; });

        while (!messageQueue_.empty()) {
            std::string message = messageQueue_.front();
            messageQueue_.pop();
            lock.unlock();

            // 空消息是退出信号
            if (message.empty() && !running_) {
                break;
            }

            // 显示消息
            std::cout << message;
            if (!message.empty() && message.back() != '\n') {
                std::cout << "\n";
            }
            std::cout.flush();

            lock.lock();
        }
    }

    logger_->info("UI display thread ended");
}

void UiManager::showHelp()  {
    displayMessage("\n可用命令:");
    displayMessage("  send, new, m  - 创建并发送新消息");
    displayMessage("  query <id>    - 查询插件信息 (例如: query 1)");
    displayMessage("  help, h       - 显示此帮助信息");
    displayMessage("  quit, exit, q - 退出程序");
}

void UiManager::handlePluginMenu(uint8_t pluginId, const nlohmann::json& pluginInfo) {
    // 这个方法已经通过updatePluginMenu实现
    updatePluginMenu(pluginId, pluginInfo);
}

void UiManager::createCustomMessage() {
    displayMessage("\n=== 创建新消息 ===");

    // 设置基本字段
    uint8_t typ = readNumber<uint8_t>("类型标识 (TYP) [1-255]", 1);
    uint8_t vsq = readNumber<uint8_t>("可变结构限定词 (VSQ) [0-255]", 1);
    uint8_t cot = readNumber<uint8_t>("传送原因 (COT) [1-255]", 6);
    uint8_t target = readNumber<uint8_t>("目标地址 (Target) [1-255]", 255);
    uint8_t fun = readNumber<uint8_t>("功能类型 (FUN) [0-255]", 1);
    uint8_t inf = readNumber<uint8_t>("信息序号 (INF) [0-255]", 1);

    // 设置可变结构体内容
    std::string content = readString("可变结构体内容 (字符串，留空为无内容)", "");

    // 显示消息摘要
    displayMessage("\n=== 消息摘要 ===");
    displayMessage("TYP: " + std::to_string(typ));
    displayMessage("VSQ: " + std::to_string(vsq));
    displayMessage("COT: " + std::to_string(cot));
    displayMessage("Target: " + std::to_string(target));
    displayMessage("FUN: " + std::to_string(fun));
    displayMessage("INF: " + std::to_string(inf));
    displayMessage("Content: \"" + content + "\"");

    std::string confirm = readString("确认发送此消息? (y/n)", "y");
    if (confirm == "y" || confirm == "Y" || confirm == "yes") {
        if (customMessageCallback_) {
            customMessageCallback_(typ, vsq, cot, target, fun, inf, content);
            displayMessage("消息发送成功");
        }
    } else {
        displayMessage("消息创建已取消");
    }
}

template<typename T>
T UiManager::readNumber(const std::string& prompt, T defaultValue) {
    std::cout << prompt << " [默认: " << static_cast<int>(defaultValue) << "]: ";
    std::string line;
    if (std::getline(std::cin, line) && !line.empty()) {
        try {
            int value = std::stoi(line);
            if (value >= std::numeric_limits<T>::min() &&
                value <= std::numeric_limits<T>::max()) {
                return static_cast<T>(value);
            }
        } catch (...) {
            // 解析失败，使用默认值
        }
    }
    return defaultValue;
}

std::string UiManager::readString(const std::string& prompt, const std::string& defaultValue) {
    std::cout << prompt;
    if (!defaultValue.empty()) {
        std::cout << " [默认: " << defaultValue << "]";
    }
    std::cout << ": ";

    std::string line;
    if (std::getline(std::cin, line) && !line.empty()) {
        return line;
    }
    return defaultValue;
}

} // namespace client
} // namespace zexuan

/**
 * @file message_client.cpp
 * @brief 基于IEC 60870-5-103协议的消息客户端实现
 * <AUTHOR>
 * @date 2024
 */

#include "../include/message_client.hpp"
#include "zexuan/base/message_types.hpp"
#include "zexuan/logger.hpp"
#include <nlohmann/json.hpp>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <limits>
#include <cstring>

namespace zexuan {
namespace client {

MessageClient::MessageClient(const std::string& configPath,
                           const std::string& serverHost,
                           uint16_t serverPort)
    : serverAddr_(serverHost, serverPort)
    , connected_(false)
    , running_(false)
    , interactiveMode_(false)
    , clientId_(233)  // 固定客户端ID为233
    , logger_(zexuan::Logger::getFileLogger("MessageClient")) {

    logger_->info("MessageClient created - Server: {}:{}", serverHost, serverPort);
    // 简化客户端：移除配置依赖，插件可用性通过 query <id> 动态查询
}

MessageClient::~MessageClient() {
    stop();
    logger_->info("MessageClient destroyed");
}


bool MessageClient::initialize() {
    try {
        logger_->info("MessageClient initialized successfully");
        return true;

    } catch (const std::exception& e) {
        logger_->error("Failed to initialize MessageClient: {}", e.what());
        return false;
    }
}

bool MessageClient::connect() {
    try {
        // 启动事件循环线程
        running_ = true;
        eventLoopThread_ = std::make_unique<std::thread>(&MessageClient::eventLoopThread, this);

        // 等待EventLoop和Client在事件循环线程中初始化完成
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        // 等待连接建立（简单的轮询方式）
        int timeout = 5000; // 5秒超时
        while (!connected_ && timeout > 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            timeout -= 10;
        }

        if (connected_) {
            logger_->info("Connected to server successfully");
            return true;
        } else {
            logger_->error("Connection timeout");
            return false;
        }

    } catch (const std::exception& e) {
        logger_->error("Connection failed: {}", e.what());
        return false;
    }
}

void MessageClient::disconnect() {
    if (client_ && connected_) {
        // 在事件循环线程中执行断连操作
        if (loop_) {
            loop_->runInLoop([this]() {
                if (client_) {
                    client_->disconnect();
                }
            });
        }
        connected_ = false;
        logger_->info("Disconnected from server");
    }
}

void MessageClient::run() {
    if (!initialize()) {
        logger_->error("Failed to initialize client");
        return;
    }

    if (!connect()) {
        logger_->error("Failed to connect to server");
        return;
    }

    // 进入交互模式，初始为命令模式
    uiState_.store(UiState::COMMAND_MODE);
    interactiveMode_ = true;
    handleUserInput();
}

void MessageClient::stop() {
    running_ = false;
    interactiveMode_ = false;

    disconnect();

    if (loop_) {
        // 在事件循环线程中退出循环
        loop_->runInLoop([this]() {
            loop_->quit();
        });
    }

    if (eventLoopThread_ && eventLoopThread_->joinable()) {
        eventLoopThread_->join();
    }

    logger_->info("MessageClient stopped");
}

bool MessageClient::sendMessage(const zexuan::base::Message& message) {
    if (!connected_ || !connection_) {
        logger_->error("Not connected to server");
        return false;
    }

    try {
        // 序列化消息
        std::vector<uint8_t> data;
        message.serialize(data);

        // 发送消息
        connection_->send(reinterpret_cast<const char*>(data.data()), data.size());

        logger_->debug("Message sent successfully, size: {} bytes", data.size());
        return true;

    } catch (const std::exception& e) {
        logger_->error("Failed to send message: {}", e.what());
        return false;
    }
}

bool MessageClient::queryPluginInfo(uint8_t pluginId) {
    if (!connected_) {
        logger_->error("Not connected to server");
        return false;
    }

    // 创建INFO_REQUEST消息
    zexuan::base::Message request;
    request.setTyp(0x01);
    request.setVsq(0x81);
    request.setCot(static_cast<uint8_t>(zexuan::base::CauseOfTransmission::INFO_REQUEST));
    request.setSource(clientId_);
    request.setTarget(pluginId);
    request.setFun(0x00);
    request.setInf(0x01);

    logger_->info("Querying plugin info for plugin ID: {}", static_cast<int>(pluginId));
    return sendMessage(request);
}

std::string MessageClient::getPluginInfo(uint8_t pluginId) const {
    std::lock_guard<std::mutex> lock(pluginInfoMutex_);
    auto it = pluginInfoCache_.find(pluginId);
    if (it != pluginInfoCache_.end()) {
        return it->second;
    }
    return "";
}

std::unique_ptr<zexuan::base::Message> MessageClient::createInteractiveMessage() {
    auto message = std::make_unique<zexuan::base::Message>();

    std::cout << "\n=== 创建新消息 ===\n";

    // 设置基本字段
    uint8_t typ = readNumber<uint8_t>("类型标识 (TYP) [1-255]", 1);
    message->setTyp(typ);

    uint8_t vsq = readNumber<uint8_t>("可变结构限定词 (VSQ) [0-255]", 1);
    message->setVsq(vsq);

    uint8_t cot = readNumber<uint8_t>("传送原因 (COT) [1-255]", 6);
    message->setCot(cot);

    // 自动设置客户端ID
    message->setSource(clientId_);
    std::cout << "源地址 (Source): " << static_cast<int>(clientId_) << " (自动设置)\n";

    // 设置目标地址
    std::cout << "\n提示：客户端已不依赖本地配置，请直接输入插件ID（或255为广播）\n";

    std::string targetChoice = readString("选择目标 (直接输入ID，如 1 或 255)", "");
    uint8_t targetId = 255; // 默认广播地址

    if (!targetChoice.empty()) {
        try {
            int id = std::stoi(targetChoice);
            if (id >= 0 && id <= 255) {
                targetId = static_cast<uint8_t>(id);
                std::cout << "已设置目标ID: " << static_cast<int>(targetId) << "\n";
            } else {
                std::cout << "无效的目标ID，使用广播地址 255\n";
            }
        } catch (...) {
            std::cout << "无效输入，使用广播地址 255\n";
        }
    }
    message->setTarget(targetId);

    uint8_t fun = readNumber<uint8_t>("功能类型 (FUN) [0-255]", 1);
    message->setFun(fun);

    uint8_t inf = readNumber<uint8_t>("信息序号 (INF) [0-255]", 1);
    message->setInf(inf);

    // 设置可变结构体内容
    std::string variableContent = readString("可变结构体内容 (字符串，留空为无内容)", "");
    if (!variableContent.empty()) {
        message->setTextContent(variableContent);
        std::cout << "设置可变结构体: \"" << variableContent << "\"\n";
    }

    // 显示消息摘要
    std::cout << "\n=== 消息摘要 ===\n";
    std::cout << "TYP: " << static_cast<int>(message->getTyp()) << "\n";
    std::cout << "VSQ: " << static_cast<int>(message->getVsq()) << "\n";
    std::cout << "COT: " << static_cast<int>(message->getCot()) << "\n";
    std::cout << "Source: " << static_cast<int>(message->getSource()) << "\n";
    std::cout << "Target: " << static_cast<int>(message->getTarget()) << "\n";
    std::cout << "FUN: " << static_cast<int>(message->getFun()) << "\n";
    std::cout << "INF: " << static_cast<int>(message->getInf()) << "\n";
    std::cout << "Content: \"" << message->getTextContent() << "\"\n";
    std::cout << "Total Size: " << message->getMessageSize() << " bytes\n";

    std::string confirm = readString("确认发送此消息? (y/n)", "y");
    if (confirm == "y" || confirm == "Y" || confirm == "yes") {
        return message;
    }

    return nullptr;
}

void MessageClient::showAvailableTargets() const {
    std::cout << "(客户端已取消本地插件配置缓存，请使用 query <id> 直接查询服务器)\n";
}

uint8_t MessageClient::getPluginId(const std::string& pluginName) const {
    // 客户端已不维护本地插件表；返回广播或提示用户使用 query
    return 255;
}

void MessageClient::onConnection(const zexuan::net::TcpConnectionPtr& conn) {
    if (conn->connected()) {
        logger_->info("Connected to {}", conn->peerAddress().toIpPort());
        connection_ = conn;
        connected_ = true;

        // 固定客户端ID为233
        clientId_ = 233;

        logger_->info("Assigned client ID: {}", static_cast<int>(clientId_));
        std::cout << "已连接服务器，客户端ID: " << static_cast<int>(clientId_) << "\n";
    } else {
        logger_->info("Disconnected from {}", conn->peerAddress().toIpPort());
        connection_.reset();
        connected_ = false;
        clientId_ = 233; // 保持客户端ID为233
    }
}

void MessageClient::onMessage(const zexuan::net::TcpConnectionPtr& conn,
                             zexuan::net::Buffer* buffer,
                             zexuan::net::Timestamp receiveTime) {
    // 处理接收到的消息
    size_t len = buffer->readableBytes();
    if (len > 0) {
        std::vector<uint8_t> data(len);
        memcpy(data.data(), buffer->peek(), len);
        buffer->retrieveAll();

        logger_->info("Received {} bytes from server", len);

        // 尝试解析为Message
        zexuan::base::Message receivedMsg;
        size_t parsedBytes = receivedMsg.deserialize(data);

        if (parsedBytes > 0) {
            std::cout << "\n收到服务器消息:\n";
            std::cout << "  TYP: " << static_cast<int>(receivedMsg.getTyp()) << "\n";
            std::cout << "  VSQ: " << static_cast<int>(receivedMsg.getVsq()) << "\n";
            std::cout << "  COT: " << static_cast<int>(receivedMsg.getCot()) << "\n";
            std::cout << "  Source: " << static_cast<int>(receivedMsg.getSource()) << "\n";
            std::cout << "  Target: " << static_cast<int>(receivedMsg.getTarget()) << "\n";
            std::cout << "  FUN: " << static_cast<int>(receivedMsg.getFun()) << "\n";
            std::cout << "  INF: " << static_cast<int>(receivedMsg.getInf()) << "\n";
            std::cout << "  Content: \"" << receivedMsg.getTextContent() << "\"\n";

            // 处理INFO_RESPONSE消息
            if (receivedMsg.getCot() == static_cast<uint8_t>(zexuan::base::CauseOfTransmission::INFO_RESPONSE)) {
                uint8_t pluginId = receivedMsg.getSource();
                std::string pluginInfo = receivedMsg.getTextContent();

                // 缓存插件信息
                {
                    std::lock_guard<std::mutex> lock(pluginInfoMutex_);
                    pluginInfoCache_[pluginId] = pluginInfo;
                }

                std::cout << "\n✓ 收到插件 " << static_cast<int>(pluginId) << " 的信息\n";
                // 切换到菜单模式，由handleUserInput处理数字输入
                try {
                    currentMenu_.pluginId = pluginId;
                    currentMenu_.infoJson = pluginInfo;
                    nlohmann::json info = nlohmann::json::parse(pluginInfo);
                    auto& available = info["available_messages"];
                    currentMenu_.choiceCount = available.is_array() ? available.size() : 0;
                    currentMenu_.valid = currentMenu_.choiceCount > 0;
                    if (currentMenu_.valid) {
                        uiState_.store(UiState::MENU_MODE);
                        std::string pluginName = info.value("plugin_name", std::to_string(pluginId));
                        std::cout << "\n插件 [" << pluginName << "] 可用操作：\n";
                        for (size_t i = 0; i < currentMenu_.choiceCount; ++i) {
                            const auto& item = info["available_messages"][i];
                            std::string name = item.value("name", "未知操作");
                            std::string desc = item.value("description", "");
                            std::cout << "  " << (i + 1) << ". " << name;
                            if (!desc.empty()) std::cout << " - " << desc;
                            std::cout << "\n";
                        }
                        std::cout << "请选择操作 (1-" << currentMenu_.choiceCount << "): ";
                    }
                } catch (const std::exception& e) {
                    logger_->error("解析插件信息JSON失败: {}", e.what());
                    currentMenu_.valid = false;
                }
                if (!currentMenu_.valid) {
                    std::cout << "插件信息无可用操作\n";
                }
            }
        } else {
            std::cout << "\n收到原始数据 (" << len << " bytes):\n  ";
            for (size_t i = 0; i < len && i < 32; ++i) {
                std::cout << std::hex << std::setw(2) << std::setfill('0')
                         << static_cast<int>(data[i]) << " ";
            }
            if (len > 32) std::cout << "...";
            std::cout << std::dec << "\n";
        }

        if (interactiveMode_) {
            std::cout << "\n> ";  // 重新显示提示符
            std::cout.flush();
        }
    }
}

void MessageClient::onWriteComplete(const zexuan::net::TcpConnectionPtr& conn) {
    logger_->debug("Write completed to {}", conn->peerAddress().toIpPort());
}

void MessageClient::eventLoopThread() {
    logger_->info("Event loop thread started");

    try {
        // 在事件循环线程中创建EventLoop
        loop_ = std::make_unique<zexuan::net::EventLoop>();

        // 在事件循环线程中创建TCP客户端
        client_ = std::make_unique<zexuan::net::TcpClient>(loop_.get(), serverAddr_, "MessageClient");

        // 设置回调函数
        client_->setConnectionCallback(
            std::bind(&MessageClient::onConnection, this, std::placeholders::_1));
        client_->setMessageCallback(
            std::bind(&MessageClient::onMessage, this, std::placeholders::_1,
                     std::placeholders::_2, std::placeholders::_3));
        client_->setWriteCompleteCallback(
            std::bind(&MessageClient::onWriteComplete, this, std::placeholders::_1));

        // 启动连接
        client_->connect();

        // 运行事件循环
        loop_->loop();

    } catch (const std::exception& e) {
        logger_->error("Event loop exception: {}", e.what());
    }

    logger_->info("Event loop thread ended");
}

void MessageClient::handleUserInput() {
    std::cout << "=== IEC 60870-5-103 消息客户端 ===\n";
    std::cout << "连接到服务器: " << serverAddr_.toIpPort() << "\n";
    showHelp();

    std::string line;
    while (interactiveMode_ && running_) {
        std::cout << "\n> ";
        if (!std::getline(std::cin, line)) {
            break; // EOF
        }
        if (line.empty()) continue;

        // 根据状态机分支处理
        auto state = uiState_.load();
        if (state == UiState::MENU_MODE) {
            try {
                int choice = std::stoi(line);
                if (!currentMenu_.valid || choice < 1 || (size_t)choice > currentMenu_.choiceCount) {
                    std::cout << "无效选择，请输入 1-" << currentMenu_.choiceCount << "\n";
                    continue;
                }
                nlohmann::json info = nlohmann::json::parse(currentMenu_.infoJson);
                const auto& selected = info["available_messages"][choice - 1];
                auto tmpl = selected["message_template"];

                zexuan::base::Message msg;
                msg.setTyp(static_cast<uint8_t>(tmpl.value("typ", 1)));
                msg.setVsq(static_cast<uint8_t>(tmpl.value("vsq", 0x81)));
                msg.setCot(static_cast<uint8_t>(tmpl.value("cot", 0x06)));
                msg.setSource(clientId_);
                msg.setTarget(currentMenu_.pluginId);  // 直接使用当前菜单的插件ID作为目标
                msg.setFun(static_cast<uint8_t>(tmpl.value("fun", 0x00)));
                msg.setInf(static_cast<uint8_t>(tmpl.value("inf", 0x01)));

                if (selected.contains("variable_parts") && selected["variable_parts"].is_array()) {
                    std::cout << "\n请输入message：";
                    std::string directoryPath;
                    std::getline(std::cin, directoryPath);
                    
                    // 去除路径前后的空白字符
                    directoryPath.erase(0, directoryPath.find_first_not_of(" \t"));
                    directoryPath.erase(directoryPath.find_last_not_of(" \t") + 1);
                    
                    if (!directoryPath.empty()) {
                        msg.setTextContent(directoryPath);
                    } else {
                        std::cout << "未输入目录路径，取消操作\n";
                        uiState_.store(UiState::COMMAND_MODE);
                        std::cout << "> ";
                        return;
                    }
                }

                if (sendMessage(msg)) {
                    std::cout << "已发送操作请求\n";
                } else {
                    std::cout << "发送失败\n";
                }

                uiState_.store(UiState::COMMAND_MODE);
                std::cout << "> ";
                continue;
            } catch (...) {
                std::cout << "无效输入，请输入数字选项。\n";
                continue;
            }
        }

        // 调试信息
        logger_->debug("Received command: '{}', length: {}", line, line.length());

        if (line == "quit" || line == "exit" || line == "q") {
            break;
        } else if (line == "help" || line == "h") {
            showHelp();
        } else if (line == "status" || line == "s") {
            std::cout << "连接状态: " << (connected_ ? "已连接" : "未连接") << "\n";
            std::cout << "客户端ID: " << static_cast<int>(clientId_) << "\n";
            std::cout << "已缓存插件信息数: " << pluginInfoCache_.size() << "\n";
        } else if (line == "plugins" || line == "p") {
            std::cout << "\n提示：客户端已不依赖本地配置，直接使用 query <id> 查询插件是否可用\n";
        } else if (line.length() >= 6 && line.substr(0, 6) == "query ") {
            // 查询插件信息: query <plugin_id>
            logger_->debug("Processing query command");
            if (!connected_) {
                std::cout << "错误: 未连接到服务器\n";
                continue;
            }

            try {
                uint8_t pluginId = static_cast<uint8_t>(std::stoi(line.substr(6)));
                logger_->debug("Querying plugin ID: {}", static_cast<int>(pluginId));
                if (queryPluginInfo(pluginId)) {
                    std::cout << "已发送插件信息查询请求\n";
                } else {
                    std::cout << "发送查询请求失败\n";
                }
            } catch (...) {
                std::cout << "错误: 无效的插件ID\n";
            }
        } else if (line.length() >= 5 && line.substr(0, 5) == "info ") {
            // 显示插件信息: info <plugin_id>
            logger_->debug("Processing info command");
            try {
                uint8_t pluginId = static_cast<uint8_t>(std::stoi(line.substr(5)));
                logger_->debug("Showing info for plugin ID: {}", static_cast<int>(pluginId));
                std::string pluginInfo = getPluginInfo(pluginId);
                if (!pluginInfo.empty()) {
                    std::cout << "\n插件 " << static_cast<int>(pluginId) << " 信息:\n";
                    std::cout << pluginInfo << "\n";
                } else {
                    std::cout << "未找到插件 " << static_cast<int>(pluginId) << " 的信息\n";
                    std::cout << "请先使用 'query " << static_cast<int>(pluginId) << "' 查询插件信息\n";
                }
            } catch (...) {
                std::cout << "错误: 无效的插件ID\n";
            }
        } else if (line == "send" || line == "new" || line == "m") {
            if (!connected_) {
                std::cout << "错误: 未连接到服务器\n";
                continue;
            }

            auto message = createInteractiveMessage();
            if (message) {
                if (sendMessage(*message)) {
                    std::cout << "消息发送成功\n";
                } else {
                    std::cout << "消息发送失败\n";
                }
            } else {
                std::cout << "消息创建已取消\n";
            }
        } else {
            std::cout << "未知命令: " << line << "\n";
            std::cout << "输入 'help' 查看可用命令\n";
        }
    }

    std::cout << "用户交互结束\n";
}

void MessageClient::showHelp() const {
    std::cout << "\n可用命令:\n";
    std::cout << "  send, new, m  - 创建并发送新消息\n";
    std::cout << "  query <id>    - 查询插件信息 (例如: query 1)\n";
    std::cout << "  info <id>     - 显示插件信息 (例如: info 1)\n";
    std::cout << "  status, s     - 显示连接状态\n";
    std::cout << "  plugins, p    - 显示可用插件列表\n";
    std::cout << "  help, h       - 显示此帮助信息\n";
    std::cout << "  quit, exit, q - 退出程序\n";
}

template<typename T>
T MessageClient::readNumber(const std::string& prompt, T defaultValue) {
    std::cout << prompt << " [默认: " << static_cast<int>(defaultValue) << "]: ";
    std::string line;
    if (std::getline(std::cin, line) && !line.empty()) {
        try {
            int value = std::stoi(line);
            if (value >= std::numeric_limits<T>::min() &&
                value <= std::numeric_limits<T>::max()) {
                return static_cast<T>(value);
            }
        } catch (...) {
            // 解析失败，使用默认值
        }
    }
    return defaultValue;
}

std::string MessageClient::readString(const std::string& prompt, const std::string& defaultValue) {
    std::cout << prompt;
    if (!defaultValue.empty()) {
        std::cout << " [默认: " << defaultValue << "]";
    }
    std::cout << ": ";

    std::string line;
    if (std::getline(std::cin, line) && !line.empty()) {
        return line;
    }
    return defaultValue;

}

void MessageClient::runPluginMenu(uint8_t pluginId) {
    // 读取缓存的JSON
    std::string jsonStr;
    {
        std::lock_guard<std::mutex> lock(pluginInfoMutex_);
        auto it = pluginInfoCache_.find(pluginId);
        if (it == pluginInfoCache_.end()) {
            std::cout << "未找到插件 " << static_cast<int>(pluginId) << " 的信息，请先执行 query 命令\n";
            return;
        }
        jsonStr = it->second;
    }

    try {
        nlohmann::json info = nlohmann::json::parse(jsonStr);
        std::string pluginName = info.value("plugin_name", std::to_string(pluginId));
        auto available = info["available_messages"];
        if (!available.is_array() || available.empty()) {
            std::cout << "插件 " << pluginName << " 没有可用操作\n";
            return;
        }

        std::cout << "\n插件 [" << pluginName << "] 可用操作：\n";
        for (size_t i = 0; i < available.size(); ++i) {
            const auto& item = available[i];
            std::string name = item.value("name", "未知操作");
            std::string desc = item.value("description", "");
            std::cout << "  " << (i + 1) << ". " << name;
            if (!desc.empty()) std::cout << " - " << desc;
            std::cout << "\n";
        }

        int choice = 0;
        while (true) {
            std::cout << "请选择操作 (1-" << available.size() << "): ";
            std::string line;
            if (!std::getline(std::cin, line)) return; // EOF
            try {
                choice = std::stoi(line);
                if (choice >= 1 && static_cast<size_t>(choice) <= available.size()) break;
            } catch (...) {}
            std::cout << "无效选择，请重试。\n";
        }

        const auto& selected = available[choice - 1];
        auto tmpl = selected["message_template"];

        // 构造消息
        zexuan::base::Message msg;
        msg.setTyp(static_cast<uint8_t>(tmpl.value("typ", 1)));
        msg.setVsq(static_cast<uint8_t>(tmpl.value("vsq", 0x81)));
        msg.setCot(static_cast<uint8_t>(tmpl.value("cot", 0x06)));
        msg.setSource(clientId_);
        msg.setTarget(pluginId);  // 直接使用查询到的插件ID作为目标
        msg.setFun(static_cast<uint8_t>(tmpl.value("fun", 0x00)));
        msg.setInf(static_cast<uint8_t>(tmpl.value("inf", 0x01)));

        // 可变参数简单通过文本内容承载：直接输入路径
        if (selected.contains("variable_parts") && selected["variable_parts"].is_array()) {
            std::cout << "\n请输入目录路径：";
            std::string directoryPath;
            std::getline(std::cin, directoryPath);
            
            // 去除路径前后的空白字符
            directoryPath.erase(0, directoryPath.find_first_not_of(" \t"));
            directoryPath.erase(directoryPath.find_last_not_of(" \t") + 1);
            
            if (!directoryPath.empty()) {
                msg.setTextContent(directoryPath);
            } else {
                std::cout << "未输入目录路径，取消操作\n";
                uiState_.store(UiState::COMMAND_MODE);
                std::cout << "> ";
                return;
            }
        }

        if (sendMessage(msg)) {
            std::cout << "已发送操作请求给插件 " << pluginName << "\n";
        } else {
            std::cout << "发送失败\n";
        }
    } catch (const std::exception& e) {
        logger_->error("解析/生成菜单失败: {}", e.what());
        std::cout << "解析插件信息失败\n";
    }
}


} // namespace client
} // namespace zexuan

/**
 * @file main.cpp
 * @brief 分层架构的消息客户端主程序
 * <AUTHOR>
 * @date 2024
 */

#include "../include/network_manager.hpp"
#include "../include/message_handler.hpp"
#include "../include/ui_manager.hpp"
#include "../include/system_manager.hpp"
#include "zexuan/logger.hpp"
#include <iostream>
#include <memory>
#include <thread>
#include <chrono>

int main(int argc, char* argv[]) {
    auto logger = zexuan::Logger::getFileLogger("MessageClient");
    logger->info("Layered IEC 60870-5-103 Message Client starting...");

    try {
        // 解析命令行参数
        std::string serverHost = "127.0.0.1";
        uint16_t serverPort = 3414;
        uint8_t clientId = 233;

        if (argc >= 2) {
            serverHost = argv[1];
        }
        if (argc >= 3) {
            serverPort = static_cast<uint16_t>(std::stoi(argv[2]));
        }
        if (argc >= 4) {
            clientId = static_cast<uint8_t>(std::stoi(argv[3]));
        }

        std::string serverAddr = serverHost + ":" + std::to_string(serverPort);
        
        std::cout << "使用配置:\n";
        std::cout << "  服务器地址: " << serverAddr << "\n";
        std::cout << "  客户端ID: " << static_cast<int>(clientId) << "\n\n";

        // 创建系统管理器并初始化信号处理
        auto systemManager = std::make_unique<zexuan::client::SystemManager>();
        systemManager->initializeSignalHandling();

        // 创建各层组件（使用shared_ptr以便SystemManager管理）
        auto networkManager = std::make_shared<zexuan::client::NetworkManager>(serverHost, serverPort);
        auto messageHandler = std::make_unique<zexuan::client::MessageHandler>(clientId);
        auto uiManager = std::make_shared<zexuan::client::UiManager>(serverAddr);

        // 设置SystemManager的组件引用
        systemManager->setComponents(networkManager, uiManager);

        // 设置组件间的回调连接

        // Network -> Message: 网络接收数据传给消息处理器
        networkManager->setMessageCallback([&](const std::vector<uint8_t>& data) {
            messageHandler->handleRawData(data);
        });

        // Network -> UI: 连接状态变化通知UI
        networkManager->setConnectionCallback([&](bool connected) {
            if (connected) {
                uiManager->displayMessage("✓ 已连接到服务器，客户端ID: " + std::to_string(clientId));
            } else {
                uiManager->displayMessage("✗ 与服务器断开连接");
            }
        });

        // Message -> UI: 消息更新传给UI显示
        messageHandler->setUiUpdateCallback([&](const std::string& message) {
            uiManager->displayMessage(message);
        });

        // Message -> UI: 插件菜单更新
        messageHandler->setMenuUpdateCallback([&](uint8_t pluginId, const nlohmann::json& pluginInfo) {
            uiManager->updatePluginMenu(pluginId, pluginInfo);
        });

        // UI -> Message: 查询插件请求
        uiManager->setQueryCallback([&](uint8_t pluginId) {
            auto data = messageHandler->createPluginQueryMessage(pluginId);
            if (!data.empty()) {
                networkManager->sendData(data);
            }
        });

        // UI -> Message: 操作请求
        uiManager->setOperationCallback([&](uint8_t pluginId, size_t operationIndex, const std::string& content) {
            auto data = messageHandler->createOperationMessage(pluginId, operationIndex, content);
            if (!data.empty()) {
                networkManager->sendData(data);
                uiManager->displayMessage("已发送操作请求");
            } else {
                uiManager->displayMessage("操作请求创建失败");
            }
        });

        // UI -> Message: 自定义消息请求
        uiManager->setCustomMessageCallback([&](uint8_t typ, uint8_t vsq, uint8_t cot, 
                                                   uint8_t target, uint8_t fun, uint8_t inf, 
                                                   const std::string& content) {
            auto data = messageHandler->createCustomMessage(typ, vsq, cot, target, fun, inf, content);
            if (!data.empty()) {
                networkManager->sendData(data);
            }
        });

        // 启动各层组件
        logger->info("Starting layered components...");

        // 1. 启动网络连接
        if (!networkManager->connect()) {
            logger->error("Failed to connect to server");
            std::cerr << "错误: 无法连接到服务器 " << serverAddr << std::endl;
            return -1;
        }

        // 2. 启动UI管理器（这会阻塞直到用户退出）
        uiManager->start();

        // 主循环：等待UI线程结束或收到退出信号
        while (uiManager->isRunning() && !systemManager->shouldExit()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        // 如果是通过信号退出，确保所有组件正确停止
        if (systemManager->shouldExit()) {
            logger->info("Signal received, stopping components...");
        }

        logger->info("Layered Message Client stopped");
        return 0;
        
    } catch (const std::exception& e) {
        logger->error("Exception: {}", e.what());
        std::cerr << "错误: " << e.what() << std::endl;
        return -1;
    }
}

/**
 * @file system_manager.hpp
 * @brief 客户端系统管理器 - 负责信号处理和优雅退出
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_CLIENT_SYSTEM_MANAGER_HPP
#define ZEXUAN_CLIENT_SYSTEM_MANAGER_HPP

#include <memory>
#include <functional>
#include <atomic>
#include "zexuan/logger.hpp"

namespace zexuan {
namespace client {

// 前向声明
class NetworkManager;
class UiManager;

/**
 * @brief 客户端系统管理器 - 负责信号处理和组件管理
 */
class SystemManager {
public:
    // 退出回调函数类型
    using ShutdownCallback = std::function<void()>;

    /**
     * @brief 构造函数
     */
    SystemManager();

    /**
     * @brief 析构函数
     */
    ~SystemManager();

    /**
     * @brief 初始化信号处理
     */
    void initializeSignalHandling();

    /**
     * @brief 设置组件引用（用于优雅退出）
     * @param networkManager 网络管理器
     * @param uiManager UI管理器
     */
    void setComponents(std::shared_ptr<NetworkManager> networkManager,
                       std::shared_ptr<UiManager> uiManager);

    /**
     * @brief 设置自定义退出回调
     * @param callback 退出回调函数
     */
    void setShutdownCallback(const ShutdownCallback& callback);

    /**
     * @brief 检查是否收到退出信号
     * @return true 收到退出信号，false 未收到
     */
    bool shouldExit() const { return shouldExit_; }

    /**
     * @brief 执行优雅退出
     */
    void gracefulShutdown();

private:
    /**
     * @brief 信号处理函数
     * @param sig 信号编号
     */
    static void signalHandler(int sig);

private:
    // 静态实例指针，用于信号处理
    static SystemManager* instance_;

    // 组件引用
    std::weak_ptr<NetworkManager> networkManager_;
    std::weak_ptr<UiManager> uiManager_;

    // 退出控制
    std::atomic<bool> shouldExit_;
    ShutdownCallback shutdownCallback_;

    // 日志
    std::shared_ptr<spdlog::logger> logger_;
};

} // namespace client
} // namespace zexuan

#endif // ZEXUAN_CLIENT_SYSTEM_MANAGER_HPP

/**
 * @file ui_manager.hpp
 * @brief UI管理器 - 负责用户界面交互和流式传输显示
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_CLIENT_UI_MANAGER_HPP
#define ZEXUAN_CLIENT_UI_MANAGER_HPP

#include <memory>
#include <functional>
#include <atomic>
#include <string>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>

#include "zexuan/logger.hpp"
#include <nlohmann/json.hpp>

namespace zexuan {
namespace client {

/**
 * @brief UI管理器 - 简化的流式传输界面
 */
class UiManager {
public:
    // 命令回调函数类型
    using QueryCallback = std::function<void(uint8_t)>;
    using OperationCallback = std::function<void(uint8_t, size_t, const std::string&)>;
    using CustomMessageCallback = std::function<void(uint8_t, uint8_t, uint8_t, uint8_t, uint8_t, uint8_t, const std::string&)>;

    /**
     * @brief 构造函数
     * @param serverAddr 服务器地址字符串（用于显示）
     */
    explicit UiManager(const std::string& serverAddr);

    /**
     * @brief 析构函数
     */
    ~UiManager();

    /**
     * @brief 启动UI管理器
     */
    void start();

    /**
     * @brief 停止UI管理器
     */
    void stop();

    /**
     * @brief 显示消息（流式传输）
     * @param message 要显示的消息
     */
    void displayMessage(const std::string& message);

    /**
     * @brief 更新插件菜单
     * @param pluginId 插件ID
     * @param pluginInfo 插件信息JSON
     */
    void updatePluginMenu(uint8_t pluginId, const nlohmann::json& pluginInfo);

    /**
     * @brief 设置查询回调
     * @param callback 回调函数
     */
    void setQueryCallback(const QueryCallback& callback);

    /**
     * @brief 设置操作回调
     * @param callback 回调函数
     */
    void setOperationCallback(const OperationCallback& callback);

    /**
     * @brief 设置自定义消息回调
     * @param callback 回调函数
     */
    void setCustomMessageCallback(const CustomMessageCallback& callback);

    /**
     * @brief 检查是否正在运行
     * @return true 正在运行，false 已停止
     */
    bool isRunning() const { return running_; }

private:
    /**
     * @brief 用户输入处理线程
     */
    void inputThread();

    /**
     * @brief 消息显示线程
     */
    void displayThread();

    /**
     * @brief 显示帮助信息
     */
    void showHelp();

    /**
     * @brief 处理插件菜单选择
     * @param pluginId 插件ID
     * @param pluginInfo 插件信息
     */
    void handlePluginMenu(uint8_t pluginId, const nlohmann::json& pluginInfo);

    /**
     * @brief 创建自定义消息
     */
    void createCustomMessage();

    /**
     * @brief 读取数值输入
     * @param prompt 提示信息
     * @param defaultValue 默认值
     * @return 用户输入的数值
     */
    template<typename T>
    T readNumber(const std::string& prompt, T defaultValue);

    /**
     * @brief 读取字符串输入
     * @param prompt 提示信息
     * @param defaultValue 默认值
     * @return 用户输入的字符串
     */
    std::string readString(const std::string& prompt, const std::string& defaultValue = "");

private:
    // 基本信息
    std::string serverAddr_;
    
    // 状态控制
    std::atomic<bool> running_;
    std::unique_ptr<std::thread> inputThread_;
    std::unique_ptr<std::thread> displayThread_;

    // UI状态机
    enum class UiState { COMMAND_MODE, MENU_MODE };
    std::atomic<UiState> uiState_;

    // 菜单上下文
    struct MenuContext {
        uint8_t pluginId{0};
        nlohmann::json pluginInfo;
        size_t choiceCount{0};
        bool valid{false};
    };
    MenuContext currentMenu_;
    std::mutex menuMutex_;

    // 消息队列（用于流式显示）
    std::queue<std::string> messageQueue_;
    std::mutex queueMutex_;
    std::condition_variable queueCv_;

    // 回调函数
    QueryCallback queryCallback_;
    OperationCallback operationCallback_;
    CustomMessageCallback customMessageCallback_;

    // 日志
    std::shared_ptr<spdlog::logger> logger_;
};

} // namespace client
} // namespace zexuan

#endif // ZEXUAN_CLIENT_UI_MANAGER_HPP

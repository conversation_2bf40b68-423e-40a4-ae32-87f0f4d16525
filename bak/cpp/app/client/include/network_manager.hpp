/**
 * @file network_manager.hpp
 * @brief 网络管理器 - 负责TCP连接和消息收发
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_CLIENT_NETWORK_MANAGER_HPP
#define ZEXUAN_CLIENT_NETWORK_MANAGER_HPP

#include <memory>
#include <functional>
#include <thread>
#include <atomic>

#include "zexuan/net/TcpClient.h"
#include "zexuan/net/EventLoop.h"
#include "zexuan/net/InetAddress.h"
#include "zexuan/net/Buffer.h"
#include "zexuan/base/message.hpp"
#include "zexuan/logger.hpp"

namespace zexuan {
namespace client {

/**
 * @brief 网络管理器 - 专注于TCP连接和原始消息收发
 */
class NetworkManager {
public:
    // 消息接收回调函数类型
    using MessageCallback = std::function<void(const std::vector<uint8_t>&)>;
    using ConnectionCallback = std::function<void(bool)>;

    /**
     * @brief 构造函数
     * @param serverHost 服务器地址
     * @param serverPort 服务器端口
     */
    NetworkManager(const std::string& serverHost = "127.0.0.1", 
                   uint16_t serverPort = 3414);

    /**
     * @brief 析构函数
     */
    ~NetworkManager();

    /**
     * @brief 连接到服务器
     * @return true 成功，false 失败
     */
    bool connect();

    /**
     * @brief 断开连接
     */
    void disconnect();

    /**
     * @brief 发送原始数据
     * @param data 要发送的数据
     * @return true 成功，false 失败
     */
    bool sendData(const std::vector<uint8_t>& data);

    /**
     * @brief 发送消息
     * @param message 要发送的消息
     * @return true 成功，false 失败
     */
    bool sendMessage(const zexuan::base::Message& message);

    /**
     * @brief 设置消息接收回调
     * @param callback 回调函数
     */
    void setMessageCallback(const MessageCallback& callback);

    /**
     * @brief 设置连接状态回调
     * @param callback 回调函数
     */
    void setConnectionCallback(const ConnectionCallback& callback);

    /**
     * @brief 检查是否已连接
     * @return true 已连接，false 未连接
     */
    bool isConnected() const { return connected_; }

    /**
     * @brief 停止网络管理器
     */
    void stop();

private:
    /**
     * @brief TCP连接回调函数
     * @param conn TCP连接指针
     */
    void onConnection(const zexuan::net::TcpConnectionPtr& conn);

    /**
     * @brief TCP消息回调函数
     * @param conn TCP连接指针
     * @param buffer 接收缓冲区
     * @param receiveTime 接收时间
     */
    void onMessage(const zexuan::net::TcpConnectionPtr& conn,
                   zexuan::net::Buffer* buffer,
                   zexuan::net::Timestamp receiveTime);

    /**
     * @brief 事件循环线程函数
     */
    void eventLoopThread();

private:
    // 网络相关
    std::unique_ptr<zexuan::net::EventLoop> loop_;
    std::unique_ptr<zexuan::net::TcpClient> client_;
    zexuan::net::InetAddress serverAddr_;
    zexuan::net::TcpConnectionPtr connection_;
    
    // 状态控制
    std::atomic<bool> connected_;
    std::atomic<bool> running_;
    std::unique_ptr<std::thread> eventLoopThread_;

    // 回调函数
    MessageCallback messageCallback_;
    ConnectionCallback connectionCallback_;

    // 日志
    std::shared_ptr<spdlog::logger> logger_;
};

} // namespace client
} // namespace zexuan

#endif // ZEXUAN_CLIENT_NETWORK_MANAGER_HPP

/**
 * @file message_client.hpp
 * @brief 基于IEC 60870-5-103协议的消息客户端
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_CLIENT_MESSAGE_CLIENT_HPP
#define ZEXUAN_CLIENT_MESSAGE_CLIENT_HPP

#include <map>
#include <string>
#include <memory>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex> // Added for mutex

#include "zexuan/net/TcpClient.h"
#include "zexuan/net/EventLoop.h"
#include "zexuan/net/InetAddress.h"
#include "zexuan/net/Buffer.h"

#include "zexuan/base/message.hpp"
#include "zexuan/logger.hpp"
#include <spdlog/spdlog.h>

namespace zexuan {
namespace client {

/**
 * @brief 基于IEC 60870-5-103协议的消息客户端
 *
 * 功能特性：
 * - 使用TcpClient连接服务器
 * - 从配置文件读取插件名称和ID映射
 * - 提供交互式消息创建和发送
 * - 支持用户输入可变结构体内容
 */
class MessageClient {
public:
    /**
     * @brief 构造函数
     * @param configPath 配置文件路径
     * @param serverHost 服务器地址
     * @param serverPort 服务器端口
     */
    MessageClient(const std::string& configPath,
                  const std::string& serverHost = "127.0.0.1",
                  uint16_t serverPort = 8080);

    /**
     * @brief 析构函数
     */
    ~MessageClient();

    /**
     * @brief 初始化客户端（加载配置、准备连接）
     * @return true 成功，false 失败
     */
    bool initialize();

    /**
     * @brief 连接到服务器
     * @return true 成功，false 失败
     */
    bool connect();

    /**
     * @brief 断开连接
     */
    void disconnect();

    /**
     * @brief 运行客户端（阻塞式）
     */
    void run();

    /**
     * @brief 停止客户端
     */
    void stop();

    /**
     * @brief 发送消息
     * @param message 要发送的消息
     * @return true 成功，false 失败
     */
    bool sendMessage(const zexuan::base::Message& message);

    /**
     * @brief 创建交互式消息
     * @return 创建的消息，如果用户取消则返回nullptr
     */
    std::unique_ptr<zexuan::base::Message> createInteractiveMessage();

    /**
     * @brief 显示可用的插件列表
     */
    void showAvailableTargets() const;

    /**
     * @brief 获取插件ID by name
     * @param pluginName 插件名称
     * @return 插件ID，如果未找到返回255
     */
    uint8_t getPluginId(const std::string& pluginName) const;

    /**
     * @brief 检查是否已连接
     * @return true 已连接，false 未连接
     */
    bool isConnected() const { return connected_; }

    /**
     * @brief 获取客户端ID
     * @return 客户端ID
     */
    uint8_t getClientId() const { return clientId_; }

    /**
     * @brief 获取插件信息
     * @param pluginId 插件ID
     * @return 插件信息JSON字符串，如果未找到返回空字符串
     */
    std::string getPluginInfo(uint8_t pluginId) const;

    /**
     * @brief 查询插件信息
     * @param pluginId 插件ID
     * @return true 成功发送查询请求，false 失败
     */
    bool queryPluginInfo(uint8_t pluginId);

private:

    /**
     * @brief 连接回调函数
     * @param conn TCP连接指针
     */
    void onConnection(const zexuan::net::TcpConnectionPtr& conn);

    /**
     * @brief 消息回调函数
     * @param conn TCP连接指针
     * @param buffer 接收缓冲区
     * @param receiveTime 接收时间
     */
    void onMessage(const zexuan::net::TcpConnectionPtr& conn,
                   zexuan::net::Buffer* buffer,
                   zexuan::net::Timestamp receiveTime);

    /**
     * @brief 写入完成回调函数
     * @param conn TCP连接指针
     */
    void onWriteComplete(const zexuan::net::TcpConnectionPtr& conn);

    /**
     * @brief 事件循环线程函数
     */
    void eventLoopThread();

    /**
     * @brief 用户输入处理函数（交互模式）
     */
    void handleUserInput();

    /**
     * @brief 显示帮助信息
     */
    void showHelp() const;

    /**
     * @brief 从用户输入读取数值
     * @param prompt 提示信息
     * @param defaultValue 默认值
     * @return 用户输入的数值
     */
    template<typename T>
    T readNumber(const std::string& prompt, T defaultValue);

    /**
     * @brief 从用户输入读取字符串
     * @param prompt 提示信息
     * @param defaultValue 默认值
     * @return 用户输入的字符串
     */
    std::string readString(const std::string& prompt, const std::string& defaultValue = "");

    /**
     * @brief 基于插件的JSON信息展示菜单并发送选定操作
     * @param pluginId 插件ID
     */
    void runPluginMenu(uint8_t pluginId);


private:
    // 网络相关
    std::unique_ptr<zexuan::net::EventLoop> loop_;
    std::unique_ptr<zexuan::net::TcpClient> client_;

    // 交互状态机
    enum class UiState { COMMAND_MODE, MENU_MODE };
    std::atomic<UiState> uiState_{UiState::COMMAND_MODE};

    // 菜单上下文
    struct MenuContext {
        uint8_t pluginId{0};
        std::string infoJson;          // 插件JSON的原始字符串（避免在头文件依赖nlohmann/json）
        size_t choiceCount{0};
        bool valid{false};
    };
    MenuContext currentMenu_;

    zexuan::net::InetAddress serverAddr_;
    zexuan::net::TcpConnectionPtr connection_;
    std::atomic<bool> connected_;

    // 配置相关（已移除配置依赖）
    // std::map<std::string, uint8_t> pluginNameToId_;
    // std::map<uint8_t, std::string> pluginIdToName_;

    // 客户端ID和插件信息管理
    uint8_t clientId_;                                ///< 客户端ID
    std::map<uint8_t, std::string> pluginInfoCache_; ///< 插件信息缓存
    mutable std::mutex pluginInfoMutex_;             ///< 插件信息互斥锁

    // 线程控制
    std::unique_ptr<std::thread> eventLoopThread_;
    std::atomic<bool> running_;

    // 日志
    std::shared_ptr<spdlog::logger> logger_;

    // 用户交互
    std::atomic<bool> interactiveMode_;
};

} // namespace client
} // namespace zexuan

#endif // ZEXUAN_CLIENT_MESSAGE_CLIENT_HPP

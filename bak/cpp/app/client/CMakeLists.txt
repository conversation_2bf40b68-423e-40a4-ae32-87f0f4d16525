# IEC 60870-5-103 分层架构消息客户端
add_executable(client 
    src/main.cpp
    src/network_manager.cpp
    src/message_handler.cpp
    src/ui_manager.cpp
    src/system_manager.cpp
)

target_include_directories(client PRIVATE 
    include
    ${CMAKE_SOURCE_DIR}/cpp/core/include
)

target_link_libraries(client 
    core 
    nlohmann_json::nlohmann_json
    pthread
)

# 设置可执行文件属性
set_target_properties(client PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/bin
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
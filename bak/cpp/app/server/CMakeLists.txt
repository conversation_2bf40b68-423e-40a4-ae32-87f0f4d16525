# 热加载服务器可执行文件
add_executable(server
    src/main.cpp
    src/config.cpp
    src/system_manager.cpp
    src/network_manager.cpp
    src/plugin_manager.cpp
    src/message_handler.cpp
    src/message_router.cpp
)

# 包含头文件目录
target_include_directories(server PRIVATE
    include
    ${CMAKE_SOURCE_DIR}/core/include
    ${CMAKE_SOURCE_DIR}/interface/include
)

# 链接库
target_link_libraries(server
    core
    plugin_interface
    nlohmann_json::nlohmann_json
)

# 设置可执行文件属性
set_target_properties(server PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/bin
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)


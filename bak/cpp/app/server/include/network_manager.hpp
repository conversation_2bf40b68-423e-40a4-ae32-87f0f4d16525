/**
 * @file network_manager.hpp
 * @brief 网络层管理器 - 基于network的TCP服务器封装
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_SERVER_Network_MANAGER_HPP
#define ZEXUAN_SERVER_Network_MANAGER_HPP

#include <memory>
#include <string>
#include <atomic>
#include <thread>
#include <mutex>
#include <vector>
#include <functional>
#include <map> // Added for clientConnections_

#include "zexuan/net/TcpServer.h"
#include "zexuan/net/EventLoop.h"
#include "zexuan/net/InetAddress.h"
#include "zexuan/net/TcpConnection.h"
#include "zexuan/net/Timestamp.h"
#include "zexuan/base/message.hpp"
#include "zexuan/logger.hpp"

namespace zexuan {
namespace server {

// 前向声明
class MessageHandler;

/**
 * @brief 网络层管理器
 * 
 * 基于network网络库的TCP服务器管理器：
 * - 只负责网络连接管理
 * - 收到消息后直接转发给MessageHandler处理
 * - 不执行任何业务逻辑
 */
class NetworkManager {
public:
    /**
     * @brief 构造函数
     * @param port 监听端口
     * @param threadNum 工作线程数
     */
    NetworkManager(uint16_t port, int threadNum = 0);

    /**
     * @brief 析构函数
     */
    ~NetworkManager();

    /**
     * @brief 启动网络服务
     * @return true 成功，false 失败
     */
    bool start();

    /**
     * @brief 停止网络服务
     */
    void stop();

    /**
     * @brief 检查是否正在运行
     * @return true 正在运行，false 已停止
     */
    bool isRunning() const { return running_.load(); }

    /**
     * @brief 获取监听端口
     * @return 端口号
     */
    uint16_t getPort() const { return port_; }

    /**
     * @brief 设置消息处理回调函数
     * @param callback 消息处理回调函数
     */
    void setMessageCallback(std::function<void(const zexuan::net::TcpConnectionPtr&, const char*, size_t)> callback);

    /**
     * @brief 向指定客户端发送消息
     * @param clientId 客户端ID
     * @param data 消息数据
     * @param len 消息长度
     * @return true 发送成功，false 发送失败
     */
    bool sendToClient(uint8_t clientId, const char* data, size_t len);

    /**
     * @brief 获取当前连接数
     * @return 连接数
     */
    size_t getConnectionCount() const;

private:
    /**
     * @brief 新连接建立回调
     * @param conn TCP连接
     */
    void onConnection(const zexuan::net::TcpConnectionPtr& conn);

    /**
     * @brief 消息接收回调
     * @param conn TCP连接
     * @param buf 接收缓冲区
     * @param receiveTime 接收时间
     */
    void onMessage(const zexuan::net::TcpConnectionPtr& conn,
                   zexuan::net::Buffer* buf,
                   zexuan::net::Timestamp receiveTime);

    /**
     * @brief 写完成回调
     * @param conn TCP连接
     */
    void onWriteComplete(const zexuan::net::TcpConnectionPtr& conn);

private:
    uint16_t port_;                                     ///< 监听端口
    int threadNum_;                                     ///< 工作线程数
    std::atomic<bool> running_;                         ///< 运行状态
    
    std::unique_ptr<zexuan::net::EventLoop> loop_;     ///< 事件循环
    std::unique_ptr<zexuan::net::TcpServer> server_;   ///< TCP服务器
    std::unique_ptr<std::thread> loopThread_;          ///< 事件循环线程
    
    std::shared_ptr<spdlog::logger> logger_;          ///< 日志器
    
    // 客户端ID映射
    mutable std::mutex clientMapMutex_;               ///< 客户端映射互斥锁
    std::map<uint8_t, std::weak_ptr<zexuan::net::TcpConnection>> clientConnections_; ///< 客户端ID到连接映射
    
    // 回调函数
    std::function<void(const zexuan::net::TcpConnectionPtr&, const char*, size_t)> messageCallback_;
};

} // namespace server
} // namespace zexuan

#endif // ZEXUAN_SERVER_Network_MANAGER_HPP

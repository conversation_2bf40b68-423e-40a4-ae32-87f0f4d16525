/**
 * @file message_handler.hpp
 * @brief 消息处理器 - 业务逻辑层
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_SERVER_MESSAGE_HANDLER_HPP
#define ZEXUAN_SERVER_MESSAGE_HANDLER_HPP

#include "zexuan/base/observer.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/logger.hpp"
#include "zexuan/net/TcpConnection.h"
#include <functional>
#include <memory>
#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <vector>
#include <map> // Added for clientConnections_
#include <unordered_map> // Added for requestClientMap_

namespace zexuan {
namespace server {

/**
 * @brief 消息处理器类
 * 
 * 作为id=0, name="server"的observer注册到mediator中
 * 接收插件发送的消息，处理业务逻辑，然后通过回调转发给网络层
 * 现在也负责处理从网络层接收的原始数据，包括消息解析、队列处理等通信逻辑
 */
class MessageHandler : public base::BaseObserver {
public:
    /**
     * @brief 消息转发回调函数类型
     */
    using MessageForwardCallback = std::function<void(const base::Message&, uint8_t clientId)>;
    
    /**
     * @brief 消息来源类型枚举
     */
    enum class MessageSource {
        CLIENT_REQUEST,    // 来自客户端的请求
        PLUGIN_RESPONSE    // 来自插件的响应
    };
    
    /**
     * @brief 构造函数，要求注入 Mediator
     */
    explicit MessageHandler(std::shared_ptr<base::Mediator> mediator);

    /**
     * @brief 析构函数
     */
    virtual ~MessageHandler();

    /**
     * @brief 启动消息处理器
     * @param workerThreads 工作线程数
     * @return true 成功，false 失败
     */
    bool start(int workerThreads = 1);

    /**
     * @brief 停止消息处理器
     */
    void stop();

    /**
     * @brief 检查是否正在运行
     * @return true 正在运行，false 已停止
     */
    bool isRunning() const { return running_.load(); }

    /**
     * @brief 处理来自客户端的消息
     * @param message 消息对象
     * @param clientId 客户端ID
     */
    void processClientMessage(const base::Message& message, uint8_t clientId);

    /**
     * @brief 处理来自插件的消息
     * @param subject 发送方
     * @param message 消息内容
     */
    void onNotify(base::Subject* subject, const base::Message& message) override;

    /**
     * @brief 获取观察者名称
     * @return "server"
     */
    const std::string& getName() const { return name_; }

    /**
     * @brief 设置消息转发回调函数
     * @param callback 回调函数，用于将消息转发给网络层
     */
    void setMessageForwardCallback(MessageForwardCallback callback);

private:
    /**
     * @brief 消息处理工作线程
     */
    void messageWorkerThread();

    /**
     * @brief 推送消息到处理队列
     * @param message 消息对象
     * @param source 消息来源
     * @param clientId 客户端ID
     */
    void pushMessageToQueue(const base::Message& message, MessageSource source, uint8_t clientId);

private:
    std::shared_ptr<base::Mediator> mediator_;          ///< 消息中介者（从单例注册表获取）
    MessageForwardCallback messageForwardCallback_;     ///< 消息转发回调
    std::shared_ptr<spdlog::logger> logger_;           ///< 日志器
    
    std::atomic<bool> running_;                         ///< 运行状态
    
    // 消息处理队列
    struct MessageTask {
        MessageSource source;           // 消息来源
        base::Message message;          // 消息内容
        uint8_t clientId;              // 客户端ID，用于标识消息来源或目标
        
        MessageTask(MessageSource src, const base::Message& msg, uint8_t clientId)
            : source(src), message(msg), clientId(clientId) {}
    };
    
    std::queue<MessageTask> messageQueue_;             ///< 消息队列
    std::mutex queueMutex_;                            ///< 队列互斥锁
    std::condition_variable queueCondition_;          ///< 队列条件变量
    std::vector<std::thread> messageWorkers_;         ///< 消息处理工作线程
};

} // namespace server
} // namespace zexuan

#endif // ZEXUAN_SERVER_MESSAGE_HANDLER_HPP

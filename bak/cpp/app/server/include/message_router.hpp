/**
 * @file message_router.hpp
 * @brief 消息路由层 - 负责消息的序列化/反序列化和路由分发
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_SERVER_MESSAGE_ROUTER_HPP
#define ZEXUAN_SERVER_MESSAGE_ROUTER_HPP

#include <functional>
#include <memory>
#include "zexuan/base/message.hpp"
#include "zexuan/net/TcpConnection.h"
#include "zexuan/logger.hpp"

namespace zexuan {
namespace server {

// 前向声明
class NetworkManager;

/**
 * @brief 消息路由层
 * 
 * 职责：
 * - 消息的序列化和反序列化
 * - 消息路由分发
 * - 客户端连接管理
 */
class MessageRouter {
public:
    /**
     * @brief 消息处理回调函数类型
     */
    using MessageHandlerCallback = std::function<void(const base::Message&, uint8_t clientId)>;
    
    /**
     * @brief 网络发送回调函数类型
     */
    using NetworkSendCallback = std::function<bool(uint8_t, const char*, size_t)>;
    
    /**
     * @brief 构造函数
     */
    MessageRouter();

    /**
     * @brief 析构函数
     */
    ~MessageRouter();

    /**
     * @brief 设置消息处理回调
     * @param callback 消息处理回调函数
     */
    void setMessageHandler(MessageHandlerCallback callback);

    /**
     * @brief 设置网络发送回调
     * @param callback 网络发送回调函数
     */
    void setNetworkSendCallback(NetworkSendCallback callback);

    /**
     * @brief 处理网络层接收到的原始数据
     * @param conn TCP连接
     * @param data 原始数据
     * @param len 数据长度
     */
    void handleNetworkData(const zexuan::net::TcpConnectionPtr& conn, 
                          const char* data, 
                          size_t len);

    /**
     * @brief 发送消息给指定客户端
     * @param message 消息对象
     * @param clientId 客户端ID（255表示广播）
     */
    void sendMessage(const base::Message& message, uint8_t clientId = 255);

private:
    /**
     * @brief 解析消息
     * @param data 数据指针
     * @param len 数据长度
     * @param message 输出消息对象
     * @return 解析成功的字节数，0表示需要更多数据，-1表示解析错误
     */
    int parseMessage(const char* data, size_t len, base::Message& message);

private:
    MessageHandlerCallback messageHandler_;            ///< 消息处理回调
    NetworkSendCallback networkSendCallback_;          ///< 网络发送回调
    
    std::shared_ptr<spdlog::logger> logger_;          ///< 日志器
};

} // namespace server
} // namespace zexuan

#endif // ZEXUAN_SERVER_MESSAGE_ROUTER_HPP

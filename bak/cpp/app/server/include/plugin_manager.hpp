/**
 * @file plugin_manager.hpp
 * @brief 插件管理器 - 管理插件的加载、初始化和卸载
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_SERVER_PLUGIN_MANAGER_HPP
#define ZEXUAN_SERVER_PLUGIN_MANAGER_HPP

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <atomic>

#include "zexuan/base/dynamic_library_loader.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/logger.hpp"
#include "config.hpp"

namespace zexuan {
namespace server {

// 插件接口函数指针类型定义 - 使用PluginBase
typedef plugin::PluginBase* (*CreatePluginFunc)(void*, int, const char*);
typedef void (*DestroyPluginFunc)(plugin::PluginBase*);

/**
 * @brief 插件信息结构体
 */
struct PluginInfo {
    int id;                                     ///< 插件ID
    std::string name;                          ///< 插件名称
    std::string path;                          ///< 插件路径
    bool enabled;                              ///< 是否启用
    bool loaded;                               ///< 是否已加载
    plugin::PluginBase* instance;             ///< 插件实例
    CreatePluginFunc createFunc;               ///< 创建函数
    DestroyPluginFunc destroyFunc;             ///< 销毁函数
    
    PluginInfo() : id(0), enabled(false), loaded(false), instance(nullptr), 
                   createFunc(nullptr), destroyFunc(nullptr) {}
};

/**
 * @brief 插件管理器
 * 
 * 负责插件系统的管理：
 * - 从配置文件加载插件信息
 * - 使用DynamicLibraryLoader加载插件动态库
 * - 调用create_plugin创建插件实例
 * - 插件自动注册到mediator
 * - 管理插件生命周期（init/shutdown）
 */
class PluginManager {
public:
    /**
     * @brief 构造函数，要求注入 Mediator
     */
    explicit PluginManager(std::shared_ptr<base::Mediator> mediator);

    /**
     * @brief 析构函数
     */
    ~PluginManager();

    /**
     * @brief 从配置加载插件
     * @param config 配置对象
     * @return true 成功，false 失败
     */
    bool loadFromConfig(const Config& config);

    /**
     * @brief 初始化所有启用的插件
     * @return true 成功，false 失败
     */
    bool initializePlugins();

    /**
     * @brief 启动所有插件
     * @return true 成功，false 失败
     */
    bool startPlugins();

    /**
     * @brief 停止所有插件
     */
    void stopPlugins();

    /**
     * @brief 卸载所有插件
     */
    void unloadPlugins();

    /**
     * @brief 加载单个插件
     * @param pluginConfig 插件配置
     * @return true 成功，false 失败
     */
    bool loadPlugin(const PluginConfig& pluginConfig);

    /**
     * @brief 卸载单个插件
     * @param pluginId 插件ID
     * @return true 成功，false 失败
     */
    bool unloadPlugin(int pluginId);

    /**
     * @brief 重新加载插件
     * @param pluginId 插件ID
     * @return true 成功，false 失败
     */
    bool reloadPlugin(int pluginId);

    /**
     * @brief 获取插件信息
     * @param pluginId 插件ID
     * @return 插件信息指针，nullptr表示不存在
     */
    const PluginInfo* getPluginInfo(int pluginId) const;

    /**
     * @brief 获取所有插件信息
     * @return 插件信息列表
     */
    std::vector<PluginInfo> getAllPluginInfo() const;

    /**
     * @brief 检查插件是否已加载
     * @param pluginId 插件ID
     * @return true 已加载，false 未加载
     */
    bool isPluginLoaded(int pluginId) const;

    /**
     * @brief 获取已加载插件数量
     * @return 插件数量
     */
    size_t getLoadedPluginCount() const;

private:
    /**
     * @brief 创建插件实例
     * @param info 插件信息
     * @return true 成功，false 失败
     */
    bool createPluginInstance(PluginInfo& info);

    /**
     * @brief 销毁插件实例
     * @param info 插件信息
     */
    void destroyPluginInstance(PluginInfo& info);

private:
    std::shared_ptr<base::Mediator> mediator_;                  ///< 消息中介者（从单例注册表获取）
    base::DynamicLibraryLoader loader_;                         ///< 动态库加载器
    std::unordered_map<int, PluginInfo> plugins_;              ///< 插件信息映射
    std::shared_ptr<spdlog::logger> logger_;                   ///< 日志器
    std::atomic<bool> initialized_;                             ///< 初始化状态
};

} // namespace server
} // namespace zexuan

#endif // ZEXUAN_SERVER_PLUGIN_MANAGER_HPP

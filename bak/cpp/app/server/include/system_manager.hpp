/**
 * @file system_manager.hpp
 * @brief 系统管理器 - 负责所有资源的初始化和关闭
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_SERVER_SYSTEM_MANAGER_HPP
#define ZEXUAN_SERVER_SYSTEM_MANAGER_HPP

#include <memory>
#include <string>
#include <atomic>
#include <thread>
#include <condition_variable>
#include <mutex>

#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/logger.hpp"
#include "network_manager.hpp"
#include "plugin_manager.hpp"
#include "message_handler.hpp"
#include "message_router.hpp"
#include "config.hpp"

namespace zexuan {
namespace server {

/**
 * @brief 系统管理器
 * 
 * 负责整个系统的生命周期管理：
 * - 初始化所有子系统（网络、插件等）
 * - 注册单例对象
 * - 配置文件加载
 * - 信号处理
 * - 优雅退出
 */
class SystemManager {
public:
    /**
     * @brief 构造函数
     */
    SystemManager();

    /**
     * @brief 析构函数
     */
    ~SystemManager();

    /**
     * @brief 初始化系统
     * @param configPath 配置文件路径
     * @return true 成功，false 失败
     */
    bool initialize(const std::string& configPath);

    /**
     * @brief 启动系统
     * @return true 成功，false 失败
     */
    bool start();

    /**
     * @brief 停止系统
     */
    void stop();

    /**
     * @brief 等待系统运行
     * 阻塞直到收到停止信号
     */
    void waitForStop();

    /**
     * @brief 检查系统是否正在运行
     * @return true 正在运行，false 已停止
     */
    bool isRunning() const { return running_.load(); }

    /**
     * @brief 获取配置
     * @return 配置对象引用
     */
    const Config& getConfig() const { return *config_; }

    /**
     * @brief 获取Mediator
     * @return Mediator指针
     */
    std::shared_ptr<base::Mediator> getMediator() { return mediator_; }

private:
    /**
     * @brief 初始化日志系统
     */
    void initializeLogging();

    /**
     * @brief 初始化信号处理
     */
    void initializeSignalHandling();

    /**
     * @brief 初始化所有组件
     * @return true 成功，false 失败
     */
    bool initializeComponents();

    /**
     * @brief 设置组件间的回调关系
     */
    void setupCallbacks();

    /**
     * @brief 信号处理函数
     * @param sig 信号值
     */
    static void signalHandler(int sig);

    /**
     * @brief 静态实例指针（用于信号处理）
     */
    static SystemManager* instance_;

private:
    std::atomic<bool> running_;                         ///< 运行状态
    std::atomic<bool> initialized_;                     ///< 初始化状态
    
    std::unique_ptr<Config> config_;                    ///< 配置对象
    std::shared_ptr<base::Mediator> mediator_;         ///< 消息中介者
    std::unique_ptr<NetworkManager> networkManager_;     ///< 网络管理器
    std::unique_ptr<PluginManager> pluginManager_;     ///< 插件管理器
    std::unique_ptr<MessageHandler> messageHandler_;   ///< 消息处理器（业务层）
    std::unique_ptr<MessageRouter> messageRouter_;     ///< 消息路由层
    
    std::shared_ptr<spdlog::logger> logger_;           ///< 日志器
    
    std::mutex stopMutex_;                              ///< 停止互斥锁
    std::condition_variable stopCondition_;            ///< 停止条件变量
};

} // namespace server
} // namespace zexuan

#endif // ZEXUAN_SERVER_SYSTEM_MANAGER_HPP

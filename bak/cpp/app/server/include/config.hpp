/**
 * @file config.hpp
 * @brief 配置系统 - 处理config.json配置文件
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_SERVER_CONFIG_HPP
#define ZEXUAN_SERVER_CONFIG_HPP

#include <string>
#include <vector>
#include <cstdint>
#include "zexuan/config_loader.hpp"

namespace zexuan {
namespace server {

/**
 * @brief 插件配置结构体
 */
struct PluginConfig {
    int id;                     ///< 插件ID
    std::string name;          ///< 插件名称
    std::string libraryPath;   ///< 插件动态库路径
    bool enabled;              ///< 是否启用
    
    PluginConfig() : id(0), enabled(false) {}
    
    PluginConfig(int pluginId, const std::string& pluginName, 
                const std::string& pluginLibPath, bool pluginEnabled)
        : id(pluginId), name(pluginName), libraryPath(pluginLibPath), enabled(pluginEnabled) {}
};

/**
 * @brief 服务器配置结构体
 */
struct ServerConfig {
    uint16_t port;              ///< 监听端口
    int threadNum;              ///< 工作线程数
    
    ServerConfig() = default;   // 不提供默认值
};

// 移除热重载配置，只保留server和plugins

/**
 * @brief 主配置类
 * 
 * 基于ConfigLoader的简化配置管理：
 * - plugins数组：包含id、name、library_path、enabled
 * - server配置：port、thread_num
 */
class Config {
public:
    /**
     * @brief 构造函数
     */
    Config();

    /**
     * @brief 析构函数
     */
    ~Config() = default;

    /**
     * @brief 从文件加载配置
     * @param configPath 配置文件路径
     * @return true 成功，false 失败
     */
    bool loadFromFile(const std::string& configPath);

    /**
     * @brief 验证配置有效性
     * @return true 有效，false 无效
     */
    bool validate() const;

    /**
     * @brief 获取插件配置列表
     * @return 插件配置向量
     */
    std::vector<PluginConfig> getPlugins() const;

    /**
     * @brief 获取服务器配置
     * @return 服务器配置
     */
    ServerConfig getServer() const;

    // 移除热重载相关方法

    /**
     * @brief 检查是否已加载
     * @return true 已加载，false 未加载
     */
    bool isLoaded() const { return loaded_; }

private:
    zexuan::ConfigLoader loader_;              ///< 配置加载器
    bool loaded_;                              ///< 是否已加载
    std::string configPath_;                   ///< 配置文件路径
};

} // namespace server
} // namespace zexuan

#endif // ZEXUAN_SERVER_CONFIG_HPP

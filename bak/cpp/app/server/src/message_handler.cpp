/**
 * @file message_handler.cpp
 * @brief 消息处理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "message_handler.hpp"
#include "zexuan/base/message_types.hpp"
#include "zexuan/base/mediator.hpp"

#include "zexuan/base/subject.hpp"
#include <algorithm>

namespace zexuan {
namespace server {

MessageHandler::MessageHandler(std::shared_ptr<base::Mediator> mediator)
    : BaseObserver(0, "server")  // id=0, name="server"
    , running_(false) {

    logger_ = zexuan::Logger::getFileLogger("message_handler");
    logger_->info("MessageHandler 初始化，id=0, name=server");

    mediator_ = std::move(mediator);
    if (!mediator_) {
        logger_->error("注入的 Mediator 为空");
    } else {
        // 注册到mediator
        std::string errorMsg;
        if (mediator_->registerObserver(0, "server", this, errorMsg)) {
            logger_->info("MessageHandler注册到mediator成功");
        } else {
            logger_->error("MessageHandler注册到mediator失败: {}", errorMsg);
        }
    }
}

MessageHandler::~MessageHandler() {
    // 先停止运行
    stop();
    
    // 清空回调函数，避免在对象销毁后被调用
    messageForwardCallback_ = nullptr;
    
    // 清空mediator引用
    mediator_ = nullptr;
    
    logger_->info("MessageHandler 销毁");
}

bool MessageHandler::start(int workerThreads) {
    if (running_.load()) {
        logger_->warn("MessageHandler 已经在运行");
        return true;
    }
    
    try {
        // 先设置运行标志，再启动工作线程
        running_ = true;
        
        // 启动消息处理工作线程
        int workerCount = std::max(1, workerThreads); 
        for (int i = 0; i < workerCount; ++i) {
            messageWorkers_.emplace_back(&MessageHandler::messageWorkerThread, this);
        }
        
        logger_->info("MessageHandler 启动成功，工作线程数: {}", workerCount);
        return true;
    }
    catch (const std::exception& e) {
        logger_->error("MessageHandler 启动失败: {}", e.what());
        return false;
    }
}

void MessageHandler::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_ = false;
    logger_->info("停止 MessageHandler...");
    
    // 通知所有等待的线程
    queueCondition_.notify_all();
    
    // 等待所有工作线程结束
    for (auto& worker : messageWorkers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    messageWorkers_.clear();
    
    logger_->info("MessageHandler 已停止");
}

void MessageHandler::processClientMessage(const base::Message& message, uint8_t clientId) {
    logger_->debug("处理客户端消息: COT={}, Source={}, Target={}, INF={}, ClientID={}", 
                  static_cast<int>(message.getCot()),
                  static_cast<int>(message.getSource()),
                  static_cast<int>(message.getTarget()),
                  static_cast<int>(message.getInf()),
                  static_cast<int>(clientId));
    
    // 将客户端请求推送到处理队列
    pushMessageToQueue(message, MessageSource::CLIENT_REQUEST, clientId);
}

void MessageHandler::onNotify(base::Subject* subject, const base::Message& message) {
    // 处理来自插件的消息
    logger_->debug("收到插件消息: type={}, cot={}, from={}, source={}, target={}", 
                  static_cast<int>(message.getTyp()),
                  static_cast<int>(message.getCot()),
                  subject ? subject->getId() : 255,
                  static_cast<int>(message.getSource()),
                  static_cast<int>(message.getTarget()));
    
    // 检查是否是INFO_RESPONSE消息
    if (message.getCot() == static_cast<uint8_t>(base::CauseOfTransmission::INFO_RESPONSE)) {
        // 对于INFO_RESPONSE，target字段包含目标客户端ID（因为插件已经交换了source和target）
        uint8_t targetClientId = message.getTarget();
        logger_->info("INFO_RESPONSE for client ID: {} from plugin: {}", 
                     static_cast<int>(targetClientId),
                     subject ? subject->getId() : 255);
        
        // 验证客户端ID的有效性
        if (targetClientId == 0 || targetClientId == 255) {
            logger_->warn("无效的目标客户端ID: {}，忽略", static_cast<int>(targetClientId));
        } else {
            // 将插件响应推送给特定客户端
            pushMessageToQueue(message, MessageSource::PLUGIN_RESPONSE, targetClientId);
        }
    } else {
        // 其他类型的消息广播给所有客户端
        logger_->debug("非INFO_RESPONSE消息，忽略");
    }
}

void MessageHandler::setMessageForwardCallback(MessageForwardCallback callback) {
    messageForwardCallback_ = std::move(callback);
    logger_->info("设置消息转发回调");
}

void MessageHandler::pushMessageToQueue(const base::Message& message, MessageSource source, uint8_t clientId) {
    MessageTask task(source, message, clientId);
    
    {
        std::lock_guard<std::mutex> lock(queueMutex_);
        messageQueue_.push(std::move(task));
    }
    
    queueCondition_.notify_one();
}

void MessageHandler::messageWorkerThread() {
    logger_->info("消息处理工作线程启动");
    
    while (running_.load()) {
        MessageTask task(MessageSource::CLIENT_REQUEST, base::Message(), 0);
        
        // 从队列中获取任务
        {
            std::unique_lock<std::mutex> lock(queueMutex_);
            queueCondition_.wait(lock, [this] { 
                return !messageQueue_.empty() || !running_.load(); 
            });
            
            if (!running_.load()) {
                break;
            }
            
            if (!messageQueue_.empty()) {
                task = std::move(messageQueue_.front());
                messageQueue_.pop();
            } else {
                continue;
            }
        }
        
        // 根据消息来源处理消息
        try {
            if (task.source == MessageSource::CLIENT_REQUEST) {
                // 客户端请求：发送给插件
                logger_->debug("处理客户端请求，客户端ID: {}, 目标插件: {}", 
                             task.clientId, static_cast<int>(task.message.getTarget()));
                
                if (mediator_) {
                    std::string description;
                    // 使用sendMessageToTarget直接指定目标插件ID
                    int targetPluginId = static_cast<int>(task.message.getTarget());
                    int result = mediator_->sendMessageToTarget(task.message, targetPluginId, description);
                    
                    if (result != 0) {
                        logger_->warn("消息处理失败: {}, 结果: {}", description, result);
                    } else {
                        logger_->debug("消息已发送到插件 {}", targetPluginId);
                    }
                }
            } else if (task.source == MessageSource::PLUGIN_RESPONSE) {
                // 插件响应：发送给客户端
                logger_->debug("处理插件响应，目标客户端ID: {}", task.clientId);
                if (messageForwardCallback_) {
                    messageForwardCallback_(task.message, task.clientId);
                } else {
                    logger_->warn("消息转发回调未设置，无法发送插件响应");
                }
            }
        }
        catch (const std::exception& e) {
            logger_->error("消息处理异常: {}", e.what());
        }
    }
    
    logger_->info("消息处理工作线程退出");
}


} // namespace server
} // namespace zexuan

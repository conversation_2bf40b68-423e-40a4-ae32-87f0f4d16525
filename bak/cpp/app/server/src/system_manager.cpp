/**
 * @file system_manager.cpp
 * @brief 系统管理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "system_manager.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include <signal.h>
#include <csignal>
#include <thread>
#include <chrono>
#include <spdlog/spdlog.h>

namespace zexuan {
namespace server {

// 静态成员定义
SystemManager* SystemManager::instance_ = nullptr;

SystemManager::SystemManager()
    : running_(false)
    , initialized_(false) {
    
    // 设置静态实例指针（用于信号处理）
    instance_ = this;
    
    initializeLogging();
    logger_->info("SystemManager 构造完成");
}

SystemManager::~SystemManager() {
    stop();
    instance_ = nullptr;
    if (logger_) {
        logger_->info("SystemManager 销毁完成");
    }
}

bool SystemManager::initialize(const std::string& configPath) {
    if (initialized_.load()) {
        logger_->warn("SystemManager 已经初始化");
        return true;
    }
    
    logger_->info("初始化 SystemManager，配置文件: {}", configPath);
    
    // 加载配置文件
    config_ = std::make_unique<Config>();
    if (!config_->loadFromFile(configPath)) {
        logger_->error("加载配置文件失败: {}", configPath);
        return false;
    }
    
    logger_->info("配置文件加载成功");
    
    // 初始化信号处理
    initializeSignalHandling();
    
    // 初始化所有组件
    if (!initializeComponents()) {
        logger_->error("初始化组件失败");
        return false;
    }
    
    // 设置组件间的回调关系
    setupCallbacks();
    
    initialized_ = true;
    logger_->info("SystemManager 初始化完成");
    return true;
}

bool SystemManager::start() {
    if (!initialized_.load()) {
        logger_->error("SystemManager 未初始化，无法启动");
        return false;
    }
    
    if (running_.load()) {
        logger_->warn("SystemManager 已经在运行");
        return true;
    }
    
    logger_->info("启动 SystemManager...");
    
    // 启动网络管理器
    if (!networkManager_->start()) {
        logger_->error("启动网络管理器失败");
        return false;
    }
    
    // 初始化插件
    if (!pluginManager_->initializePlugins()) {
        logger_->error("初始化插件失败");
        return false;
    }
    
    // 启动插件
    if (!pluginManager_->startPlugins()) {
        logger_->error("启动插件失败");
        return false;
    }
    
    running_ = true;
    logger_->info("SystemManager 启动成功");
    logger_->info("服务器运行在端口: {}, 工作线程数: {}", 
                 config_->getServer().port, config_->getServer().threadNum);
    logger_->info("已加载插件数: {}", pluginManager_->getLoadedPluginCount());
    logger_->info("当前连接数: {}", networkManager_->getConnectionCount());
    
    return true;
}

void SystemManager::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_ = false;
    logger_->info("正在优雅停止 SystemManager...");
    
    // 按相反顺序停止组件，确保优雅关闭
    
    // 1. 首先停止消息处理器
    if (messageHandler_) {
        logger_->info("停止消息处理器...");
        messageHandler_->stop();
        logger_->info("消息处理器已停止");
    }
    
    // 2. 停止网络管理器
    if (networkManager_) {
        logger_->info("停止网络管理器...");
        networkManager_->stop();
        logger_->info("网络管理器已停止");
    }
    
    // 3. 停止插件（给插件时间处理剩余消息）
    if (pluginManager_) {
        logger_->info("停止插件...");
        pluginManager_->stopPlugins();

        logger_->info("卸载插件...");
        pluginManager_->unloadPlugins();
        logger_->info("插件已卸载");
    }

    // 4. 刷新日志，清理回调，避免在日志后台线程关闭后仍有回调触发
    spdlog::apply_all([](std::shared_ptr<spdlog::logger> l) { l->flush(); });

    if (messageHandler_) {
        messageHandler_->setMessageForwardCallback(nullptr);
    }
    if (messageRouter_) {
        messageRouter_->setMessageHandler(nullptr);
        messageRouter_->setNetworkSendCallback(nullptr);
    }
    if (networkManager_) {
        networkManager_->setMessageCallback(nullptr);
    }

    // 5. 通知等待线程（先唤醒等待者，随后逐步释放组件与日志）
    {
        std::lock_guard<std::mutex> lock(stopMutex_);
        stopCondition_.notify_all();
    }

    // 6. 释放组件，确保它们的析构期间仍可写日志
    if (messageHandler_) { logger_->info("释放消息处理器..."); }
    messageHandler_.reset();

    if (messageRouter_) { logger_->info("释放消息路由层..."); }
    messageRouter_.reset();

    if (pluginManager_) { logger_->info("释放插件管理器..."); }
    pluginManager_.reset();

    if (networkManager_) { logger_->info("释放网络管理器..."); }
    networkManager_.reset();

    logger_->info("SystemManager 优雅停止完成，即将关闭日志线程池...");

    // 7. 最后：刷新并关闭日志线程池（之后不要再写日志）
    spdlog::apply_all([](std::shared_ptr<spdlog::logger> l) { if (l) l->flush(); });
    spdlog::shutdown();
}

void SystemManager::waitForStop() {
    std::unique_lock<std::mutex> lock(stopMutex_);
    stopCondition_.wait(lock, [this] { return !running_.load(); });
}

void SystemManager::initializeLogging() {
    logger_ = zexuan::Logger::getFileLogger("system");
    logger_->info("日志系统初始化完成");
    
    // 禁用Logger自带的信号处理，使用我们自己的
    // Logger::setupSignalHandlers(); // 不调用这个
}

void SystemManager::initializeSignalHandling() {
    // 注册信号处理函数
    signal(SIGINT, signalHandler);   // Ctrl+C (交互式)
    signal(SIGTERM, signalHandler);  // systemd停止信号
    signal(SIGHUP, signalHandler);   // systemd重载信号
    
    // 忽略SIGPIPE信号（避免写入已关闭的socket导致程序崩溃）
    signal(SIGPIPE, SIG_IGN);
    
    logger_->info("信号处理初始化完成 (支持SIGINT, SIGTERM, SIGHUP)");
}

bool SystemManager::initializeComponents() {
    logger_->info("初始化所有组件...");
    
    // 1. 初始化Mediator（使用单例注册表）
    logger_->info("初始化Mediator...");
    mediator_ = zexuan::getSingleton<base::BaseMediator>();
    if (!mediator_) {
        logger_->error("创建Mediator失败");
        return false;
    }
    logger_->info("Mediator创建成功");
    
    // 2. 初始化网络管理器
    logger_->info("初始化网络管理器...");
    networkManager_ = std::make_unique<NetworkManager>(
        config_->getServer().port, 
        config_->getServer().threadNum);
    if (!networkManager_) {
        logger_->error("创建网络管理器失败");
        return false;
    }
    logger_->info("网络管理器初始化成功");
    
    // 3. 初始化插件管理器（注入 Mediator）
    logger_->info("初始化插件管理器...");
    pluginManager_ = std::make_unique<PluginManager>(mediator_);
    if (!pluginManager_) {
        logger_->error("创建插件管理器失败");
        return false;
    }

    // 从配置加载插件
    if (!pluginManager_->loadFromConfig(*config_)) {
        logger_->error("从配置加载插件失败");
        return false;
    }
    logger_->info("插件管理器初始化成功");

    // 4. 初始化消息路由层
    logger_->info("初始化消息路由层...");
    messageRouter_ = std::make_unique<MessageRouter>();
    if (!messageRouter_) {
        logger_->error("创建消息路由层失败");
        return false;
    }
    logger_->info("消息路由层初始化成功");

    // 5. 初始化消息处理器（注入 Mediator）
    logger_->info("初始化消息处理器...");
    messageHandler_ = std::make_unique<MessageHandler>(mediator_);
    if (!messageHandler_) {
        logger_->error("创建消息处理器失败");
        return false;
    }
    
    // 启动消息处理器
    if (!messageHandler_->start(config_->getServer().threadNum)) {
        logger_->error("启动消息处理器失败");
        return false;
    }
    logger_->info("消息处理器初始化成功");
    
    logger_->info("所有组件初始化完成");
    return true;
}

void SystemManager::setupCallbacks() {
    logger_->info("设置组件间的回调关系...");
    
    // 1. 设置路由层的网络发送回调
    if (messageRouter_) {
        messageRouter_->setNetworkSendCallback(
            [this](uint8_t clientId, const char* data, size_t len) {
                return networkManager_->sendToClient(clientId, data, len);
            }
        );
    }
    
    // 2. 设置路由层的消息处理回调
    if (messageRouter_ && messageHandler_) {
        messageRouter_->setMessageHandler(
            [this](const base::Message& message, uint8_t clientId) {
                messageHandler_->processClientMessage(message, clientId);
            }
        );
    }
    
    // 3. 设置消息处理器的转发回调
    if (messageHandler_ && messageRouter_) {
        messageHandler_->setMessageForwardCallback(
            [this](const base::Message& message, uint8_t clientId) {
                messageRouter_->sendMessage(message, clientId);
            }
        );
    }
    
    // 4. 设置网络管理器的回调
    if (networkManager_ && messageRouter_) {
        networkManager_->setMessageCallback(
            [this](const zexuan::net::TcpConnectionPtr& conn, const char* data, size_t len) {
                messageRouter_->handleNetworkData(conn, data, len);
            }
        );
    }
    
    logger_->info("组件回调关系设置完成");
}

void SystemManager::signalHandler(int sig) {
    if (instance_) {
        auto logger = zexuan::Logger::getFileLogger("system");
        const char* sigName = (sig == SIGINT ? "SIGINT" : 
                              sig == SIGTERM ? "SIGTERM" : 
                              sig == SIGHUP ? "SIGHUP" :
                              "UNKNOWN");
        
        logger->info("收到信号 {} ({})", sig, sigName);
        
        if (sig == SIGHUP) {
            // SIGHUP信号：重新加载配置（systemd reload）
            logger->info("收到重载信号，暂不支持热重载配置");
            // 这里可以添加配置重载逻辑
            return;
        }
        
        // SIGINT/SIGTERM：优雅停止
        logger->info("开始优雅退出...");
        std::printf("Received signal %s, shutting down gracefully...\n", sigName);
        
        // 设置信号处理为默认，防止重复信号
        signal(sig, SIG_DFL);
        
        // 异步停止，避免在信号处理器中执行复杂操作
        std::thread([sig]() {
            try {
                instance_->stop();
            } catch (const std::exception& e) {
                auto logger = zexuan::Logger::getConsoleLogger();
                logger->error("优雅退出过程中发生异常: {}", e.what());
            } catch (...) {
                auto logger = zexuan::Logger::getConsoleLogger();
                logger->error("优雅退出过程中发生未知异常");
            }
            // 不在信号处理线程中直接exit，交由主线程在waitForStop返回后退出
            return;
        }).detach();
        
    } else {
        // 如果实例不存在，直接退出
        auto logger = zexuan::Logger::getConsoleLogger();
        logger->warn("收到信号但SystemManager实例不存在，直接退出");
        exit(sig);
    }
}

} // namespace server
} // namespace zexuan

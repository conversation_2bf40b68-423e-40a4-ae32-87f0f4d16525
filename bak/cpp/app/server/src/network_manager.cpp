/**
 * @file network_manager.cpp
 * @brief 网络层管理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "network_manager.hpp"
#include "message_handler.hpp"
#include "zexuan/net/Buffer.h"
#include <algorithm>

namespace zexuan {
namespace server {

NetworkManager::NetworkManager(uint16_t port, int threadNum)
    : port_(port)
    , threadNum_(threadNum)
    , running_(false) {
    
    logger_ = zexuan::Logger::getFileLogger("network");
    logger_->info("NetworkManager 初始化，端口: {}, 线程数: {}", port_, threadNum_);
}

NetworkManager::~NetworkManager() {
    stop();
    logger_->info("NetworkManager 销毁");
}

bool NetworkManager::start() {
    if (running_.load()) {
        logger_->warn("NetworkManager 已经在运行");
        return true;
    }
    
    try {
        // 先设置运行标志
        running_ = true;
        
        // 在独立线程中创建和启动所有network对象
        // 这确保EventLoop和TcpServer在同一个线程中创建和运行
        loopThread_ = std::make_unique<std::thread>([this]() {
            // 在这个线程中创建EventLoop
            loop_ = std::make_unique<zexuan::net::EventLoop>();
            
            // 创建监听地址
            zexuan::net::InetAddress listenAddr(port_);
            
            // 在同一个线程中创建TcpServer
            server_ = std::make_unique<zexuan::net::TcpServer>(
                loop_.get(), listenAddr, "NetworkServer");
            
            // 设置回调函数
            server_->setConnectionCallback(
                std::bind(&NetworkManager::onConnection, this, std::placeholders::_1));
            
            server_->setMessageCallback(
                std::bind(&NetworkManager::onMessage, this, 
                         std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
            
            server_->setWriteCompleteCallback(
                std::bind(&NetworkManager::onWriteComplete, this, std::placeholders::_1));
            
            // 设置线程数，TcpServer会自动管理EventLoopThreadPool
            server_->setThreadNum(threadNum_);
            
            // 启动TcpServer
            server_->start();
            logger_->info("TCP服务器启动，监听端口: {}, 工作线程数: {}", port_, threadNum_);
            
            // 运行EventLoop
            loop_->loop();
        });
        
        logger_->info("NetworkManager 启动成功");
        return true;
    }
    catch (const std::exception& e) {
        logger_->error("NetworkManager 启动失败: {}", e.what());
        return false;
    }
}

void NetworkManager::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_ = false;
    logger_->info("正在停止 NetworkManager...");
    
    // 停止事件循环
    if (loop_) {
        loop_->quit();
    }
    
    // 等待事件循环线程结束
    if (loopThread_ && loopThread_->joinable()) {
        loopThread_->join();
    }
    
    server_.reset();
    loop_.reset();
    loopThread_.reset();
    
    logger_->info("NetworkManager 已停止");
}

void NetworkManager::setMessageCallback(std::function<void(const zexuan::net::TcpConnectionPtr&, const char*, size_t)> callback) {
    messageCallback_ = std::move(callback);
    logger_->info("设置消息处理回调函数");
}

void NetworkManager::onConnection(const zexuan::net::TcpConnectionPtr& conn) {
    if (conn->connected()) {
        logger_->info("新客户端连接: {}", conn->peerAddress().toIpPort());
    } else {
        logger_->info("客户端断开连接: {}", conn->peerAddress().toIpPort());
        // 连接断开时自动清理映射
        std::lock_guard<std::mutex> lock(clientMapMutex_);
        for (auto it = clientConnections_.begin(); it != clientConnections_.end();) {
            auto sharedConn = it->second.lock();
            if (!sharedConn || sharedConn.get() == conn.get()) {
                it = clientConnections_.erase(it);
            } else {
                ++it;
            }
        }
    }
}

void NetworkManager::onMessage(const zexuan::net::TcpConnectionPtr& conn,
                            zexuan::net::Buffer* buf,
                            zexuan::net::Timestamp receiveTime) {
    // 快速处理，不执行耗时操作
    size_t readable = buf->readableBytes();
    if (readable == 0) {
        return;
    }
    
    // 获取数据指针
    const char* data = buf->peek();
    
    // 尝试解析消息以获取客户端ID并注册到映射中
    if (readable >= 9) { // 至少需要消息头的基本字段
        // 检查起始字符
        if (static_cast<uint8_t>(data[0]) == 0x68) {
            // 解析SOURCE字段（第7个字节）
            uint8_t clientId = static_cast<uint8_t>(data[6]);
            
            logger_->debug("解析到客户端ID: {} from {}", 
                          static_cast<int>(clientId), 
                          conn->peerAddress().toIpPort());
            
            if (clientId != 0 && clientId != 255) { // 0和255是特殊值
                std::lock_guard<std::mutex> lock(clientMapMutex_);
                
                // 检查是否已存在该客户端ID的映射
                auto existingIt = clientConnections_.find(clientId);
                if (existingIt != clientConnections_.end()) {
                    auto existingConn = existingIt->second.lock();
                    if (existingConn && existingConn != conn) {
                        logger_->warn("客户端ID {} 已存在其他连接，更新映射", static_cast<int>(clientId));
                    }
                }
                
                clientConnections_[clientId] = conn;
                logger_->info("注册客户端ID {} 到连接: {}", 
                             static_cast<int>(clientId), 
                             conn->peerAddress().toIpPort());
            } else {
                logger_->debug("跳过特殊客户端ID: {} (0=系统, 255=广播)", static_cast<int>(clientId));
            }
        } else {
            logger_->warn("无效的起始字符: 0x{:02X}", static_cast<uint8_t>(data[0]));
        }
    } else {
        logger_->debug("消息数据不足，无法解析客户端ID");
    }
    
    // 通过回调函数处理消息
    if (messageCallback_) {
        messageCallback_(conn, data, readable);
    } else {
        logger_->warn("消息处理回调未设置，无法处理消息");
    }
    
    // 从缓冲区中移除已处理的数据
    buf->retrieveAll();
    
    logger_->debug("收到消息，长度: {} 字节，来自: {}", 
                  readable, conn->peerAddress().toIpPort());
}

void NetworkManager::onWriteComplete(const zexuan::net::TcpConnectionPtr& conn) {
    logger_->debug("数据发送完成，连接: {}", conn->peerAddress().toIpPort());
}

bool NetworkManager::sendToClient(uint8_t clientId, const char* data, size_t len) {
    if (!data || len == 0) {
        logger_->warn("发送消息为空，忽略");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(clientMapMutex_);
    auto it = clientConnections_.find(clientId);
    if (it == clientConnections_.end()) {
        logger_->warn("客户端ID {} 不存在或已断开连接", static_cast<int>(clientId));
        return false;
    }
    
    auto conn = it->second.lock();
    if (!conn || !conn->connected()) {
        logger_->warn("客户端ID {} 的连接已失效，清理映射", static_cast<int>(clientId));
        clientConnections_.erase(it);
        return false;
    }
    
    try {
        conn->send(data, len);
        logger_->debug("成功发送消息给客户端 {} ({} bytes)", 
                      static_cast<int>(clientId), len);
        return true;
    } catch (const std::exception& e) {
        logger_->error("向客户端 {} 发送消息失败: {}", 
                      static_cast<int>(clientId), e.what());
        return false;
    }
}

size_t NetworkManager::getConnectionCount() const {
    std::lock_guard<std::mutex> lock(clientMapMutex_);
    return clientConnections_.size();
}

} // namespace server
} // namespace zexuan

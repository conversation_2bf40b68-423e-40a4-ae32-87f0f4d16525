/**
 * @file config.cpp
 * @brief 配置系统实现 - 基于ConfigLoader
 * <AUTHOR>
 * @date 2024
 */

#include "config.hpp"
#include <fstream>
#include <filesystem>
#include <nlohmann/json.hpp>
#include <stdexcept>

namespace zexuan {
namespace server {

Config::Config() : loaded_(false) {
}

bool Config::loadFromFile(const std::string& configPath) {
    try {
        if (!std::filesystem::exists(configPath)) {
            return false;
        }

        loader_.loadFromFile(configPath);
        configPath_ = configPath;
        loaded_ = true;
        return true;
    }
    catch (const std::exception& e) {
        loaded_ = false;
        return false;
    }
}

bool Config::validate() const {
    if (!loaded_) {
        return false;
    }

    // 验证服务器配置
    auto server = getServer();
    if (server.port == 0 || server.port > 65535) {
        return false;
    }

    if (server.threadNum < 0) {
        return false;
    }

    // 验证插件配置
    auto plugins = getPlugins();
    for (const auto& plugin : plugins) {
        if (plugin.id <= 0) {
            return false;
        }
        if (plugin.name.empty()) {
            return false;
        }
        if (plugin.libraryPath.empty()) {
            return false;
        }
    }

    return true;
}

std::vector<PluginConfig> Config::getPlugins() const {
    std::vector<PluginConfig> plugins;
    
    if (!loaded_) {
        return plugins;
    }

    // 获取plugins数组
    std::vector<nlohmann::json> pluginArray = loader_.get("plugins", std::vector<nlohmann::json>());
    
    for (const auto& pluginJson : pluginArray) {
        PluginConfig plugin;
        
        if (pluginJson.contains("id") && pluginJson["id"].is_number_integer()) {
            plugin.id = pluginJson["id"];
        } else {
            continue; // 跳过无效插件
        }
        
        if (pluginJson.contains("name") && pluginJson["name"].is_string()) {
            plugin.name = pluginJson["name"];
        } else {
            continue; // 跳过无效插件
        }
        
        if (pluginJson.contains("library_path") && pluginJson["library_path"].is_string()) {
            plugin.libraryPath = pluginJson["library_path"];
        } else {
            continue; // 跳过无效插件
        }
        
        if (pluginJson.contains("enabled") && pluginJson["enabled"].is_boolean()) {
            plugin.enabled = pluginJson["enabled"];
        } else {
            plugin.enabled = false; // 默认不启用
        }
        
        plugins.push_back(plugin);
    }
    
    return plugins;
}

ServerConfig Config::getServer() const {
    ServerConfig server;
    
    if (!loaded_) {
        throw std::runtime_error("配置未加载");
    }

    // 从server对象中读取配置，需要手动解析嵌套JSON
    try {
        // ConfigLoader不支持嵌套路径，需要手动获取server对象
        nlohmann::json serverObj = loader_.get("server", nlohmann::json{});
        
        if (serverObj.empty()) {
            throw std::runtime_error("server 配置节点不存在");
        }
        
        // 检查必需的字段
        if (!serverObj.contains("port")) {
            throw std::runtime_error("server.port 配置缺失");
        }
        if (!serverObj.contains("thread_num")) {
            throw std::runtime_error("server.thread_num 配置缺失");
        }
        
        server.port = serverObj["port"];
        server.threadNum = serverObj["thread_num"];
        
        // 验证配置值的有效性
        if (server.port == 0 || server.port > 65535) {
            throw std::runtime_error("server.port 配置无效，必须在1-65535范围内");
        }
        if (server.threadNum <= 0) {
            throw std::runtime_error("server.thread_num 配置无效，必须大于0");
        }
        
    } catch (const std::exception& e) {
        throw std::runtime_error("服务器配置不完整: " + std::string(e.what()));
    }
    
    return server;
}

// 移除热重载配置和默认配置创建功能

} // namespace server
} // namespace zexuan
/**
 * @file main.cpp
 * @brief 服务器主程序入口 - 适配systemd
 * <AUTHOR>
 * @date 2024
 */

#include "system_manager.hpp"
#include "config.hpp"
#include "zexuan/logger.hpp"
#include <iostream>
#include <filesystem>
#include <string>
#include <cstdlib>

using namespace zexuan::server;

int main(int argc, char* argv[]) {
    // 检查命令行参数
    if (argc < 2) {
        std::fprintf(stderr, "用法: %s <配置文件路径>\n", argv[0]);
        std::fprintf(stderr, "示例: %s config.json\n", argv[0]);
        return 1;
    }
    
    std::string configPath = argv[1];
    
    // systemd环境下主要使用文件日志，减少控制台输出
    auto logger = zexuan::Logger::getFileLogger("main");
    logger->info("=== Zexuan Server 启动 ===");
    logger->info("配置文件: {}", configPath);
    logger->info("工作目录: {}", std::filesystem::current_path().string());
    
    // systemd启动通知
    std::printf("Zexuan Server starting with config: %s\n", configPath.c_str());
    
    try {
        // 检查配置文件是否存在
        if (!std::filesystem::exists(configPath)) {
            logger->error("配置文件不存在: {}", configPath);
            std::fprintf(stderr, "错误: 配置文件不存在: %s\n", configPath.c_str());
            return 1;
        }
        
        // 创建系统管理器
        SystemManager systemManager;
        
        // 初始化系统
        if (!systemManager.initialize(configPath)) {
            logger->error("系统初始化失败");
            std::fprintf(stderr, "错误: 系统初始化失败\n");
            return 1;
        }
        
        // 启动系统
        if (!systemManager.start()) {
            logger->error("系统启动失败");
            std::fprintf(stderr, "错误: 系统启动失败\n");
            return 1;
        }
        
        logger->info("=== 服务器启动成功 ===");
        std::printf("Zexuan Server started successfully\n");
        std::fflush(stdout); // 确保systemd能立即看到输出
        
        // 等待停止信号
        systemManager.waitForStop();
        
        logger->info("=== 服务器正在停止 ===");
        std::printf("Zexuan Server stopping...\n");
        
        logger->info("=== 服务器已停止 ===");
        return 0;
    }
    catch (const std::exception& e) {
        logger->error("程序异常: {}", e.what());
        std::fprintf(stderr, "错误: 程序异常: %s\n", e.what());
        return 1;
    }
    catch (...) {
        logger->error("未知异常");
        std::fprintf(stderr, "错误: 未知异常\n");
        return 1;
    }
}
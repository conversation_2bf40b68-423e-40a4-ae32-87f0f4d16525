/**
 * @file message_router.cpp
 * @brief 消息路由层实现
 * <AUTHOR>
 * @date 2024
 */

#include "message_router.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/logger.hpp"

namespace zexuan {
namespace server {

MessageRouter::MessageRouter()
    : logger_(zexuan::Logger::getFileLogger("message_router")) {
    logger_->info("MessageRouter 初始化");
}

MessageRouter::~MessageRouter() {
    logger_->info("MessageRouter 销毁");
}

void MessageRouter::setMessageHandler(MessageHandlerCallback callback) {
    messageHandler_ = std::move(callback);
    logger_->info("设置消息处理回调");
}

void MessageRouter::setNetworkSendCallback(NetworkSendCallback callback) {
    networkSendCallback_ = std::move(callback);
    logger_->info("设置网络发送回调");
}

void MessageRouter::handleNetworkData(const zexuan::net::TcpConnectionPtr& conn, 
                                     const char* data, 
                                     size_t len) {
    size_t processed = 0;
    
    while (processed < len) {
        base::Message message;
        
        // 尝试解析消息
        int parseResult = parseMessage(data + processed, len - processed, message);
        
        if (parseResult > 0) {
            // 解析成功，处理消息
            uint8_t clientId = message.getSource();
            
            logger_->debug("解析消息成功: COT={}, Source={}, Target={}, INF={}, 来自连接: {}", 
                          static_cast<int>(message.getCot()),
                          static_cast<int>(message.getSource()),
                          static_cast<int>(message.getTarget()),
                          static_cast<int>(message.getInf()),
                          conn->peerAddress().toIpPort());
            
            // 调用客户端消息处理回调
            if (messageHandler_) {
                messageHandler_(message, clientId);
            } else {
                logger_->error("消息处理回调未设置");
            }
            
            processed += parseResult;
        } else if (parseResult == 0) {
            // 需要更多数据
            logger_->debug("需要更多数据来完成消息解析");
            break;
        } else {
            // 解析错误
            logger_->error("消息解析错误，丢弃剩余数据");
            break;
        }
    }
}

void MessageRouter::sendMessage(const base::Message& message, uint8_t clientId) {
    if (clientId == 255) {
        // 广播消息
        logger_->debug("广播消息给所有客户端");
        if (networkSendCallback_) {
            // 序列化消息
            std::vector<uint8_t> data;
            message.serialize(data);
            networkSendCallback_(255, reinterpret_cast<const char*>(data.data()), data.size());
        } else {
            logger_->warn("网络发送回调未设置，无法发送消息");
        }
    } else {
        // 发送给指定客户端
        logger_->debug("发送消息给客户端: {}", clientId);
        if (networkSendCallback_) {
            // 序列化消息
            std::vector<uint8_t> data;
            message.serialize(data);
            networkSendCallback_(clientId, reinterpret_cast<const char*>(data.data()), data.size());
        } else {
            logger_->warn("网络发送回调未设置，无法发送消息");
        }
    }
}

int MessageRouter::parseMessage(const char* data, size_t len, base::Message& message) {
    // 至少需要消息头的基本字段
    const size_t MIN_HEADER_SIZE = 9; // START(1) + LENGTH(2) + TYP(1) + VSQ(1) + COT(1) + SOURCE(1) + TARGET(1) + FUN(1) + INF(1)
    
    if (len < MIN_HEADER_SIZE) {
        return 0; // 需要更多数据
    }
    
    // 检查起始字符
    if (static_cast<uint8_t>(data[0]) != 0x68) {
        logger_->error("无效的起始字符: 0x{:02X}", static_cast<uint8_t>(data[0]));
        return -1;
    }
    
    // 解析长度字段
    uint16_t length = static_cast<uint8_t>(data[1]) | (static_cast<uint8_t>(data[2]) << 8);
    size_t totalLength = 3 + length; // START(1) + LENGTH(2) + ASDU(length)
    
    if (len < totalLength) {
        return 0; // 需要更多数据
    }
    
    // 使用Message类的反序列化功能
    std::vector<uint8_t> buffer(data, data + totalLength);
    size_t parsed = message.deserialize(buffer, 0);
    
    if (parsed == 0) {
        logger_->error("消息反序列化失败");
        return -1;
    }
    
    return static_cast<int>(parsed);
}

} // namespace server
} // namespace zexuan

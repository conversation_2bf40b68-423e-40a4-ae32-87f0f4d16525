/**
 * @file plugin_manager.cpp
 * @brief 插件管理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "plugin_manager.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include <algorithm>

namespace zexuan {
namespace server {

PluginManager::PluginManager(std::shared_ptr<base::Mediator> mediator)
    : initialized_(false) {

    logger_ = zexuan::Logger::getFileLogger("plugin");
    logger_->info("PluginManager 初始化");

    mediator_ = std::move(mediator);
    if (!mediator_) {
        logger_->error("注入的 Mediator 为空");
    }
}

PluginManager::~PluginManager() {
    unloadPlugins();
    logger_->info("PluginManager 销毁");
}

bool PluginManager::loadFromConfig(const Config& config) {
    logger_->info("从配置加载插件...");
    
    const auto& pluginConfigs = config.getPlugins();
    bool allSuccess = true;
    
    for (const auto& pluginConfig : pluginConfigs) {
        if (pluginConfig.enabled) {
            if (!loadPlugin(pluginConfig)) {
                logger_->error("加载插件失败: {} (ID: {})", pluginConfig.name, pluginConfig.id);
                allSuccess = false;
            }
        } else {
            logger_->info("插件 {} (ID: {}) 被禁用，跳过加载", pluginConfig.name, pluginConfig.id);
        }
    }
    
    logger_->info("插件加载完成，成功: {}/{}", 
                 getLoadedPluginCount(), pluginConfigs.size());
    
    return allSuccess;
}

bool PluginManager::initializePlugins() {
    logger_->info("初始化所有插件...");
    
    bool allSuccess = true;
    for (auto& [id, info] : plugins_) {
        if (info.loaded && info.instance) {
            try {
                // 这里可以调用插件的初始化方法
                // 由于Observer接口没有init方法，我们假设插件在构造时已经初始化
                logger_->info("插件 {} (ID: {}) 初始化成功", info.name, info.id);
            }
            catch (const std::exception& e) {
                logger_->error("插件 {} (ID: {}) 初始化失败: {}", info.name, info.id, e.what());
                allSuccess = false;
            }
        }
    }
    
    initialized_ = allSuccess;
    return allSuccess;
}

bool PluginManager::startPlugins() {
    if (!initialized_) {
        logger_->error("插件未初始化，无法启动");
        return false;
    }
    
    logger_->info("启动所有插件...");
    
    bool allSuccess = true;
    for (auto& [id, info] : plugins_) {
        if (info.loaded && info.instance) {
            try {
                // 这里可以调用插件的启动方法
                // 由于Observer接口没有start方法，插件启动逻辑可能在注册到mediator后自动开始
                logger_->info("插件 {} (ID: {}) 启动成功", info.name, info.id);
            }
            catch (const std::exception& e) {
                logger_->error("插件 {} (ID: {}) 启动失败: {}", info.name, info.id, e.what());
                allSuccess = false;
            }
        }
    }
    
    return allSuccess;
}

void PluginManager::stopPlugins() {
    logger_->info("停止所有插件...");
    
    for (auto& [id, info] : plugins_) {
        if (info.loaded && info.instance) {
            try {
                // 这里可以调用插件的停止方法
                logger_->info("插件 {} (ID: {}) 停止成功", info.name, info.id);
            }
            catch (const std::exception& e) {
                logger_->error("插件 {} (ID: {}) 停止失败: {}", info.name, info.id, e.what());
            }
        }
    }
}

void PluginManager::unloadPlugins() {
    logger_->info("卸载所有插件...");
    
    // 使用迭代器安全地删除元素
    for (auto it = plugins_.begin(); it != plugins_.end();) {
        if (it->second.loaded) {
            destroyPluginInstance(it->second);
            it = plugins_.erase(it);  // erase返回下一个有效的迭代器
        } else {
            ++it;
        }
    }
    
    // 明确卸载所有动态库
    logger_->info("卸载动态库...");
    loader_.unloadAllLibraries();
    
    logger_->info("所有插件已卸载");
}

bool PluginManager::loadPlugin(const PluginConfig& pluginConfig) {
    logger_->info("加载插件: {} (ID: {}, 路径: {})", 
                 pluginConfig.name, pluginConfig.id, pluginConfig.libraryPath);
    
    // 检查插件是否已加载
    if (plugins_.find(pluginConfig.id) != plugins_.end()) {
        logger_->warn("插件 {} (ID: {}) 已加载", pluginConfig.name, pluginConfig.id);
        return true;
    }
    
    // 创建插件信息
    PluginInfo info;
    info.id = pluginConfig.id;
    info.name = pluginConfig.name;
    info.path = pluginConfig.libraryPath;
    info.enabled = pluginConfig.enabled;
    
    // 加载动态库
    if (!loader_.loadLibrary(pluginConfig.libraryPath)) {
        logger_->error("加载动态库失败: {}, 错误: {}", 
                      pluginConfig.libraryPath, loader_.getLastError());
        return false;
    }
    
    // 获取创建函数
    info.createFunc = loader_.getFunction<CreatePluginFunc>(
        pluginConfig.libraryPath, "create_plugin");
    if (!info.createFunc) {
        logger_->error("获取create_plugin函数失败: {}", loader_.getLastError());
        return false;
    }
    
    // 获取销毁函数
    info.destroyFunc = loader_.getFunction<DestroyPluginFunc>(
        pluginConfig.libraryPath, "destroy_plugin");
    if (!info.destroyFunc) {
        logger_->error("获取destroy_plugin函数失败: {}", loader_.getLastError());
        return false;
    }
    
    // 创建插件实例
    if (!createPluginInstance(info)) {
        return false;
    }
    
    // PluginBase会自动注册到mediator，无需手动注册
    
    info.loaded = true;
    plugins_[pluginConfig.id] = info;
    
    logger_->info("插件 {} (ID: {}) 加载成功", pluginConfig.name, pluginConfig.id);
    return true;
}

bool PluginManager::unloadPlugin(int pluginId) {
    auto it = plugins_.find(pluginId);
    if (it == plugins_.end()) {
        logger_->warn("插件ID {} 不存在", pluginId);
        return false;
    }
    
    logger_->info("卸载插件: {} (ID: {})", it->second.name, pluginId);
    
    destroyPluginInstance(it->second);
    plugins_.erase(it);
    
    logger_->info("插件 {} (ID: {}) 卸载成功", it->second.name, pluginId);
    return true;
}

bool PluginManager::reloadPlugin(int pluginId) {
    auto it = plugins_.find(pluginId);
    if (it == plugins_.end()) {
        logger_->warn("插件ID {} 不存在，无法重载", pluginId);
        return false;
    }
    
    logger_->info("重载插件: {} (ID: {})", it->second.name, pluginId);
    
    // 保存插件配置
    PluginConfig config;
    config.id = it->second.id;
    config.name = it->second.name;
    config.libraryPath = it->second.path;
    config.enabled = it->second.enabled;
    
    // 先卸载
    if (!unloadPlugin(pluginId)) {
        return false;
    }
    
    // 再加载
    return loadPlugin(config);
}

const PluginInfo* PluginManager::getPluginInfo(int pluginId) const {
    auto it = plugins_.find(pluginId);
    return (it != plugins_.end()) ? &(it->second) : nullptr;
}

std::vector<PluginInfo> PluginManager::getAllPluginInfo() const {
    std::vector<PluginInfo> result;
    result.reserve(plugins_.size());
    
    for (const auto& [id, info] : plugins_) {
        result.push_back(info);
    }
    
    return result;
}

bool PluginManager::isPluginLoaded(int pluginId) const {
    return plugins_.find(pluginId) != plugins_.end();
}

size_t PluginManager::getLoadedPluginCount() const {
    return plugins_.size();
}

bool PluginManager::createPluginInstance(PluginInfo& info) {
    try {
        if (!info.createFunc) {
            logger_->error("插件 {} (ID: {}) 创建函数为空", info.name, info.id);
            return false;
        }
        
        // 调用插件的创建函数 - 传递mediator指针的地址
        info.instance = info.createFunc(&mediator_, info.id, info.name.c_str());
        
        if (!info.instance) {
            logger_->error("插件 {} (ID: {}) 创建实例失败", info.name, info.id);
            return false;
        }
        
        // 调用插件的初始化方法
        if (!info.instance->initialize()) {
            logger_->error("插件 {} (ID: {}) 初始化失败", info.name, info.id);
            destroyPluginInstance(info);
            return false;
        }
        
        logger_->debug("插件 {} (ID: {}) 实例创建并初始化成功", info.name, info.id);
        return true;
    }
    catch (const std::exception& e) {
        logger_->error("插件 {} (ID: {}) 创建实例异常: {}", info.name, info.id, e.what());
        return false;
    }
}

void PluginManager::destroyPluginInstance(PluginInfo& info) {
    if (info.instance) {
        try {
            // 调用插件的关闭方法
            info.instance->shutdown();
            
            // 销毁插件实例
            if (info.destroyFunc) {
                info.destroyFunc(info.instance);
            } else {
                // 如果没有销毁函数，直接删除（不推荐）
                delete info.instance;
            }
            
            logger_->debug("插件 {} (ID: {}) 实例销毁成功", info.name, info.id);
        }
        catch (const std::exception& e) {
            logger_->error("插件 {} (ID: {}) 销毁实例异常: {}", info.name, info.id, e.what());
        }
        
        info.instance = nullptr;
    }
}

// PluginBase会自动处理mediator的注册和注销，这里不再需要手动处理

} // namespace server
} // namespace zexuan

#!/bin/bash

echo "安装 Zexuan Server systemd 服务..."

# 检查服务器文件是否存在
if [[ ! -f "/root/zexuan/cpp/bin/server" ]]; then
    echo "错误: 服务器可执行文件不存在，请先编译: make server"
    exit 1
fi

# 安装systemd服务
cp /root/zexuan/cpp/zexuan-server.service /etc/systemd/system/
systemctl daemon-reload
systemctl enable zexuan-server

echo "✓ 服务已安装并启用开机自启"
echo ""
echo "常用管理命令:"
echo "  systemctl start zexuan-server     # 启动服务"
echo "  systemctl stop zexuan-server      # 停止服务"  
echo "  systemctl restart zexuan-server   # 重启服务"
echo "  systemctl reload zexuan-server    # 重载配置 (发送SIGHUP)"
echo "  systemctl status zexuan-server    # 查看状态"
echo "  systemctl disable zexuan-server   # 禁用开机自启"
echo ""
echo "日志查看:"
echo "  journalctl -u zexuan-server -f    # 实时日志"
echo "  journalctl -u zexuan-server -n 50 # 最近50行"
echo "  tail -f /root/zexuan/cpp/logs/system.log  # 系统日志文件"
echo ""
echo "现在可以使用: systemctl start zexuan-server"
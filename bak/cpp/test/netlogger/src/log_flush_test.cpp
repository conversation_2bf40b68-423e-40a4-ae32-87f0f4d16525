#include "zexuan/logger.hpp"
#include <iostream>
#include <thread>
#include <chrono>

using namespace zexuan;

int main() {
    std::cout << "测试日志刷新功能..." << std::endl;
    
    auto logger = Logger::getFileLogger("net");
    
    logger->trace("TRACE: 测试日志刷新");
    logger->debug("DEBUG: 测试日志刷新");
    logger->info("INFO: 测试日志刷新");
    logger->warn("WARN: 测试日志刷新");
    logger->error("ERROR: 测试日志刷新");
    logger->critical("CRITICAL: 测试日志刷新");
    
    std::cout << "日志已写入，程序即将退出..." << std::endl;
    std::cout << "请检查 logs/net.log 文件是否包含上述日志。" << std::endl;
    
    return 0;
} 
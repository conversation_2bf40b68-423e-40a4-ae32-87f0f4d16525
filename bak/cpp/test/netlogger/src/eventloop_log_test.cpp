#include "zexuan/logger.hpp"
#include "zexuan/net/EventLoop.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace zexuan;
using namespace zexuan::net;

int main() {
    std::cout << "开始测试EventLoop日志功能..." << std::endl;
    
    // 测试基本日志功能
    auto logger = Logger::getFileLogger("net");
    logger->info("=== 开始EventLoop日志测试 ===");
    
    // 创建EventLoop
    std::cout << "创建EventLoop..." << std::endl;
    EventLoop loop;
    
    // 运行EventLoop一小段时间
    std::cout << "运行EventLoop 2秒..." << std::endl;
    std::thread([&loop]() {
        std::this_thread::sleep_for(std::chrono::seconds(2));
        loop.quit();
    }).detach();
    
    loop.loop();
    
    logger->info("EventLoop测试完成");
    
    std::cout << "测试完成！请检查 logs/net.log 文件。" << std::endl;
    
    return 0;
} 
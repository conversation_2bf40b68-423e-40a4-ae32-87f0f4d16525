#include "zexuan/logger.hpp"
#include <iostream>
#include <thread>
#include <chrono>

using namespace zexuan;

int main() {
    std::cout << "自动刷新测试程序" << std::endl;
    std::cout << "这个程序会每500ms输出一条日志，验证1秒自动刷新功能" << std::endl;
    
    auto logger = Logger::getFileLogger("net");
    logger->info("程序启动，开始测试自动刷新功能");
    
    int count = 0;
    while (count < 10) {  // 只运行10次，避免无限循环
        count++;
        
        // 输出不同级别的日志
        logger->trace("TRACE: 测试日志 #{}", count);
        logger->debug("DEBUG: 测试日志 #{}", count);
        logger->info("INFO: 测试日志 #{}", count);
        
        std::cout << "输出日志 #" << count << "，等待500ms..." << std::endl;
        
        // 等待500ms
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
    
    logger->info("测试完成，程序即将退出");
    std::cout << "测试完成！请检查 logs/net.log 文件。" << std::endl;
    std::cout << "注意：日志应该每1秒自动刷新一次，重要日志会立即刷新。" << std::endl;
    
    return 0;
} 
#include "zexuan/logger.hpp"
#include "zexuan/net/EventLoop.h"
#include "zexuan/net/TcpServer.h"
#include "zexuan/net/InetAddress.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace zexuan;
using namespace zexuan::net;

int main() {
    std::cout << "开始测试ChatServer日志功能..." << std::endl;
    
    auto logger = Logger::getFileLogger("net");
    logger->info("=== 开始ChatServer日志测试 ===");
    logger->info("pid = {}", getpid());
    
    try {
        // 创建EventLoop
        EventLoop loop;
        logger->info("EventLoop创建成功");
        
        // 创建服务器地址
        uint16_t port = 8888;
        InetAddress serverAddr(port);
        logger->info("服务器地址: {}", serverAddr.toIpPort());
        
        // 创建ChatServer
        TcpServer server(&loop, serverAddr, "TestChatServer");
        logger->info("ChatServer创建成功");
        
        // 设置连接回调
        server.setConnectionCallback(
            [](const TcpConnectionPtr& conn) {
                auto logger = Logger::getFileLogger("net");
                logger->info("新连接: {} -> {} is {}", 
                           conn->peerAddress().toIpPort(),
                           conn->localAddress().toIpPort(),
                           conn->connected() ? "UP" : "DOWN");
            });
        
        // 设置消息回调
        server.setMessageCallback(
            [](const TcpConnectionPtr& conn, Buffer* buf, Timestamp) {
                auto logger = Logger::getFileLogger("net");
                logger->debug("收到消息，长度: {}", buf->readableBytes());
                buf->retrieveAll();
            });
        
        // 启动服务器
        server.start();
        logger->info("ChatServer启动成功，监听端口 {}", port);
        
        // 运行一小段时间
        std::thread([&loop]() {
            std::this_thread::sleep_for(std::chrono::seconds(3));
            loop.quit();
        }).detach();
        
        // 运行事件循环
        loop.loop();
        
        logger->info("ChatServer事件循环结束");
        
    } catch (const std::exception& e) {
        logger->error("ChatServer测试失败: {}", e.what());
        std::cerr << "ChatServer测试失败: " << e.what() << std::endl;
    }
    
    std::cout << "ChatServer日志测试完成！请检查 logs/net.log 文件。" << std::endl;
    
    return 0;
} 
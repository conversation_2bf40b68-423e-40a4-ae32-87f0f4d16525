#include "zexuan/logger.hpp"
#include <iostream>
#include <thread>
#include <chrono>

using namespace zexuan;

int main() {
    std::cout << "信号处理测试程序" << std::endl;
    std::cout << "这个程序会持续输出日志，按Ctrl+C测试信号处理" << std::endl;
    
    auto logger = Logger::getFileLogger("net");
    logger->info("程序启动，PID = {}", getpid());
    
    int count = 0;
    while (true) {
        logger->info("测试日志 #{} - 程序正在运行...", ++count);
        logger->debug("调试信息 #{}", count);
        
        std::cout << "日志 #" << count << " 已写入，按Ctrl+C退出..." << std::endl;
        
        // 等待2秒
        std::this_thread::sleep_for(std::chrono::seconds(2));
    }
    
    // 正常情况下不会到达这里
    logger->info("程序正常退出");
    return 0;
} 
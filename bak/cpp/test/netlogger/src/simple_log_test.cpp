#include "zexuan/logger.hpp"
#include <iostream>
#include <thread>
#include <chrono>

using namespace zexuan;

int main() {
    std::cout << "开始测试所有等级的日志输出..." << std::endl;
    
    // 测试控制台日志
    auto console_logger = Logger::getConsoleLogger();
    std::cout << "\n=== 控制台日志测试 ===" << std::endl;
    
    console_logger->trace("TRACE: 这是跟踪级别的日志");
    console_logger->debug("DEBUG: 这是调试级别的日志");
    console_logger->info("INFO: 这是信息级别的日志");
    console_logger->warn("WARN: 这是警告级别的日志");
    console_logger->error("ERROR: 这是错误级别的日志");
    console_logger->critical("CRITICAL: 这是严重错误级别的日志");
    
    // 测试文件日志
    auto file_logger = Logger::getFileLogger("net");
    std::cout << "\n=== 文件日志测试 ===" << std::endl;
    
    file_logger->trace("TRACE: 这是跟踪级别的日志");
    file_logger->debug("DEBUG: 这是调试级别的日志");
    file_logger->info("INFO: 这是信息级别的日志");
    file_logger->warn("WARN: 这是警告级别的日志");
    file_logger->error("ERROR: 这是错误级别的日志");
    file_logger->critical("CRITICAL: 这是严重错误级别的日志");
    
    // 测试网络模块的日志
    std::cout << "\n=== 网络模块日志测试 ===" << std::endl;
    auto net_logger = Logger::getFileLogger("net");
    
    net_logger->trace("网络模块: TRACE - 连接建立过程");
    net_logger->debug("网络模块: DEBUG - 数据包接收");
    net_logger->info("网络模块: INFO - 新客户端连接");
    net_logger->warn("网络模块: WARN - 连接超时");
    net_logger->error("网络模块: ERROR - 网络错误");
    net_logger->critical("网络模块: CRITICAL - 致命错误");
    
    // 等待一下确保异步日志写入完成
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    std::cout << "请检查以下文件:" << std::endl;
    std::cout << "1. 控制台输出（上面应该显示了所有等级的日志）" << std::endl;
    std::cout << "2. logs/net.log 文件（应该包含所有等级的日志）" << std::endl;
    
    return 0;
} 


# 简单日志测试程序
add_executable(simple_log_test src/simple_log_test.cpp)
target_link_libraries(simple_log_test core)
target_include_directories(simple_log_test PRIVATE
    include
    ${CMAKE_SOURCE_DIR}/core/include
)

# EventLoop日志测试程序
add_executable(eventloop_log_test src/eventloop_log_test.cpp)
target_link_libraries(eventloop_log_test core)
target_include_directories(eventloop_log_test PRIVATE
    include
    ${CMAKE_SOURCE_DIR}/core/include
)

# 日志刷新测试程序
add_executable(log_flush_test src/log_flush_test.cpp)
target_link_libraries(log_flush_test core)
target_include_directories(log_flush_test PRIVATE
    include
    ${CMAKE_SOURCE_DIR}/core/include
)


# ChatServer日志测试程序
add_executable(chatserver_log_test src/chatserver_log_test.cpp)
target_link_libraries(chatserver_log_test core)
target_include_directories(chatserver_log_test PRIVATE
    include
    ${CMAKE_SOURCE_DIR}/core/include
)

# 信号处理测试程序
add_executable(signal_test src/signal_test.cpp)
target_link_libraries(signal_test core)
target_include_directories(signal_test PRIVATE
    include
    ${CMAKE_SOURCE_DIR}/core/include
)

# 自动刷新测试程序
add_executable(auto_flush_test src/auto_flush_test.cpp)
target_link_libraries(auto_flush_test core)
target_include_directories(auto_flush_test PRIVATE
    include
    ${CMAKE_SOURCE_DIR}/core/include
)


set_target_properties(simple_log_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/bin/test"
)
set_target_properties(eventloop_log_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/bin/test"
)
set_target_properties(log_flush_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/bin/test"
)

set_target_properties(chatserver_log_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/bin/test"
)
set_target_properties(signal_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/bin/test"
)
set_target_properties(auto_flush_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/bin/test"
)
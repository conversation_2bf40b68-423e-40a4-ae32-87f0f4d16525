#pragma once

#include <zip.h>
#include <iostream>
#include <string>
#include <vector>

/**
 * libzip API 使用示例
 * 
 * 主要API说明：
 * 
 * 1. 打开zip文件：
 *    zip_t* zip_open(const char* path, int flags, int* error_code);
 *    - flags: ZIP_RDONLY (只读), ZIP_CREATE (创建), ZIP_TRUNCATE (截断)
 * 
 * 2. 获取文件数量：
 *    zip_int64_t zip_get_num_entries(zip_t* archive, zip_flags_t flags);
 * 
 * 3. 获取文件信息：
 *    int zip_stat_index(zip_t* archive, zip_uint64_t index, zip_flags_t flags, zip_stat_t* stat);
 *    int zip_stat(zip_t* archive, const char* fname, zip_flags_t flags, zip_stat_t* stat);
 * 
 * 4. 打开zip内的文件：
 *    zip_file_t* zip_fopen_index(zip_t* archive, zip_uint64_t index, zip_flags_t flags);
 *    zip_file_t* zip_fopen(zip_t* archive, const char* fname, zip_flags_t flags);
 * 
 * 5. 读取文件内容：
 *    zip_int64_t zip_fread(zip_file_t* file, void* buf, zip_uint64_t nbytes);
 * 
 * 6. 关闭文件：
 *    int zip_fclose(zip_file_t* file);
 * 
 * 7. 关闭zip文件：
 *    int zip_close(zip_t* archive);
 * 
 * 8. 错误处理：
 *    const char* zip_strerror(zip_t* archive);
 *    void zip_error_to_str(char* buf, zip_uint64_t len, int ze, int se);
 */

class ZipApiExamples {
public:
    // 示例1: 列出zip文件内容
    static void list_zip_contents(const std::string& zip_path) {
        std::cout << "=== 列出zip文件内容: " << zip_path << " ===" << std::endl;
        
        int error_code;
        zip_t* zip = zip_open(zip_path.c_str(), 0, &error_code);
        if (!zip) {
            std::cerr << "无法打开zip文件: " << zip_path << " - 错误代码: " << error_code << std::endl;
            return;
        }
        
        zip_int64_t num_entries = zip_get_num_entries(zip, 0);
        std::cout << "文件总数: " << num_entries << std::endl;
        
        for (zip_uint64_t i = 0; i < static_cast<zip_uint64_t>(num_entries); i++) {
            zip_stat_t stat;
            if (zip_stat_index(zip, i, 0, &stat) == 0) {
                std::cout << "  [" << i << "] " << stat.name;
                std::cout << " (大小: " << stat.size << " 字节)";
                if (stat.valid & ZIP_STAT_MTIME) {
                    std::cout << " (修改时间: " << stat.mtime << ")";
                }
                std::cout << std::endl;
            }
        }
        
        zip_close(zip);
    }
    
    // 示例2: 检查zip文件是否有效
    static bool is_valid_zip(const std::string& zip_path) {
        int error_code;
        zip_t* zip = zip_open(zip_path.c_str(), 0, &error_code);
        if (!zip) {
            return false;
        }
        
        zip_close(zip);
        return true;
    }
    
    // 示例3: 获取zip文件信息
    static void get_zip_info(const std::string& zip_path) {
        std::cout << "=== 获取zip文件信息: " << zip_path << " ===" << std::endl;
        
        int error_code;
        zip_t* zip = zip_open(zip_path.c_str(), 0, &error_code);
        if (!zip) {
            std::cerr << "无法打开zip文件: " << zip_path << " - 错误代码: " << error_code << std::endl;
            return;
        }
        
        zip_int64_t num_entries = zip_get_num_entries(zip, 0);
        std::cout << "文件数量: " << num_entries << std::endl;
        
        // 计算总大小
        zip_uint64_t total_size = 0;
        for (zip_uint64_t i = 0; i < static_cast<zip_uint64_t>(num_entries); i++) {
            zip_stat_t stat;
            if (zip_stat_index(zip, i, 0, &stat) == 0) {
                total_size += stat.size;
            }
        }
        std::cout << "总大小: " << total_size << " 字节" << std::endl;
        
        zip_close(zip);
    }
    
    // 示例4: 读取zip中的文本文件
    static std::string read_text_file_from_zip(const std::string& zip_path, const std::string& file_name) {
        int error_code;
        zip_t* zip = zip_open(zip_path.c_str(), 0, &error_code);
        if (!zip) {
            return "";
        }
        
        zip_file_t* zip_file = zip_fopen(zip, file_name.c_str(), 0);
        if (!zip_file) {
            zip_close(zip);
            return "";
        }
        
        std::string content;
        char buffer[1024];
        zip_int64_t bytes_read;
        
        while ((bytes_read = zip_fread(zip_file, buffer, sizeof(buffer))) > 0) {
            content.append(buffer, bytes_read);
        }
        
        zip_fclose(zip_file);
        zip_close(zip);
        
        return content;
    }
    
    // 示例5: 检查zip中是否存在特定文件
    static bool file_exists_in_zip(const std::string& zip_path, const std::string& file_name) {
        int error_code;
        zip_t* zip = zip_open(zip_path.c_str(), 0, &error_code);
        if (!zip) {
            return false;
        }
        
        zip_stat_t stat;
        bool exists = (zip_stat(zip, file_name.c_str(), 0, &stat) == 0);
        
        zip_close(zip);
        return exists;
    }
    
    // 示例6: 获取zip中所有文件名
    static std::vector<std::string> get_file_list(const std::string& zip_path) {
        std::vector<std::string> files;
        
        int error_code;
        zip_t* zip = zip_open(zip_path.c_str(), 0, &error_code);
        if (!zip) {
            return files;
        }
        
        zip_int64_t num_entries = zip_get_num_entries(zip, 0);
        for (zip_uint64_t i = 0; i < static_cast<zip_uint64_t>(num_entries); i++) {
            zip_stat_t stat;
            if (zip_stat_index(zip, i, 0, &stat) == 0) {
                files.push_back(stat.name);
            }
        }
        
        zip_close(zip);
        return files;
    }
}; 
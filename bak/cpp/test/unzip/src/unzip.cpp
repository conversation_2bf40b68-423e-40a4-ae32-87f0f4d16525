#include <iostream>
#include <filesystem>
#include <vector>
#include <string>
#include <zip.h>
#include <cstring>
#include <sys/stat.h>

namespace fs = std::filesystem;

class ZipExtractor {
private:
    std::string extract_dir_;
    
    // 创建目录（包括父目录）
    bool create_directory(const std::string& path) {
        try {
            fs::create_directories(path);
            return true;
        } catch (const std::exception& e) {
            std::cerr << "创建目录失败: " << path << " - " << e.what() << std::endl;
            return false;
        }
    }
    
    // 获取文件权限
    mode_t get_file_permissions(zip_t* zip, zip_uint64_t index) {
        zip_stat_t stat;
        if (zip_stat_index(zip, index, 0, &stat) < 0) {
            return 0644; // 默认权限
        }
        // 注意：新版本的libzip可能不支持ZIP_STAT_ATTRIB
        return 0644; // 使用默认权限
    }
    
    // 解压单个文件
    bool extract_file(zip_t* zip, zip_uint64_t index, const std::string& output_path) {
        zip_file_t* zip_file = zip_fopen_index(zip, index, 0);
        if (!zip_file) {
            std::cerr << "无法打开zip文件中的文件: " << zip_strerror(zip) << std::endl;
            return false;
        }
        
        // 创建输出目录
        fs::path output_dir = fs::path(output_path).parent_path();
        if (!create_directory(output_dir.string())) {
            zip_fclose(zip_file);
            return false;
        }
        
        // 打开输出文件
        FILE* output_file = fopen(output_path.c_str(), "wb");
        if (!output_file) {
            std::cerr << "无法创建输出文件: " << output_path << std::endl;
            zip_fclose(zip_file);
            return false;
        }
        
        // 复制文件内容
        char buffer[8192];
        zip_int64_t bytes_read;
        while ((bytes_read = zip_fread(zip_file, buffer, sizeof(buffer))) > 0) {
            if (fwrite(buffer, 1, bytes_read, output_file) != static_cast<size_t>(bytes_read)) {
                std::cerr << "写入文件失败: " << output_path << std::endl;
                fclose(output_file);
                zip_fclose(zip_file);
                return false;
            }
        }
        
        fclose(output_file);
        zip_fclose(zip_file);
        
        // 设置文件权限（使用默认权限）
        chmod(output_path.c_str(), 0644);
        
        return true;
    }

public:
    ZipExtractor(const std::string& extract_dir) : extract_dir_(extract_dir) {
        // 确保输出目录存在
        create_directory(extract_dir_);
    }
    
    // 解压zip文件
    bool extract_zip(const std::string& zip_path) {
        std::cout << "正在解压: " << zip_path << std::endl;
        
        int error_code;
        zip_t* zip = zip_open(zip_path.c_str(), 0, &error_code);
        if (!zip) {
            std::cerr << "无法打开zip文件: " << zip_path << " - 错误代码: " << error_code << std::endl;
            return false;
        }
        
        zip_int64_t num_entries = zip_get_num_entries(zip, 0);
        std::cout << "  文件数量: " << num_entries << std::endl;
        
        bool success = true;
        for (zip_uint64_t i = 0; i < static_cast<zip_uint64_t>(num_entries); i++) {
            zip_stat_t stat;
            if (zip_stat_index(zip, i, 0, &stat) < 0) {
                std::cerr << "无法获取文件信息: " << zip_strerror(zip) << std::endl;
                success = false;
                continue;
            }
            
            // 跳过目录
            if (stat.name[strlen(stat.name) - 1] == '/') {
                std::cout << "  跳过目录: " << stat.name << std::endl;
                continue;
            }
            
            // 构建输出路径
            std::string output_path = extract_dir_ + "/" + stat.name;
            std::cout << "  解压文件: " << stat.name << " -> " << output_path << std::endl;
            
            if (!extract_file(zip, i, output_path)) {
                success = false;
            }
        }
        
        zip_close(zip);
        return success;
    }
    
    // 批量解压zip文件
    bool extract_multiple_zips(const std::vector<std::string>& zip_files) {
        bool all_success = true;
        
        for (const auto& zip_file : zip_files) {
            std::cout << "\n=== 开始解压: " << zip_file << " ===" << std::endl;
            
            if (!fs::exists(zip_file)) {
                std::cerr << "文件不存在: " << zip_file << std::endl;
                all_success = false;
                continue;
            }
            
            // 为每个zip文件创建单独的子目录
            std::string zip_name = fs::path(zip_file).stem().string();
            std::string sub_dir = extract_dir_ + "/" + zip_name;
            
            ZipExtractor extractor(sub_dir);
            if (!extractor.extract_zip(zip_file)) {
                all_success = false;
            }
            
            std::cout << "=== 解压完成: " << zip_file << " ===" << std::endl;
        }
        
        return all_success;
    }
};

int main() {
    std::cout << "=== libzip 解压测试程序 ===" << std::endl;
    
    // 设置解压目录
    std::string extract_base_dir = "/mnt/g/temp/extracted";
    
    // 获取所有zip文件
    std::vector<std::string> zip_files;
    std::string temp_dir = "/mnt/g/temp";
    
    try {
        for (const auto& entry : fs::directory_iterator(temp_dir)) {
            if (entry.is_regular_file() && entry.path().extension() == ".zip") {
                zip_files.push_back(entry.path().string());
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "读取目录失败: " << e.what() << std::endl;
        return 1;
    }
    
    if (zip_files.empty()) {
        std::cout << "在 " << temp_dir << " 中没有找到zip文件" << std::endl;
        return 0;
    }
    
    std::cout << "找到 " << zip_files.size() << " 个zip文件:" << std::endl;
    for (const auto& zip_file : zip_files) {
        std::cout << "  - " << fs::path(zip_file).filename().string() << std::endl;
    }
    
    // 创建解压器并开始解压
    ZipExtractor extractor(extract_base_dir);
    
    std::cout << "\n开始批量解压..." << std::endl;
    if (extractor.extract_multiple_zips(zip_files)) {
        std::cout << "\n所有文件解压成功！" << std::endl;
        std::cout << "解压目录: " << extract_base_dir << std::endl;
    } else {
        std::cout << "\n部分文件解压失败，请检查错误信息" << std::endl;
        return 1;
    }
    
    return 0;
}
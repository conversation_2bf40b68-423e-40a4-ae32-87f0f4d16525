#include "../include/zip_api_examples.hpp"
#include <iostream>

int main() {
    std::cout << "=== libzip API 测试程序 ===" << std::endl;
    
    // 测试第一个zip文件
    std::string test_zip = "/mnt/g/temp/1.zip";
    
    std::cout << "\n1. 检查zip文件是否有效..." << std::endl;
    if (ZipApiExamples::is_valid_zip(test_zip)) {
        std::cout << "✓ " << test_zip << " 是有效的zip文件" << std::endl;
    } else {
        std::cout << "✗ " << test_zip << " 不是有效的zip文件" << std::endl;
        return 1;
    }
    
    std::cout << "\n2. 获取zip文件信息..." << std::endl;
    ZipApiExamples::get_zip_info(test_zip);
    
    std::cout << "\n3. 列出zip文件内容..." << std::endl;
    ZipApiExamples::list_zip_contents(test_zip);
    
    std::cout << "\n4. 获取文件列表..." << std::endl;
    auto files = ZipApiExamples::get_file_list(test_zip);
    std::cout << "文件列表:" << std::endl;
    for (const auto& file : files) {
        std::cout << "  - " << file << std::endl;
    }
    
    // 如果有文本文件，尝试读取
    if (!files.empty()) {
        std::cout << "\n5. 尝试读取第一个文件..." << std::endl;
        std::string content = ZipApiExamples::read_text_file_from_zip(test_zip, files[0]);
        if (!content.empty()) {
            std::cout << "文件内容 (前100字符): " << content.substr(0, 100) << std::endl;
        } else {
            std::cout << "无法读取文件或文件为空" << std::endl;
        }
    }
    
    std::cout << "\n=== API 测试完成 ===" << std::endl;
    return 0;
} 
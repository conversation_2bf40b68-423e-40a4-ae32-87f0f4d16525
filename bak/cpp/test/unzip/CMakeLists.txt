# zexuan 单元测试
add_executable(unzip src/unzip.cpp)
target_link_libraries(unzip core libzip::zip)
target_include_directories(unzip PRIVATE
    include
    ${CMAKE_SOURCE_DIR}/core/include
)

# API测试程序
add_executable(api_test src/api_test.cpp)
target_link_libraries(api_test core libzip::zip)
target_include_directories(api_test PRIVATE
    include
    ${CMAKE_SOURCE_DIR}/core/include
)

# 设置输出目录
set_target_properties(unzip PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/bin/test"
)

set_target_properties(api_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/bin/test"
)
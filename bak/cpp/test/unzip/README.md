# libzip 使用示例

这个目录包含了使用 libzip 库的完整示例程序，展示了如何解压 zip 文件和使用 libzip 的各种 API。

## 文件说明

### 主要程序
- `src/unzip.cpp` - 完整的批量解压程序，可以解压 `/mnt/g/temp/` 目录下的所有 zip 文件
- `src/api_test.cpp` - API 测试程序，展示 libzip 的基本功能
- `include/zip_api_examples.hpp` - libzip API 使用示例和说明

### 功能特性

#### 批量解压程序 (unzip)
- 自动扫描 `/mnt/g/temp/` 目录下的所有 `.zip` 文件
- 为每个 zip 文件创建单独的解压目录
- 支持错误处理和进度显示
- 保持原始文件权限
- 支持嵌套目录结构

#### API 测试程序 (api_test)
- 检查 zip 文件是否有效
- 获取 zip 文件信息（文件数量、总大小等）
- 列出 zip 文件内容
- 读取 zip 中的文本文件
- 检查特定文件是否存在

## 编译和运行

### 编译
```bash
# 在项目根目录下
mkdir -p build && cd build
cmake ..
make
```

### 运行批量解压程序
```bash
# 解压所有 zip 文件
./bin/test/unzip
```

### 运行 API 测试程序
```bash
# 测试 libzip API
./bin/test/api_test
```

## libzip 主要 API 说明

### 1. 打开 zip 文件
```cpp
int error_code;
zip_t* zip = zip_open("file.zip", 0, &error_code);
if (!zip) {
    // 处理错误
}
```

### 2. 获取文件数量
```cpp
zip_int64_t num_entries = zip_get_num_entries(zip, 0);
```

### 3. 获取文件信息
```cpp
zip_stat_t stat;
if (zip_stat_index(zip, index, 0, &stat) == 0) {
    // stat.name - 文件名
    // stat.size - 文件大小
    // stat.mtime - 修改时间
}
```

### 4. 打开 zip 内的文件
```cpp
zip_file_t* zip_file = zip_fopen_index(zip, index, 0);
// 或者
zip_file_t* zip_file = zip_fopen(zip, "filename.txt", 0);
```

### 5. 读取文件内容
```cpp
char buffer[1024];
zip_int64_t bytes_read = zip_fread(zip_file, buffer, sizeof(buffer));
```

### 6. 关闭文件
```cpp
zip_fclose(zip_file);
zip_close(zip);
```

### 7. 错误处理
```cpp
const char* error_msg = zip_strerror(zip);
// 或者
char error_buffer[1024];
zip_error_to_str(error_buffer, sizeof(error_buffer), error_code, errno);
```

## 输出目录结构

解压后的文件将保存在 `/mnt/g/temp/extracted/` 目录下，每个 zip 文件会有自己的子目录：

```
/mnt/g/temp/extracted/
├── 1/          # 1.zip 的解压内容
├── 2/          # 2.zip 的解压内容
├── 3/          # 3.zip 的解压内容
└── ...
```

## 注意事项

1. 确保 `/mnt/g/temp/` 目录存在且有读取权限
2. 确保有足够的磁盘空间用于解压
3. 程序会自动创建必要的目录结构
4. 如果文件已存在，会被覆盖

## 错误处理

程序包含完整的错误处理机制：
- 检查 zip 文件是否有效
- 处理文件读写错误
- 处理目录创建失败
- 显示详细的错误信息

## 扩展功能

你可以基于这些示例程序扩展更多功能：
- 添加密码支持
- 实现压缩功能
- 添加进度条显示
- 支持更多压缩格式
- 添加文件过滤功能 
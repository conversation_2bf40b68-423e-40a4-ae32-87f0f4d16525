#include "zexuan/platform/platform.hpp"
#include "zexuan/platform/network/address.hpp"
#include <iostream>

int main() {
    std::cout << "=== 平台自动选择测试 ===" << std::endl;
    
    // 测试平台检测
#ifdef ZEXUAN_WINDOWS
    std::cout << "编译目标: Windows" << std::endl;
#elif ZEXUAN_LINUX
    std::cout << "编译目标: Linux" << std::endl;
#endif
    
    // 测试Address类
    try {
        zexuan::platform::network::Address addr("127.0.0.1", 8080);
        std::cout << "Address创建成功: " << addr.toIpPort() << std::endl;
        
        zexuan::platform::network::Address loopback(8080, true);
        std::cout << "回环地址: " << loopback.toIpPort() << std::endl;
        
        std::cout << "平台自动选择工作正常！" << std::endl;
    } catch (const std::exception& e) {
        std::cout << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}

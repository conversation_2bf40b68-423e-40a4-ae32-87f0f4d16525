#include "zexuan/platform/network/address.hpp"
#include <iostream>

int main() {
    std::cout << "=== Address类测试 ===" << std::endl;
    
    try {
        // 测试基本构造函数
        zexuan::platform::network::Address addr1;
        std::cout << "默认地址: " << addr1.toIpPort() << std::endl;
        
        // 测试IP和端口构造函数
        zexuan::platform::network::Address addr2("127.0.0.1", 8080);
        std::cout << "指定IP地址: " << addr2.toIpPort() << std::endl;
        
        // 测试端口和回环构造函数
        zexuan::platform::network::Address addr3(9090, true);
        std::cout << "回环地址: " << addr3.toIpPort() << std::endl;
        
        // 测试端口构造函数
        zexuan::platform::network::Address addr4(7070, false);
        std::cout << "任意地址: " << addr4.toIpPort() << std::endl;
        
        // 测试方法
        std::cout << "IP: " << addr2.toIp() << std::endl;
        std::cout << "端口: " << addr2.port() << std::endl;
        std::cout << "地址族: " << addr2.family() << std::endl;
        
        std::cout << "测试成功！" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "错误: " << e.what() << std::endl;
        return 1;
    }
}

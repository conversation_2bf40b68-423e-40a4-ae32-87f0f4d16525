#include "zexuan/logger.hpp"
#include <mutex>
#include "zexuan/net/EventLoop.h"
#include "zexuan/net/TcpServer.h"

#include <set>
#include <stdio.h>
#include <unistd.h>
#include <signal.h>

using namespace zexuan;
using namespace zexuan::net;



class ChatServer : zexuan::base::noncopyable
{
 public:
  ChatServer(EventLoop* loop,
             const InetAddress& listenAddr)
  : server_(loop, listenAddr, "ChatServer")
  {
    server_.setConnectionCallback(
        std::bind(&ChatServer::onConnection, this, _1));
    server_.setMessageCallback(
        std::bind(&ChatServer::onMessage, this, _1, _2, _3));
  }

  void setThreadNum(int numThreads)
  {
    server_.setThreadNum(numThreads);
  }

  void start()
  {
    server_.start();
  }

 private:
  void onConnection(const TcpConnectionPtr& conn)
  {
    auto logger = Logger::getFileLogger("net");
    logger->info("{} -> {} is {}", 
                 conn->peerAddress().toIpPort(),
                 conn->localAddress().toIpPort(),
                 conn->connected() ? "UP" : "DOWN");

    std::lock_guard<std::mutex> lock(mutex_);
    if (conn->connected())
    {
      connections_.insert(conn);
    }
    else
    {
      connections_.erase(conn);
    }
  }

  void onMessage(const TcpConnectionPtr& conn,
                 Buffer* buf,
                 Timestamp)
  {
    while (buf->readableBytes() > 0)
    {
      std::string message(buf->peek(), buf->readableBytes());
      buf->retrieveAll();
      
      std::lock_guard<std::mutex> lock(mutex_);
      for (auto& connection : connections_)
      {
        get_pointer(connection)->send(message);
      }
    }
  }

  typedef std::set<TcpConnectionPtr> ConnectionList;
  TcpServer server_;
  std::mutex mutex_;
  ConnectionList connections_;
};

int main(int argc, char* argv[])
{
  auto logger = Logger::getFileLogger("net");
  logger->info("pid = {}", getpid());
  
  if (argc > 1)
  {
    EventLoop loop;
    
    uint16_t port = static_cast<uint16_t>(atoi(argv[1]));
    InetAddress serverAddr(port);
    ChatServer server(&loop, serverAddr);
    if (argc > 2)
    {
      server.setThreadNum(atoi(argv[2]));
    }
    server.start();
    logger->info("ChatServer启动成功，按Ctrl+C退出");
    loop.loop();
  }
  else
  {
    logger->warn("Usage: {} port [thread_num]", argv[0]);
    printf("Usage: %s port [thread_num]\n", argv[0]);
  }
}




#include "zexuan/net/EventLoopThread.h"
#include "zexuan/net/TcpClient.h"
#include "zexuan/net/EventLoop.h"


#include <iostream>
#include <stdio.h>
#include <unistd.h>
#include <thread>
#include <chrono>
#include <signal.h>

using namespace zexuan;
using namespace zexuan::net;
using  std::string;



class ChatClient : zexuan::base::noncopyable
{
 public:
  ChatClient(EventLoop* loop, const InetAddress& serverAddr)
    : client_(loop, serverAddr, "ChatClient")
  {
    client_.setConnectionCallback(
        std::bind(&ChatClient::onConnection, this, _1));
    client_.setMessageCallback(
        std::bind(&ChatClient::onMessage, this, _1, _2, _3));
    client_.enableRetry();
  }

  void connect()
  {
    client_.connect();
  }

  void disconnect()
  {
    client_.disconnect();
  }

  void write(const StringPiece& message)
  {
    std::lock_guard<std::mutex> lock(mutex_);
    if (connection_)
    {
      get_pointer(connection_)->send(message.data(), message.size());
    }
  }

 private:
  void onConnection(const TcpConnectionPtr& conn)
  {
    auto logger = Logger::getFileLogger("net");
    logger->info("{} -> {} is {}", 
                 conn->localAddress().toIpPort(),
                 conn->peerAddress().toIpPort(),
                 conn->connected() ? "UP" : "DOWN");

    std::lock_guard<std::mutex> lock(mutex_);
    if (conn->connected())
    {
      connection_ = conn;
    }
    else
    {
      connection_.reset();
    }
  }


  void onMessage(const TcpConnectionPtr& conn,
                 Buffer* buf,
                 Timestamp)
  {
    while (buf->readableBytes() > 0)
    {
      std::string msg(buf->peek(), buf->readableBytes());
      printf("<<< %s\n", msg.c_str());
      buf->retrieveAll();
    }
  }

  TcpClient client_;
  // LengthHeaderCodec codec_;
  std::mutex mutex_;
  TcpConnectionPtr connection_;
};

int main(int argc, char* argv[])
{
  auto logger = Logger::getFileLogger("net");
  logger->info("pid = {}", getpid());
  
  if (argc > 2)
  {
    EventLoopThread loopThread;
    EventLoop* loop = loopThread.startLoop();
    
    uint16_t port = static_cast<uint16_t>(atoi(argv[2]));
    InetAddress serverAddr(argv[1], port);

    ChatClient client(loop, serverAddr);
    client.connect();
    logger->info("ChatClient连接成功，输入消息或按Ctrl+C退出");
    
    std::string line;
    while (std::getline(std::cin, line))
    {
      client.write(line);
    }
    client.disconnect();
    std::this_thread::sleep_for(std::chrono::seconds(1));  // wait for disconnect
  }
  else
  {
    printf("Usage: %s host_ip port\n", argv[0]);
  }
}

#include <iostream>
#include <fstream>
#include <tinyxml2.h>
#include "xml_models.hpp"

int main() {
    const char* path = "/root/zexuan/cpp/config/message.xml";
    tinyxml2::XMLDocument doc;
    auto rc = doc.LoadFile(path);
    if (rc != tinyxml2::XML_SUCCESS) {
        std::cerr << "Failed to load XML: " << path << ", rc=" << rc << std::endl;
        return 1;
    }
    const tinyxml2::XMLElement* root = doc.RootElement();
    if (!root) { std::cerr << "No root element" << std::endl; return 1; }

    XmlConfig cfg{};
    if (!parseXml(cfg, root)) { std::cerr << "parseXml(XmlConfig) failed" << std::endl; return 1; }

    // Verify and print
    std::cout << "config.key=" << cfg.key << "\n";
    std::cout << "items.size=" << cfg.items.size() << "\n";
    for (const auto& it : cfg.items) {
        std::cout << "item: key=" << it.key << ", value=" << it.value << ", desc=" << it.desc << "\n";
        for (const auto& s : it.subItems) {
            std::cout << "  subItem: key=" << s.key << ", value=" << s.value << ", desc=" << s.desc << "\n";
        }
    }
    return 0;
}


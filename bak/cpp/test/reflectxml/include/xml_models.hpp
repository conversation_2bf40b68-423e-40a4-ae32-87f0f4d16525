#pragma once

#include <string>
#include <vector>
#include "reflect_xml.hpp"

struct XmlSubItem {
    std::string key;
    std::string value;
    std::string desc;
};

struct XmlItem {
    std::string key;
    std::string value;
    std::string desc;
    std::vector<XmlSubItem> subItems;
};

struct XmlConfig {
    std::string key; // attribute on <config>
    std::vector<XmlItem> items;
};

// Generate parseXml specializations using reflection-like macros
BEGIN_REFLECT_XML(XmlSubItem, "subItem")
    XML_ATTR(key,   "key");
    XML_ATTR(value, "value");
    XML_ATTR(desc,  "desc");
END_REFLECT_XML()

BEGIN_REFLECT_XML(XmlItem, "item")
    XML_ATTR(key,   "key");
    XML_ATTR(value, "value");
    XML_ATTR(desc,  "desc");
    XML_CHILD_VECTOR(subItems, XmlSubItem, "subItem");
END_REFLECT_XML()

// XmlConfig handled with a thin custom wrapper that uses attributes + children
inline bool parseXml(XmlConfig& out, const tinyxml2::XMLElement* elem) {
    if (!elem) return false;
    // accept root named "config"
    const char* k = elem->Attribute("key");
    reflectxml::assign_from_cstr(out.key, k);
    out.items.clear();
    for (auto* it = elem->FirstChildElement("item"); it; it = it->NextSiblingElement("item")) {
        XmlItem item{}; if (!parseXml(item, it)) return false; out.items.push_back(std::move(item));
    }
    return true;
}


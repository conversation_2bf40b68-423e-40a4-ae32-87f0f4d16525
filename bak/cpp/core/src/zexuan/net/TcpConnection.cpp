  
#include "zexuan/net/TcpConnection.h"

#include "zexuan/net/Channel.h"
#include "zexuan/net/EventLoop.h"
#include "zexuan/net/Socket.h"
#include "zexuan/net/SocketsOps.h"
#include "zexuan/logger.hpp"

#include <errno.h>

using namespace zexuan;
using namespace zexuan::net;

void zexuan::net::defaultConnectionCallback(const TcpConnectionPtr& conn)
{
  auto logger = Logger::getFileLogger("net");
  logger->trace("{} -> {} is {}", 
                conn->localAddress().toIpPort(), 
                conn->peerAddress().toIpPort(), 
                conn->connected() ? "UP" : "DOWN");
  // do not call conn->forceClose(), because some users want to register message callback only.
}

void zexuan::net::defaultMessageCallback(const TcpConnectionPtr&,
                                        <PERSON><PERSON><PERSON>* buf,
                                        Timestamp)
{
  buf->retrieveAll();
}

TcpConnection::TcpConnection(EventLoop* loop,
                             const std::string& nameArg,
                             int sockfd,
                             const InetAddress& localAddr,
                             const InetAddress& peerAddr)
  : loop_(loop),
    name_(nameArg),
    state_(kConnecting),
    reading_(true),
    socket_(new Socket(sockfd)),
    channel_(new Channel(loop, sockfd)),
    localAddr_(localAddr),
    peerAddr_(peerAddr),
    highWaterMark_(64*1024*1024)
{
  assert(loop != nullptr);
  channel_->setReadCallback(
      [this](Timestamp receiveTime) { handleRead(receiveTime); });
  channel_->setWriteCallback(
      [this]() { handleWrite(); });
  channel_->setCloseCallback(
      [this]() { handleClose(); });
  channel_->setErrorCallback(
      [this]() { handleError(); });
  auto logger = Logger::getFileLogger("net");
  logger->debug("TcpConnection::ctor[{}] at {} fd={}", name_, static_cast<void*>(this), sockfd);
  socket_->setKeepAlive(true);
}

TcpConnection::~TcpConnection()
{
  auto logger = Logger::getFileLogger("net");
  logger->debug("TcpConnection::dtor[{}] at {} fd={} state={}", 
                name_, static_cast<void*>(this), channel_->fd(), stateToString());
  assert(state_ == kDisconnected);
}

bool TcpConnection::getTcpInfo(struct tcp_info* tcpi) const
{
  return socket_->getTcpInfo(tcpi);
}

std::string TcpConnection::getTcpInfoString() const
{
  char buf[1024];
  buf[0] = '\0';
  socket_->getTcpInfoString(buf, sizeof buf);
  return buf;
}

void TcpConnection::send(const void* data, int len)
{
  send(StringPiece(static_cast<const char*>(data), len));
}

void TcpConnection::send(const StringPiece& message)
{
  if (state_ == kConnected)
  {
    if (loop_->isInLoopThread())
    {
      sendInLoop(message);
    }
    else
    {
      std::string msg = message.as_string();
      loop_->runInLoop([this, msg = std::move(msg)]() {
        sendInLoop(msg);
      });
    }
  }
}

// FIXME efficiency!!!
void TcpConnection::send(Buffer* buf)
{
  if (state_ == kConnected)
  {
    if (loop_->isInLoopThread())
    {
      sendInLoop(buf->peek(), buf->readableBytes());
      buf->retrieveAll();
    }
    else
    {
      std::string msg = buf->retrieveAllAsString();
      loop_->runInLoop([this, msg = std::move(msg)]() {
        sendInLoop(msg);
      });
    }
  }
}

void TcpConnection::sendInLoop(const StringPiece& message)
{
  sendInLoop(message.data(), message.size());
}

void TcpConnection::sendInLoop(const void* data, size_t len)
{
  loop_->assertInLoopThread();
  ssize_t nwrote = 0;
  size_t remaining = len;
  bool faultError = false;
  if (state_ == kDisconnected)
  {
    auto logger = Logger::getFileLogger("net");
    logger->warn("disconnected, give up writing");
    return;
  }
  // if no thing in output queue, try writing directly
  if (!channel_->isWriting() && outputBuffer_.readableBytes() == 0)
  {
    nwrote = sockets::write(channel_->fd(), data, len);
    if (nwrote >= 0)
    {
      remaining = len - nwrote;
      if (remaining == 0 && writeCompleteCallback_)
      {
        auto conn = shared_from_this();
        loop_->queueInLoop([this, conn]() {
          writeCompleteCallback_(conn);
        });
      }
    }
    else // nwrote < 0
    {
      nwrote = 0;
      if (errno != EWOULDBLOCK)
      {
        auto logger = Logger::getFileLogger("net");
        logger->error("TcpConnection::sendInLoop: {}", strerror(errno));
        if (errno == EPIPE || errno == ECONNRESET) // FIXME: any others?
        {
          faultError = true;
        }
      }
    }
  }

  assert(remaining <= len);
  if (!faultError && remaining > 0)
  {
    size_t oldLen = outputBuffer_.readableBytes();
    if (oldLen + remaining >= highWaterMark_
        && oldLen < highWaterMark_
        && highWaterMarkCallback_)
    {
      auto conn = shared_from_this();
      size_t totalLen = oldLen + remaining;
      loop_->queueInLoop([this, conn, totalLen]() {
        highWaterMarkCallback_(conn, totalLen);
      });
    }
    outputBuffer_.append(static_cast<const char*>(data)+nwrote, remaining);
    if (!channel_->isWriting())
    {
      channel_->enableWriting();
    }
  }
}

void TcpConnection::shutdown()
{
  // FIXME: use compare and swap
  if (state_ == kConnected)
  {
    setState(kDisconnecting);
    // FIXME: shared_from_this()?
    loop_->runInLoop([this]() { shutdownInLoop(); });
  }
}

void TcpConnection::shutdownInLoop()
{
  loop_->assertInLoopThread();
  if (!channel_->isWriting())
  {
    // we are not writing
    socket_->shutdownWrite();
  }
}

// void TcpConnection::shutdownAndForceCloseAfter(double seconds)
// {
//   // FIXME: use compare and swap
//   if (state_ == kConnected)
//   {
//     setState(kDisconnecting);
//     loop_->runInLoop(std::bind(&TcpConnection::shutdownAndForceCloseInLoop, this, seconds));
//   }
// }

// void TcpConnection::shutdownAndForceCloseInLoop(double seconds)
// {
//   loop_->assertInLoopThread();
//   if (!channel_->isWriting())
//   {
//     // we are not writing
//     socket_->shutdownWrite();
//   }
//   loop_->runAfter(
//       seconds,
//       makeWeakCallback(shared_from_this(),
//                        &TcpConnection::forceCloseInLoop));
// }

void TcpConnection::forceClose()
{
  // FIXME: use compare and swap
  if (state_ == kConnected || state_ == kDisconnecting)
  {
    setState(kDisconnecting);
    auto conn = shared_from_this();
    loop_->queueInLoop([conn]() { conn->forceCloseInLoop(); });
  }
}

void TcpConnection::forceCloseWithDelay(double seconds)
{
  // 定时器功能已移除，直接调用forceClose
  if (state_ == kConnected || state_ == kDisconnecting)
  {
    setState(kDisconnecting);
    forceClose();
  }
}

void TcpConnection::forceCloseInLoop()
{
  loop_->assertInLoopThread();
  if (state_ == kConnected || state_ == kDisconnecting)
  {
    // as if we received 0 byte in handleRead();
    handleClose();
  }
}

const char* TcpConnection::stateToString() const
{
  switch (state_)
  {
    case kDisconnected:
      return "kDisconnected";
    case kConnecting:
      return "kConnecting";
    case kConnected:
      return "kConnected";
    case kDisconnecting:
      return "kDisconnecting";
    default:
      return "unknown state";
  }
}

void TcpConnection::setTcpNoDelay(bool on)
{
  socket_->setTcpNoDelay(on);
}

void TcpConnection::startRead()
{
  loop_->runInLoop([this]() { startReadInLoop(); });
}

void TcpConnection::startReadInLoop()
{
  loop_->assertInLoopThread();
  if (!reading_ || !channel_->isReading())
  {
    channel_->enableReading();
    reading_ = true;
  }
}

void TcpConnection::stopRead()
{
  loop_->runInLoop([this]() { stopReadInLoop(); });
}

void TcpConnection::stopReadInLoop()
{
  loop_->assertInLoopThread();
  if (reading_ || channel_->isReading())
  {
    channel_->disableReading();
    reading_ = false;
  }
}

void TcpConnection::connectEstablished()
{
  loop_->assertInLoopThread();
  assert(state_ == kConnecting);
  setState(kConnected);
  channel_->tie(shared_from_this());
  channel_->enableReading();

  connectionCallback_(shared_from_this());
}

void TcpConnection::connectDestroyed()
{
  loop_->assertInLoopThread();
  if (state_ == kConnected)
  {
    setState(kDisconnected);
    channel_->disableAll();

    connectionCallback_(shared_from_this());
  }
  channel_->remove();
}

void TcpConnection::handleRead(Timestamp receiveTime)
{
  loop_->assertInLoopThread();
  int savedErrno = 0;
  ssize_t n = inputBuffer_.readFd(channel_->fd(), &savedErrno);
  if (n > 0)
  {
    messageCallback_(shared_from_this(), &inputBuffer_, receiveTime);
  }
  else if (n == 0)
  {
    handleClose();
  }
  else
  {
    errno = savedErrno;
    auto logger = Logger::getFileLogger("net");
    logger->error("TcpConnection::handleRead: {}", strerror(savedErrno));
    handleError();
  }
}

void TcpConnection::handleWrite()
{
  loop_->assertInLoopThread();
  if (channel_->isWriting())
  {
    ssize_t n = sockets::write(channel_->fd(),
                               outputBuffer_.peek(),
                               outputBuffer_.readableBytes());
    if (n > 0)
    {
      outputBuffer_.retrieve(n);
      if (outputBuffer_.readableBytes() == 0)
      {
        channel_->disableWriting();
        if (writeCompleteCallback_)
        {
          auto conn = shared_from_this();
          loop_->queueInLoop([this, conn]() {
            writeCompleteCallback_(conn);
          });
        }
        if (state_ == kDisconnecting)
        {
          shutdownInLoop();
        }
      }
    }
    else
    {
      auto logger = Logger::getFileLogger("net");
      logger->error("TcpConnection::handleWrite: {}", strerror(errno));
      // if (state_ == kDisconnecting)
      // {
      //   shutdownInLoop();
      // }
    }
  }
  else
  {
    auto logger = Logger::getFileLogger("net");
    logger->trace("Connection fd = {} is down, no more writing", channel_->fd());
  }
}

void TcpConnection::handleClose()
{
  loop_->assertInLoopThread();
  auto logger = Logger::getFileLogger("net");
  logger->trace("fd = {} state = {}", channel_->fd(), stateToString());
  assert(state_ == kConnected || state_ == kDisconnecting);
  // we don't close fd, leave it to dtor, so we can find leaks easily.
  setState(kDisconnected);
  channel_->disableAll();

  TcpConnectionPtr guardThis(shared_from_this());
  connectionCallback_(guardThis);
  // must be the last line
  closeCallback_(guardThis);
}

void TcpConnection::handleError()
{
  int err = sockets::getSocketError(channel_->fd());
  auto logger = Logger::getFileLogger("net");
  logger->error("TcpConnection::handleError [{}] - SO_ERROR = {}: {}", name_, err, strerror(err));
}


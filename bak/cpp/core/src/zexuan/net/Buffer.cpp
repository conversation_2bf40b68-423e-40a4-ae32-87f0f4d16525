#include "zexuan/net/Buffer.h"
#include "zexuan/logger.hpp"

#include "zexuan/net/SocketsOps.h"

#include <errno.h>
#include <sys/uio.h>

using namespace zexuan;
using namespace zexuan::net;

const char Buffer::kCRLF[] = "\r\n";

const size_t Buffer::kCheapPrepend;
const size_t Buffer::kInitialSize;

ssize_t Buffer::readFd(int fd, int* savedErrno)
{
  // saved an ioctl()/FIONREAD call to tell how much to read
  constexpr size_t kExtraBufSize = 65536;
  char extrabuf[kExtraBufSize];
  struct iovec vec[2];
  const size_t writable = writableBytes();
  vec[0].iov_base = begin() + writerIndex_;
  vec[0].iov_len = writable;
  vec[1].iov_base = extrabuf;
  vec[1].iov_len = kExtraBufSize;
  // when there is enough space in this buffer, don't read into extrabuf.
  // when extrabuf is used, we read 128k-1 bytes at most.
  const int iovcnt = (writable < kExtraBufSize) ? 2 : 1;
  const ssize_t n = sockets::readv(fd, vec, iovcnt);
  if (n < 0) [[unlikely]]
  {
    *savedErrno = errno;
  }
  else if (static_cast<size_t>(n) <= writable) [[likely]]
  {
    writerIndex_ += static_cast<size_t>(n);
  }
  else
  {
    writerIndex_ = buffer_.size();
    append(extrabuf, static_cast<size_t>(n - writable));
  }
  return n;
}


  
#include "zexuan/net/TcpServer.h"
#include "zexuan/logger.hpp"


#include "zexuan/net/Acceptor.h"
#include "zexuan/net/EventLoop.h"
#include "zexuan/net/EventLoopThreadPool.h"
#include "zexuan/net/SocketsOps.h"

#include <stdio.h>  // snprintf

using namespace zexuan;
using namespace zexuan::net;

TcpServer::TcpServer(EventLoop* loop,
                     const InetAddress& listenAddr,
                     const std::string& nameArg,
                     Option option)
  : loop_(loop),
    ipPort_(listenAddr.toIpPort()),
    name_(nameArg),
    acceptor_(new Acceptor(loop, listenAddr, option == kReusePort)),
    threadPool_(new EventLoopThreadPool(loop, name_)),
    connectionCallback_(defaultConnectionCallback),
    messageCallback_(defaultMessageCallback),
    started_(0),
    nextConnId_(1)
{
  assert(loop != nullptr);
  acceptor_->setNewConnectionCallback(
      [this](int sockfd, const InetAddress& peerAddr) {
        newConnection(sockfd, peerAddr);
      });
}

TcpServer::~TcpServer()
{
  loop_->assertInLoopThread();
  auto logger = Logger::getFileLogger("net");
  logger->trace("TcpServer::~TcpServer [{}] destructing", name_);

  for (auto& item : connections_)
  {
    TcpConnectionPtr conn(item.second);
    item.second.reset();
    conn->getLoop()->runInLoop([conn]() {
      conn->connectDestroyed();
    });
  }
}

void TcpServer::setThreadNum(int numThreads)
{
  assert(0 <= numThreads);
  threadPool_->setThreadNum(numThreads);
}

void TcpServer::start()
{
  if (started_.exchange(1) == 0)
  {
    threadPool_->start(threadInitCallback_);

    assert(!acceptor_->listening());
    loop_->runInLoop([this]() {
      acceptor_->listen();
    });
  }
}

void TcpServer::newConnection(int sockfd, const InetAddress& peerAddr)
{
  loop_->assertInLoopThread();
  EventLoop* ioLoop = threadPool_->getNextLoop();
  char buf[64];
  snprintf(buf, sizeof buf, "-%s#%d", ipPort_.c_str(), nextConnId_);
  ++nextConnId_;
  std::string connName = name_ + buf;

  auto logger = Logger::getFileLogger("net");
  logger->info("TcpServer::newConnection [{}] - new connection [{}] from {}", 
               name_, connName, peerAddr.toIpPort());
  InetAddress localAddr(sockets::getLocalAddr(sockfd));
  // FIXME poll with zero timeout to double confirm the new connection
  // FIXME use make_shared if necessary
  TcpConnectionPtr conn(new TcpConnection(ioLoop,
                                          connName,
                                          sockfd,
                                          localAddr,
                                          peerAddr));
  connections_[connName] = conn;
  conn->setConnectionCallback(connectionCallback_);
  conn->setMessageCallback(messageCallback_);
  conn->setWriteCompleteCallback(writeCompleteCallback_);
  conn->setCloseCallback([this](const TcpConnectionPtr& conn) {
    removeConnection(conn);
  }); // FIXME: unsafe
  ioLoop->runInLoop([conn]() { conn->connectEstablished(); });
}

void TcpServer::removeConnection(const TcpConnectionPtr& conn)
{
  // FIXME: unsafe
  loop_->runInLoop([this, conn]() { removeConnectionInLoop(conn); });
}

void TcpServer::removeConnectionInLoop(const TcpConnectionPtr& conn)
{
  loop_->assertInLoopThread();
  auto logger = Logger::getFileLogger("net");
  logger->info("TcpServer::removeConnectionInLoop [{}] - connection {}", name_, conn->name());
  size_t n = connections_.erase(conn->name());
  (void)n;
  assert(n == 1);
  EventLoop* ioLoop = conn->getLoop();
  ioLoop->queueInLoop([conn]() { conn->connectDestroyed(); });
}


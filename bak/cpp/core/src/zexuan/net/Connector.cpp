#include "zexuan/net/Connector.h"
#include "zexuan/logger.hpp"


#include "zexuan/net/Channel.h"
#include "zexuan/net/EventLoop.h"
#include "zexuan/net/SocketsOps.h"
#include "cassert"
#include <errno.h>

using namespace zexuan;
using namespace zexuan::net;

const int Connector::kMaxRetryDelayMs;

Connector::Connector(EventLoop* loop, const InetAddress& serverAddr)
  : loop_(loop),
    serverAddr_(serverAddr),
    connect_(false),
    state_(kDisconnected),
    retryDelayMs_(kInitRetryDelayMs)
{
  auto logger = Logger::getFileLogger("net");
  logger->debug("Connector::ctor[{}]", static_cast<void*>(this));
}

Connector::~Connector()
{
  auto logger = Logger::getFileLogger("net");
  logger->debug("Connector::dtor[{}]", static_cast<void*>(this));
  assert(!channel_);
}

void Connector::start()
{
  connect_ = true;
  loop_->runInLoop([this]{startInLoop();}); // FIXME: unsafe
}

void Connector::startInLoop()
{
  loop_->assertInLoopThread();
  assert(state_ == kDisconnected);
  if (connect_)
  {
    connect();
  }
  else
  {
    auto logger = Logger::getFileLogger("net");
    logger->debug("do not connect");
  }
}

void Connector::stop()
{
  connect_ = false;
  loop_->queueInLoop([this]() { stopInLoop(); }); // FIXME: unsafe
}

void Connector::stopInLoop()
{
  loop_->assertInLoopThread();
  if (state_ == kConnecting)
  {
    setState(kDisconnected);
    int sockfd = removeAndResetChannel();
    retry(sockfd);
  }
}

void Connector::connect()
{
  int sockfd = sockets::createNonblockingOrDie(serverAddr_.family());
  int ret = sockets::connect(sockfd, serverAddr_.getSockAddr());
  int savedErrno = (ret == 0) ? 0 : errno;
  switch (savedErrno)
  {
    case 0:
    case EINPROGRESS:
    case EINTR:
    case EISCONN:
      connecting(sockfd);
      break;

    case EAGAIN:
    case EADDRINUSE:
    case EADDRNOTAVAIL:
    case ECONNREFUSED:
    case ENETUNREACH:
      retry(sockfd);
      break;

    case EACCES:
    case EPERM:
    case EAFNOSUPPORT:
    case EALREADY:
    case EBADF:
    case EFAULT:
    case ENOTSOCK:
      {
        auto logger = Logger::getFileLogger("net");
        logger->error("connect error in Connector::startInLoop {}: {}", savedErrno, strerror(savedErrno));
        sockets::close(sockfd);
      }
      break;

    default:
      {
        auto logger2 = Logger::getFileLogger("net");
        logger2->error("Unexpected error in Connector::startInLoop {}: {}", savedErrno, strerror(savedErrno));
        sockets::close(sockfd);
        // connectErrorCallback_();
      }
      break;
  }
}

void Connector::restart()
{
  loop_->assertInLoopThread();
  setState(kDisconnected);
  retryDelayMs_ = kInitRetryDelayMs;
  connect_ = true;
  startInLoop();
}

void Connector::connecting(int sockfd)
{
  setState(kConnecting);
  assert(!channel_);
  channel_.reset(new Channel(loop_, sockfd));
  channel_->setWriteCallback(
      [this]() { handleWrite(); }); // FIXME: unsafe
  channel_->setErrorCallback(
      [this]() { handleError(); }); // FIXME: unsafe

  // channel_->tie(shared_from_this()); is not working,
  // as channel_ is not managed by shared_ptr
  channel_->enableWriting();
}

int Connector::removeAndResetChannel()
{
  channel_->disableAll();
  channel_->remove();
  int sockfd = channel_->fd();
  // Can't reset channel_ here, because we are inside Channel::handleEvent
  loop_->queueInLoop([this]() { resetChannel(); }); // FIXME: unsafe
  return sockfd;
}

void Connector::resetChannel()
{
  channel_.reset();
}

void Connector::handleWrite()
{
  auto logger = Logger::getFileLogger("net");
  const char* state_str = (state_ == kDisconnected) ? "Disconnected" : 
                         (state_ == kConnecting) ? "Connecting" : 
                         (state_ == kConnected) ? "Connected" : "Unknown";
  logger->trace("Connector::handleWrite {}", state_str);

  if (state_ == kConnecting)
  {
    int sockfd = removeAndResetChannel();
    int err = sockets::getSocketError(sockfd);
    if (err)
    {
      logger->warn("Connector::handleWrite - SO_ERROR = {}: {}", err, strerror(err));
      retry(sockfd);
    }
    else if (sockets::isSelfConnect(sockfd))
    {
      logger->warn("Connector::handleWrite - Self connect");
      retry(sockfd);
    }
    else
    {
      setState(kConnected);
      if (connect_)
      {
        newConnectionCallback_(sockfd);
      }
      else
      {
        sockets::close(sockfd);
      }
    }
  }
  else
  {
    // what happened?
    assert(state_ == kDisconnected);
  }
}

void Connector::handleError()
{
  auto logger = Logger::getFileLogger("net");
  const char* state_str = (state_ == kDisconnected) ? "Disconnected" : 
                         (state_ == kConnecting) ? "Connecting" : 
                         (state_ == kConnected) ? "Connected" : "Unknown";
  logger->error("Connector::handleError state={}", state_str);
  if (state_ == kConnecting)
  {
    int sockfd = removeAndResetChannel();
    int err = sockets::getSocketError(sockfd);
    logger->trace("SO_ERROR = {}: {}", err, strerror(err));
    retry(sockfd);
  }
}

void Connector::retry(int sockfd)
{
  sockets::close(sockfd);
  setState(kDisconnected);
  if (connect_)
  {
    auto logger = Logger::getFileLogger("net");
    logger->info("Connector::retry - Retry connecting to {} immediately (timer removed)", 
                 serverAddr_.toIpPort());
    // 定时器功能已移除，立即重试
    loop_->runInLoop([self = shared_from_this()]() { self->startInLoop(); });
    retryDelayMs_ = std::min(retryDelayMs_ * 2, kMaxRetryDelayMs);
  }
  else
  {
    auto logger2 = Logger::getFileLogger("net");
    logger2->debug("do not connect");
  }
}


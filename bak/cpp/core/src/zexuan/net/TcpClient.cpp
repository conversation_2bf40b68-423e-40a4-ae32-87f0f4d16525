  //

#include "zexuan/net/TcpClient.h"
#include "zexuan/logger.hpp"


#include "zexuan/net/Connector.h"
#include "zexuan/net/EventLoop.h"
#include "zexuan/net/SocketsOps.h"

#include <stdio.h>  // snprintf

using namespace zexuan;
using namespace zexuan::net;

// TcpClient::TcpClient(EventLoop* loop)
//   : loop_(loop)
// {
// }

// TcpClient::TcpClient(EventLoop* loop, const std::string& host, uint16_t port)
//   : loop_(CHECK_NOTNULL(loop)),
//     serverAddr_(host, port)
// {
// }

namespace zexuan
{
namespace net
{
namespace detail
{

void removeConnection(EventLoop* loop, const TcpConnectionPtr& conn)
{
  loop->queueInLoop([conn]() { conn->connectDestroyed(); });
}

void removeConnector(const ConnectorPtr& connector)
{
  //connector->
}

}  // namespace detail
}  // namespace net
}  // namespace zexuan

TcpClient::TcpClient(EventLoop* loop,
                     const InetAddress& serverAddr,
                     const std::string& nameArg)
  : loop_(loop),
    connector_(new Connector(loop, serverAddr)),
    name_(nameArg),
    connectionCallback_(defaultConnectionCallback),
    messageCallback_(defaultMessageCallback),
    retry_(false),
    connect_(true),
    nextConnId_(1)
{
  assert(loop != nullptr);
  connector_->setNewConnectionCallback(
      [this](int sockfd) { newConnection(sockfd); });
  // FIXME setConnectFailedCallback
  auto logger = Logger::getFileLogger("net");
  logger->info("TcpClient::TcpClient[{}] - connector {}", name_, static_cast<void*>(connector_.get()));
}

TcpClient::~TcpClient()
{
  auto logger = Logger::getFileLogger("net");
  logger->info("TcpClient::~TcpClient[{}] - connector {}", name_, static_cast<void*>(connector_.get()));
  TcpConnectionPtr conn;
  bool unique = false;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    unique = connection_.unique();
    conn = connection_;
  }
  if (conn)
  {
    assert(loop_ == conn->getLoop());
    // FIXME: not 100% safe, if we are in different thread
  CloseCallback cb = [loop = loop_](const TcpConnectionPtr& c) { detail::removeConnection(loop, c); };
  loop_->runInLoop([conn, cb]() { conn->setCloseCallback(cb); });
    if (unique)
    {
      conn->forceClose();
    }
  }
  else
  {
    connector_->stop();
    // 定时器功能已移除，立即移除connector
    detail::removeConnector(connector_);
  }
}

void TcpClient::connect()
{
  // FIXME: check state
  auto logger = Logger::getFileLogger("net");
  logger->info("TcpClient::connect[{}] - connecting to {}", name_, connector_->serverAddress().toIpPort());
  connect_ = true;
  connector_->start();
}

void TcpClient::disconnect()
{
  connect_ = false;

  {
    std::lock_guard<std::mutex> lock(mutex_);
    if (connection_)
    {
      connection_->shutdown();
    }
  }
}

void TcpClient::stop()
{
  connect_ = false;
  connector_->stop();
}

void TcpClient::newConnection(int sockfd)
{
  loop_->assertInLoopThread();
  InetAddress peerAddr(sockets::getPeerAddr(sockfd));
  char buf[32];
  snprintf(buf, sizeof buf, ":%s#%d", peerAddr.toIpPort().c_str(), nextConnId_);
  ++nextConnId_;
  std::string connName = name_ + buf;

  InetAddress localAddr(sockets::getLocalAddr(sockfd));
  // FIXME poll with zero timeout to double confirm the new connection
  // FIXME use make_shared if necessary
  TcpConnectionPtr conn(new TcpConnection(loop_,
                                          connName,
                                          sockfd,
                                          localAddr,
                                          peerAddr));

  conn->setConnectionCallback(connectionCallback_);
  conn->setMessageCallback(messageCallback_);
  conn->setWriteCompleteCallback(writeCompleteCallback_);
  conn->setCloseCallback(
      [this](const TcpConnectionPtr& c) { removeConnection(c); }); // FIXME: unsafe
  {
    std::lock_guard<std::mutex> lock(mutex_);
    connection_ = conn;
  }
  conn->connectEstablished();
}

void TcpClient::removeConnection(const TcpConnectionPtr& conn)
{
  loop_->assertInLoopThread();
  assert(loop_ == conn->getLoop());

  {
    std::lock_guard<std::mutex> lock(mutex_);
    assert(connection_ == conn);
    connection_.reset();
  }

  loop_->queueInLoop([conn]() { conn->connectDestroyed(); });
  if (retry_ && connect_)
  {
    auto logger = Logger::getFileLogger("net");
    logger->info("TcpClient::connect[{}] - Reconnecting to {}", name_, connector_->serverAddress().toIpPort());
    connector_->restart();
  }
}


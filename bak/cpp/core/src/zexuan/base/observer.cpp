/**
 * @file observer.cpp
 * @brief Observer pattern implementation for zexuan project
 * <AUTHOR> from NXObserver.cpp
 * @date 2024
 */

#include "zexuan/base/observer.hpp"
#include <algorithm>
#include <stdexcept>

namespace zexuan {
namespace base {

// BaseObserver implementation

BaseObserver::BaseObserver(int id, const std::string& name)
    : id_(id), name_(name) {
    if (id < 0) {
        throw std::invalid_argument("Observer ID must be non-negative");
    }
}

void BaseObserver::setMessageCallback(std::function<void(const Message&)> callback) {
    messageCallback_ = std::move(callback);
}

void BaseObserver::onNotify(Subject* subject, const Message& message) {
    if (messageCallback_) {
        messageCallback_(message);
    }
}



} // namespace base
} // namespace zexuan

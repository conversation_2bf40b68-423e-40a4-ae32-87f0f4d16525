/**
 * @file mediator.cpp
 * @brief Simplified Mediator pattern implementation for zexuan project
 * <AUTHOR> from NXLoadSrvMedLib.cpp
 * @date 2024
 */

#include "zexuan/base/mediator.hpp"
#include "zexuan/base/observer.hpp"

namespace zexuan {
namespace base {

// BaseMediator implementation

BaseMediator::BaseMediator() {
    // 构造函数已经初始化了所有成员，无需额外初始化
}

bool BaseMediator::registerObserver(int observerId, Observer* observer, std::string& errorMsg) {
    if (!observer) {
        errorMsg = "Observer cannot be null";
        return false;
    }

    if (observerId < 0) {
        errorMsg = "Observer ID must be non-negative";
        return false;
    }

    std::lock_guard<std::mutex> lock(observersMutex_);

    // Check if observer is already registered
    if (observers_.find(observerId) != observers_.end()) {
        errorMsg = "Observer with ID " + std::to_string(observerId) + " already registered";
        return false;
    }

    // Register the observer
    observers_[observerId] = observer;

    return true;
}

bool BaseMediator::registerObserver(int observerId, const std::string& observerName, Observer* observer, std::string& errorMsg) {
    if (!observer) {
        errorMsg = "Observer cannot be null";
        return false;
    }

    if (observerId < 0) {
        errorMsg = "Observer ID must be non-negative";
        return false;
    }

    if (observerName.empty()) {
        errorMsg = "Observer name cannot be empty";
        return false;
    }

    std::lock_guard<std::mutex> lock(observersMutex_);

    // Check if observer ID is already registered
    if (observers_.find(observerId) != observers_.end()) {
        errorMsg = "Observer with ID " + std::to_string(observerId) + " already registered";
        return false;
    }

    // Check if observer name is already registered
    if (nameToId_.find(observerName) != nameToId_.end()) {
        errorMsg = "Observer with name '" + observerName + "' already registered";
        return false;
    }

    // Register the observer with both ID and name
    observers_[observerId] = observer;
    nameToId_[observerName] = observerId;

    return true;
}

bool BaseMediator::unregisterObserver(int observerId, std::string& errorMsg) {
    std::lock_guard<std::mutex> lock(observersMutex_);

    auto it = observers_.find(observerId);
    if (it == observers_.end()) {
        errorMsg = "Observer with ID " + std::to_string(observerId) + " not found";
        return false;
    }

    // Remove from both maps
    observers_.erase(it);
    
    // Find and remove from name mapping
    for (auto nameIt = nameToId_.begin(); nameIt != nameToId_.end(); ++nameIt) {
        if (nameIt->second == observerId) {
            nameToId_.erase(nameIt);
            break;
        }
    }

    return true;
}

int BaseMediator::sendMessage(const Message& message, std::string& description) {
    // Get target address from message
    uint8_t targetId = message.getTarget();

    // 复制需要通知的Observer列表，避免在通知时持锁
    std::vector<Observer*> observersToNotify;
    
    {
        std::lock_guard<std::mutex> lock(observersMutex_);
        
        // If target address is 0, broadcast to all observers
        if (targetId == 255) {
            if (observers_.empty()) {
                description = "No observers registered for broadcast";
                return -1;
            }

            // 复制所有Observer指针
            for (const auto& pair : observers_) {
                if (pair.second) {
                    observersToNotify.push_back(pair.second);
                }
            }
        } else {
            // Original logic for specific target
            auto it = observers_.find(static_cast<int>(targetId));
            if (it == observers_.end()) {
                description = "Observer with ID " + std::to_string(targetId) + " not found";
                return -1;
            }

            Observer* observer = it->second;
            if (!observer) {
                description = "Observer pointer is null";
                return -1;
            }
            
            observersToNotify.push_back(observer);
        }
    } // 锁在这里释放
    
    // 在锁外进行通知，避免死锁
    if (targetId == 255) {
        // 广播模式
        int successCount = 0;
        int failureCount = 0;
        std::string errorDetails;

        for (Observer* observer : observersToNotify) {
            try {
                observer->onNotify(nullptr, message);
                successCount++;
            } catch (const std::exception& e) {
                failureCount++;
                errorDetails += "Observer exception: " + std::string(e.what()) + "; ";
            }
        }

        if (failureCount == 0) {
            description = "Message broadcast to all " + std::to_string(successCount) + " observers successfully";
            return 0;
        } else if (successCount > 0) {
            description = "Message broadcast partially successful: " + std::to_string(successCount) + " succeeded, " +
                         std::to_string(failureCount) + " failed. Errors: " + errorDetails;
            return 1; // Partial success
        } else {
            description = "Message broadcast failed to all observers. Errors: " + errorDetails;
            return -1;
        }
    } else {
        // 单播模式
        try {
            observersToNotify[0]->onNotify(nullptr, message);
            description = "Message sent to observer " + std::to_string(targetId) + " successfully";
            return 0;
        } catch (const std::exception& e) {
            description = "Exception in observer notification: " + std::string(e.what());
            return -1;
        }
    }
}

int BaseMediator::sendMessageToTarget(const Message& message, int targetId, std::string& description) {
    // 仅用于“路由到指定观察者”，不修改消息内容（保持source/target原样）
    // 查找目标观察者
    Observer* targetObserver = nullptr;
    {
        std::lock_guard<std::mutex> lock(observersMutex_);
        auto it = observers_.find(targetId);
        if (it == observers_.end()) {
            description = "Observer with ID " + std::to_string(targetId) + " not found";
            return -1;
        }
        targetObserver = it->second;
        if (!targetObserver) {
            description = "Observer pointer is null";
            return -1;
        }
    }

    // 发送消息（不改写message.target/message.source）
    try {
        targetObserver->onNotify(nullptr, message);
        description = "Message sent to observer " + std::to_string(targetId) + " successfully";
        return 0;
    } catch (const std::exception& e) {
        description = "Failed to send message to observer " + std::to_string(targetId) + ": " + e.what();
        return -1;
    }
}

size_t BaseMediator::getObserverCount() const {
    std::lock_guard<std::mutex> lock(observersMutex_);
    return observers_.size();
}

bool BaseMediator::isObserverRegistered(int observerId) const {
    std::lock_guard<std::mutex> lock(observersMutex_);
    return observers_.find(observerId) != observers_.end();
}

int BaseMediator::getObserverIdByName(const std::string& observerName) const {
    std::lock_guard<std::mutex> lock(observersMutex_);
    auto it = nameToId_.find(observerName);
    if (it != nameToId_.end()) {
        return it->second;
    }
    return -1; // Not found
}

int BaseMediator::sendMessageToTargetByName(const Message& message, const std::string& targetName, std::string& description) {
    int targetId = getObserverIdByName(targetName);
    if (targetId < 0) {
        description = "Observer with name '" + targetName + "' not found";
        return -1;
    }
    // 不修改message，直接按ID路由
    return sendMessageToTarget(message, targetId, description);
}

} // namespace base
} // namespace zexuan

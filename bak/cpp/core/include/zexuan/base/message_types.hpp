/**
 * @file message_types.hpp
 * @brief 简洁统一的消息类型定义 - 唯一数据源
 * <AUTHOR> project
 * @date 2024
 */

#ifndef ZEXUAN_BASE_MESSAGE_TYPES_HPP
#define ZEXUAN_BASE_MESSAGE_TYPES_HPP

#include <cstdint>
#include <string>
#include <vector>

namespace zexuan {
namespace base {

/**
 * @brief 传送原因 (COT - Cause of Transmission)
 */
enum class CauseOfTransmission : uint8_t {
    INFO_REQUEST = 0x01,        ///< 信息请求
    INFO_RESPONSE = 0x02,       ///< 信息响应
    SPONTANEOUS = 0x03,         ///< 自发传输
    ACTIVATION = 0x06,          ///< 激活/执行操作
    CONFIRMATION = 0x07,        ///< 激活确认
    DEACTIVATION = 0x08,        ///< 停用操作
    TERMINATION = 0x0A,         ///< 激活终止
};

/**
 * @brief 类型标识 (TYP - Type Identification)
 */
namespace TypeIdentification {
    constexpr uint8_t STANDARD = 0x01;
}

/**
 * @brief 可变结构限定词 (VSQ - Variable Structure Qualifier)
 */
namespace VariableStructureQualifier {
    constexpr uint8_t SINGLE_INFO = 0x81;
}

/**
 * @brief 功能类型 (FUN - Function Type)
 */
namespace FunctionType {
    // 文件名插件
    constexpr uint8_t FILENAME_RENAME = 0x01;
    
    // 解压插件
    constexpr uint8_t UNZIP_EXTRACT = 0x01;
    
    // 加密插件
    constexpr uint8_t CRYPTO_AES = 0x01;
    constexpr uint8_t CRYPTO_RSA = 0x02;
    constexpr uint8_t CRYPTO_BASE64 = 0x03;
    constexpr uint8_t CRYPTO_SHA256 = 0x04;
    
    // 传输插件
    constexpr uint8_t TRANSFER_SCP = 0x01;
    constexpr uint8_t TRANSFER_SFTP = 0x02;
    
    // 序列化插件
    constexpr uint8_t SERIALIZE_XML = 0x01;
    constexpr uint8_t SERIALIZE_JSON = 0x02;
}

/**
 * @brief 信息序号 (INF - Information Number)
 */
namespace InformationNumber {
    constexpr uint8_t STATUS_REQUEST = 0x00;
    constexpr uint8_t OPERATION_EXECUTE = 0x01;
    constexpr uint8_t OPERATION_REVERSE = 0x02;
}

// 消息路由说明：
// 1. 客户端发送消息时，source=客户端ID，target=插件ID
// 2. 插件回复时，调用message.swapSourceTarget()交换source和target
// 3. 服务器处理INFO_RESPONSE时，直接使用message.getTarget()获取目标客户端ID
// 4. 不再需要在inf字段中传递客户端ID，使用标准的source/target机制

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_MESSAGE_TYPES_HPP
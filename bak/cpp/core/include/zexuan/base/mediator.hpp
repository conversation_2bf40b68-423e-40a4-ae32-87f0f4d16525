/**
 * @file mediator.hpp
 * @brief Simplified Mediator pattern implementation for zexuan project
 * <AUTHOR> from NXLoadSrvMedLib.h
 * @date 2024
 *
 * Simplified mediator pattern implementation using unified Message class
 * based on IEC 60870-5-103 protocol.
 */

#ifndef ZEXUAN_BASE_MEDIATOR_HPP
#define ZEXUAN_BASE_MEDIATOR_HPP

#include "message.hpp"
#include <memory>
#include <unordered_map>
#include <string>
#include <mutex>

namespace zexuan {
namespace base {

// Forward declarations
class Observer;
class Subject;

/**
 * @brief Mediator interface for coordinating communication
 *
 * Simplified mediator interface that only handles Message communication.
 */
class Mediator {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~Mediator() = default;

    /**
     * @brief Register an observer with the mediator
     * @param observerId Observer unique identifier
     * @param observer Observer instance
     * @param errorMsg Error message output on failure
     * @return true on success, false on failure
     */
    virtual bool registerObserver(int observerId, Observer* observer, std::string& errorMsg) = 0;

    /**
     * @brief Register an observer with both ID and name
     * @param observerId Observer unique identifier
     * @param observerName Observer name for lookup
     * @param observer Observer instance
     * @param errorMsg Error message output on failure
     * @return true on success, false on failure
     */
    virtual bool registerObserver(int observerId, const std::string& observerName, Observer* observer, std::string& errorMsg) = 0;

    /**
     * @brief Unregister an observer from the mediator
     * @param observerId Observer unique identifier
     * @param errorMsg Error message output on failure
     * @return true on success, false on failure
     */
    virtual bool unregisterObserver(int observerId, std::string& errorMsg) = 0;

    /**
     * @brief Send a message using internal routing
     * @param message Message to send (contains source and target addresses)
     * @param description Result description
     * @return 0 on success, negative on failure
     */
    virtual int sendMessage(const Message& message, std::string& description) = 0;

    /**
     * @brief Route a message to a specific target observer (does NOT modify the message)
     * @param message Message to send (source/target fields are preserved as-is)
     * @param targetId Target observer ID to deliver to
     * @param description Result description
     * @return 0 on success, negative on failure
     */
    virtual int sendMessageToTarget(const Message& message, int targetId, std::string& description) = 0;

    /**
     * @brief Route a message to a specific target by name (does NOT modify the message)
     * @param message Message to send (source/target preserved)
     * @param targetName Target observer name
     * @param description Result description
     * @return 0 on success, negative on failure
     */
    virtual int sendMessageToTargetByName(const Message& message, const std::string& targetName, std::string& description) = 0;

    /**
     * @brief Get observer ID by name
     * @param observerName Observer name
     * @return Observer ID, or -1 if not found
     */
    virtual int getObserverIdByName(const std::string& observerName) const = 0;
};

/**
 * @brief Base implementation of Mediator interface
 *
 * Simplified base mediator implementation with basic observer management.
 */
class BaseMediator : public Mediator {
public:
    /**
     * @brief Constructor
     */
    BaseMediator();

    /**
     * @brief Virtual destructor
     */
    virtual ~BaseMediator() = default;

    // Mediator interface implementation

    bool registerObserver(int observerId, Observer* observer, std::string& errorMsg) override;
    bool registerObserver(int observerId, const std::string& observerName, Observer* observer, std::string& errorMsg) override;
    bool unregisterObserver(int observerId, std::string& errorMsg) override;
    int sendMessage(const Message& message, std::string& description) override;
    int sendMessageToTarget(const Message& message, int targetId, std::string& description) override;
    int sendMessageToTargetByName(const Message& message, const std::string& targetName, std::string& description) override;
    int getObserverIdByName(const std::string& observerName) const override;

    /**
     * @brief Get registered observers count
     * @return Number of registered observers
     */
    size_t getObserverCount() const;

    /**
     * @brief Check if an observer is registered
     * @param observerId Observer ID to check
     * @return true if registered, false otherwise
     */
    bool isObserverRegistered(int observerId) const;

protected:
    std::unordered_map<int, Observer*> observers_;      ///< Registered observers
    std::unordered_map<std::string, int> nameToId_;     ///< Name to ID mapping
    mutable std::mutex observersMutex_;                 ///< Mutex for observer operations
};

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_MEDIATOR_HPP

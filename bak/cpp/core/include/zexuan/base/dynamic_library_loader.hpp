#pragma once

#include <string>
#include <map>
#include <stdexcept>

#ifdef _WIN32
    #include <windows.h>
    typedef HMODULE LibraryHandle;
#else
    #include <dlfcn.h>
    typedef void* LibraryHandle;
#endif

namespace zexuan {
namespace base {

/**
 * @brief 简化的动态库加载器
 * 专注于插件系统的核心需求：加载库、获取函数指针、卸载库
 */
class DynamicLibraryLoader {
public:
    /**
     * @brief 构造函数
     */
    DynamicLibraryLoader() = default;

    /**
     * @brief 析构函数 - 不再自动卸载库，必须手动调用 unloadAllLibraries()
     * 
     * 如果析构时仍有库未卸载，会在调试模式下发出警告。
     * 强烈建议在适当的时机（如系统关闭前、日志系统关闭前）主动调用 unloadAllLibraries()。
     */
    ~DynamicLibraryLoader();

    /**
     * @brief 加载动态库
     * @param libPath 动态库路径（完整路径，包含扩展名）
     * @return 加载成功返回true，失败返回false
     */
    bool loadLibrary(const std::string& libPath);

    /**
     * @brief 获取函数指针
     * @tparam FuncType 函数指针类型
     * @param libPath 动态库路径
     * @param functionName 函数名称
     * @return 类型安全的函数指针，失败返回nullptr
     */
    template<typename FuncType>
    FuncType getFunction(const std::string& libPath, const std::string& functionName) {
        auto it = loadedLibraries_.find(libPath);
        if (it == loadedLibraries_.end()) {
            lastError_ = "Library not loaded: " + libPath;
            return nullptr;
        }

        LibraryHandle handle = it->second;
        void* funcAddr = nullptr;

#ifdef _WIN32
        funcAddr = GetProcAddress(handle, functionName.c_str());
#else
        funcAddr = dlsym(handle, functionName.c_str());
#endif

        if (!funcAddr) {
            lastError_ = "Function not found: " + functionName + " in " + libPath;
            return nullptr;
        }

        lastError_.clear();
        return reinterpret_cast<FuncType>(funcAddr);
    }

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息字符串
     */
    const std::string& getLastError() const { return lastError_; }

    /**
     * @brief 加载动态库，失败时抛出异常
     * @param libPath 动态库路径
     * @throws std::runtime_error 加载失败时抛出
     */
    void loadLibraryOrThrow(const std::string& libPath);

    /**
     * @brief 获取函数指针，失败时抛出异常
     * @tparam FuncType 函数指针类型
     * @param libPath 动态库路径
     * @param functionName 函数名称
     * @return 类型安全的函数指针
     * @throws std::runtime_error 获取失败时抛出
     */
    template<typename FuncType>
    FuncType getFunctionOrThrow(const std::string& libPath, const std::string& functionName) {
        auto func = getFunction<FuncType>(libPath, functionName);
        if (!func) {
            throw std::runtime_error("Cannot load function '" + functionName + "' from: " + libPath + " - " + getLastError());
        }
        return func;
    }

    /**
     * @brief 获取可选的函数指针（不抛出异常）
     * @tparam FuncType 函数指针类型
     * @param libPath 动态库路径
     * @param functionName 函数名称
     * @return 函数指针，失败返回nullptr（这是正常情况）
     */
    template<typename FuncType>
    FuncType getFunctionOptional(const std::string& libPath, const std::string& functionName) {
        return getFunction<FuncType>(libPath, functionName);
    }

    /**
     * @brief 手动卸载所有已加载的动态库
     * 
     * 必须在适当的时机手动调用此函数来卸载所有动态库。
     * 建议在系统关闭序列中、日志系统关闭之前调用。
     * 析构函数不再自动调用此函数。
     */
    void unloadAllLibraries();

private:
    // 已加载的动态库句柄映射
    std::map<std::string, LibraryHandle> loadedLibraries_;

    // 最后的错误信息
    mutable std::string lastError_;
};

} // namespace base
} // namespace zexuan

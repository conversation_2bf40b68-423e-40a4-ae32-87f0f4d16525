  //
// This is an internal header file, you should not include this.

#ifndef  NET_POLLER_H
#define  NET_POLLER_H

#include <map>
#include <vector>

#include "zexuan/net/Timestamp.h"
#include "zexuan/net/EventLoop.h"
#include "zexuan/base/noncopyable.hpp"
#include "zexuan/logger.hpp"

namespace zexuan
{
namespace net
{

class Channel;

class Poller : zexuan::base::noncopyable
{
 public:
  typedef std::vector<Channel*> ChannelList;

  Poller(EventLoop* loop);
  virtual ~Poller();

  virtual Timestamp poll(int timeoutMs, ChannelList* activeChannels) = 0;

  virtual void updateChannel(Channel* channel) = 0;

  virtual void removeChannel(Channel* channel) = 0;

  virtual bool hasChannel(Channel* channel) const;

  static Poller* newDefaultPoller(EventLoop* loop);

  void assertInLoopThread() const
  {
    ownerLoop_->assertInLoopThread();
  }

 protected:
  typedef std::map<int, Channel*> ChannelMap;
  ChannelMap channels_;

 private:
  EventLoop* ownerLoop_;
};

}  // namespace net
}  // namespace zexuan

#endif  //  NET_POLLER_H

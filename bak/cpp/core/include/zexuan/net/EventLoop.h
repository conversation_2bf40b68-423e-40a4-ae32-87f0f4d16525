#ifndef  NET_EVENTLOOP_H
#define  NET_EVENTLOOP_H

#include <atomic>
#include <functional>
#include <vector>
#include <mutex>
#include <thread>

#include <boost/any.hpp>
#include "zexuan/net/Timestamp.h"
#include "zexuan/net/Callbacks.h"
#include "zexuan/base/noncopyable.hpp"
#include "zexuan/logger.hpp"

namespace zexuan
{
namespace net
{

class Channel;
class Poller;


class EventLoop : zexuan::base::noncopyable
{
 public:
  typedef std::function<void()> Functor;

  EventLoop();
  ~EventLoop();  // force out-line dtor, for std::unique_ptr members.

  void loop();

  void quit();

  Timestamp pollReturnTime() const { return pollReturnTime_; }

  int64_t iteration() const { return iteration_; }

  void runInLoop(Functor cb);

  void queueInLoop(Functor cb);

  size_t queueSize() const;

  void wakeup();
  void updateChannel(Channel* channel);
  void removeChannel(Channel* channel);
  bool hasChannel(Channel* channel);

  void assertInLoopThread()
  {
    if (!isInLoopThread())
    {
      abortNotInLoopThread();
    }
  }
  bool isInLoopThread() const { return threadId_ == std::this_thread::get_id(); }

  bool eventHandling() const { return eventHandling_; }

  void setContext(const boost::any& context)
  { context_ = context; }

  const boost::any& getContext() const
  { return context_; }

  boost::any* getMutableContext()
  { return &context_; }

  static EventLoop* getEventLoopOfCurrentThread();

 private:
  void abortNotInLoopThread();
  void handleRead(Timestamp);  // waked up
  void doPendingFunctors();

  void printActiveChannels() const; // DEBUG

  typedef std::vector<Channel*> ChannelList;

  bool looping_; /* atomic */
  std::atomic<bool> quit_;
  bool eventHandling_; /* atomic */
  bool callingPendingFunctors_; /* atomic */
  int64_t iteration_;
  const std::thread::id threadId_;
  Timestamp pollReturnTime_;
  std::unique_ptr<Poller> poller_;
  int wakeupFd_;
  std::unique_ptr<Channel> wakeupChannel_;
  boost::any context_;

  ChannelList activeChannels_;
  Channel* currentActiveChannel_;

  mutable std::mutex mutex_;
  std::vector<Functor> pendingFunctors_;
};

}  // namespace net
}  // namespace zexuan

#endif  //  NET_EVENTLOOP_H

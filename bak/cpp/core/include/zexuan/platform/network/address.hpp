#ifndef ZEXUAN_PLATFORM_NETWORK_ADDRESS_HPP
#define ZEXUAN_PLATFORM_NETWORK_ADDRESS_HPP

#include "zexuan/platform/platform.hpp"
#include <string>
#include <cstdint>

// 平台特定的头文件包含
#ifdef ZEXUAN_WINDOWS
    #include <winsock2.h>
    #include <ws2tcpip.h>
#else
    #include <netinet/in.h>
    #include <sys/socket.h>
#endif

namespace zexuan {
namespace platform {
namespace network {

class Address {
public:
    // 构造函数
    Address();
    Address(uint16_t port, bool loopbackOnly = false);
    Address(const std::string& ip, uint16_t port);
    explicit Address(const struct sockaddr_in& addr);

    // 核心方法
    std::string toIp() const;
    std::string toIpPort() const;
    uint16_t port() const;
    sa_family_t family() const;
    
    // 系统调用接口
    const struct sockaddr* getSockAddr() const;
    struct sockaddr_in toSockAddr() const;

    // 网络字节序
    uint32_t ipv4NetEndian() const;
    uint16_t portNetEndian() const;

private:
    struct sockaddr_in addr_;
};

} // namespace network
} // namespace platform
} // namespace zexuan

#endif // ZEXUAN_PLATFORM_NETWORK_ADDRESS_HPP

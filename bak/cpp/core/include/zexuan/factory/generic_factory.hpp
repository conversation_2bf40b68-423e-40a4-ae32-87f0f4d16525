#pragma once

#include <map>
#include <string>
#include <memory>
#include <vector>
#include <functional>

namespace zexuan {

/**
 * 通用工厂基类模板
 * 提供基于字符串名称的对象创建机制
 * 
 * @tparam BaseType 要创建的对象的基类型
 */
template<typename BaseType>
class GenericFactory {
public:
    // 创建函数类型定义
    typedef std::function<std::shared_ptr<BaseType>()> CreateFn;
    typedef std::map<std::string, CreateFn> FactoryMap;

protected:
    /**
     * 获取工厂映射表的静态实例
     * 每个模板实例化都有自己的映射表
     */
    static FactoryMap& getFactoryMap() {
        static FactoryMap factoryMap;
        return factoryMap;
    }

public:
    /**
     * 注册一个类的创建函数
     * @param name 类名称标识符
     * @param createFn 创建函数
     * @return 注册成功返回true，已存在返回false
     */
    static bool registerClass(const std::string& name, CreateFn createFn) {
        auto& factoryMap = getFactoryMap();
        if (factoryMap.find(name) != factoryMap.end()) {
            return false; // 已存在
        }
        factoryMap[name] = createFn;
        return true;
    }

    /**
     * 根据名称创建对象实例
     * @param name 类名称标识符
     * @return 创建的对象智能指针，失败返回nullptr
     */
    static std::shared_ptr<BaseType> create(const std::string& name) {
        auto& factoryMap = getFactoryMap();
        auto it = factoryMap.find(name);
        return (it != factoryMap.end()) ? it->second() : nullptr;
    }

    /**
     * 注销一个已注册的类
     * @param name 类名称标识符
     * @return 注销成功返回true，不存在返回false
     */
    static bool unregisterClass(const std::string& name) {
        auto& factoryMap = getFactoryMap();
        auto it = factoryMap.find(name);
        if (it != factoryMap.end()) {
            factoryMap.erase(it);
            return true;
        }
        return false;
    }

    /**
     * 检查是否已注册指定名称的类
     * @param name 类名称标识符
     * @return 已注册返回true，否则返回false
     */
    static bool isRegistered(const std::string& name) {
        auto& factoryMap = getFactoryMap();
        return factoryMap.find(name) != factoryMap.end();
    }

    /**
     * 获取所有已注册的类名称
     * @return 所有已注册类名称的向量
     */
    static std::vector<std::string> getRegisteredNames() {
        std::vector<std::string> names;
        auto& factoryMap = getFactoryMap();
        names.reserve(factoryMap.size());
        for (const auto& pair : factoryMap) {
            names.push_back(pair.first);
        }
        return names;
    }

    /**
     * 获取已注册类的数量
     * @return 已注册类的数量
     */
    static size_t getRegisteredCount() {
        return getFactoryMap().size();
    }

    /**
     * 清空所有已注册的类
     */
    static void clearAll() {
        getFactoryMap().clear();
    }
};

} // namespace zexuan

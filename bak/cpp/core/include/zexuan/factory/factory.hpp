#pragma once

/**
 * 工厂模式相关组件的组合头文件
 * 包含通用工厂和自动注册器
 */

#include "zexuan/factory/generic_factory.hpp"
#include "zexuan/factory/auto_register.hpp"
#include "zexuan/base/singleton.hpp"

namespace zexuan {

/**
 * 单例工厂模板类
 * 结合了单例模式和通用工厂的功能
 * 
 * @tparam BaseType 要创建的对象的基类型
 */
template<typename BaseType>
class SingletonFactory : public GenericFactory<BaseType>, 
                         public singleton<SingletonFactory<BaseType>> {
public:
    // 为了方便使用，重新定义类型别名
    typedef BaseType base_type;
    typedef typename GenericFactory<BaseType>::CreateFn CreateFn;

private:
    friend class singleton<SingletonFactory<BaseType>>;
    SingletonFactory() = default;
    ~SingletonFactory() = default;
};

} // namespace zexuan

#pragma once

#include <memory>
#include <string>
#include <filesystem>
#include <unordered_map>
#include <mutex>
#include <signal.h>
#include <spdlog/spdlog.h>
#include <spdlog/async.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/basic_file_sink.h>

namespace zexuan {

/**
 * @brief 异步日志管理器
 *
 * 提供高性能的异步控制台logger和文件logger
 * 使用线程池处理日志，避免阻塞主线程
 */
class Logger {
private:
    // 初始化异步日志线程池（只初始化一次）
    static void initAsyncPool() {
        static std::once_flag init_flag;
        std::call_once(init_flag, []() {
            // 创建异步线程池：队列大小8192，线程数1
            spdlog::init_thread_pool(8192, 1);
            // 设置每1秒自动刷新所有已注册的logger
            spdlog::flush_every(std::chrono::seconds(1));
        });
    }

public:
    // 获取异步控制台logger
    static std::shared_ptr<spdlog::logger> getConsoleLogger() {
        static auto console_logger = []() {
            initAsyncPool();
            
            // 使用简洁的异步控制台logger创建方式
            auto logger = spdlog::stdout_color_mt<spdlog::async_factory>("console");
            
            // 设置日志级别和格式
            logger->set_level(spdlog::level::debug);
            logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [thread %t] [%^%l%$] %v");
            
            return logger;
        }();
        return console_logger;
    }

    // 获取异步文件logger（保存在logs文件夹中）
    static std::shared_ptr<spdlog::logger> getFileLogger(const std::string& filename) {
        static std::unordered_map<std::string, std::shared_ptr<spdlog::logger>> file_loggers;
        static std::mutex loggers_mutex;
        
        std::lock_guard<std::mutex> lock(loggers_mutex);
        
        // 检查是否已存在
        auto it = file_loggers.find(filename);
        if (it != file_loggers.end()) {
            return it->second;
        }
        
        try {
            initAsyncPool();
            
            // 确保logs目录存在
            std::filesystem::create_directories("logs");

            // 使用简洁的异步文件logger创建方式
            auto logger = spdlog::basic_logger_mt<spdlog::async_factory>(
                filename, 
                "logs/" + filename + ".log"
            );

            // 设置日志级别和格式
            logger->set_level(spdlog::level::debug);
            logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [thread %t] [%^%l%$] %v");

            file_loggers[filename] = logger;
            return logger;
        } catch (const std::exception& e) {
            // 回退到已存在的logger
            return spdlog::get(filename);
        }
    }
    
};

} // namespace zexuan

/**
 * @file plugin_base.cpp
 * @brief Implementation of PluginBase class
 * <AUTHOR> project
 * @date 2024
 */

#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/logger.hpp"
#include <stdexcept>
#include <iostream>

namespace zexuan {
namespace plugin {

PluginBase::PluginBase(std::shared_ptr<base::Mediator> mediator, 
                       int pluginId, 
                       const std::string& name)
    : base::BaseObserver(pluginId, name)
    , base::BaseSubject(pluginId, name)
    , mediator_(mediator)
    , initialized_(false)
    , registered_(false) {
    
    // Validate input parameters
    if (!mediator) {
        throw std::invalid_argument("Mediator cannot be null");
    }
    
    if (pluginId < 0) {
        throw std::invalid_argument("Plugin ID must be non-negative");
    }
    
    if (name.empty()) {
        throw std::invalid_argument("Plugin name cannot be empty");
    }
    
    // Set the mediator for the subject functionality
    setMediator(mediator_);
    
    // Register with mediator
    if (!registerWithMediator()) {
        throw std::runtime_error("Failed to register plugin with mediator");
    }

    auto logger = Logger::getFileLogger("plugin_base");
    logger->info("Plugin created: ID={}, Name={}", pluginId, name);
}

PluginBase::~PluginBase() {
    // Perform shutdown if not already done
    if (initialized_) {
        shutdown();
    }
    
    // Unregister from mediator
    unregisterFromMediator();

    auto logger = Logger::getFileLogger("plugin_base");
    logger->info("Plugin destroyed: ID={}, Name={}",
                base::BaseObserver::getId(),
                base::BaseObserver::name_);
}

void PluginBase::onNotify(base::Subject* subject, const base::Message& message) {
    // Call the base observer implementation first (handles callback if set)
    base::BaseObserver::onNotify(subject, message);
    
    // Then call our plugin-specific message processing
    processMessage(message);
}

int PluginBase::sendPluginMessage(const base::Message& message) {
    if (!mediator_) {
        auto logger = Logger::getFileLogger("plugin_base");
        logger->error("Plugin {}: No mediator available for sending message", base::BaseObserver::getId());
        return -1;
    }
    
    // Use the subject's sendMessage method which handles source address setting
    return sendMessage(message);
}

int PluginBase::sendPluginMessageToTarget(base::Message message, const std::string& targetName) {
    if (!mediator_) {
        auto logger = Logger::getFileLogger("plugin_base");
        logger->error("Plugin {}: No mediator available for sending message", base::BaseObserver::getId());
        return -1;
    }

    // 通过 Mediator 的“按名称路由”接口，不改写 message
    std::string description;
    int result = mediator_->sendMessageToTargetByName(message, targetName, description);
    if (result != 0) {
        auto logger = Logger::getFileLogger("plugin_base");
        logger->error("Plugin {}: Failed to send message to target '{}' : {}", base::BaseObserver::getId(), targetName, description);
    }
    return result;
}

bool PluginBase::registerWithMediator() {
    if (!mediator_) {
        return false;
    }
    
    std::string errorMsg;
    // Use the new registration method with both ID and name
    bool success = mediator_->registerObserver(base::BaseObserver::getId(), base::BaseObserver::name_, this, errorMsg);

    auto logger = Logger::getFileLogger("plugin_base");
    if (success) {
        registered_ = true;
        logger->info("Plugin {} ({}) registered with mediator successfully", base::BaseObserver::getId(), base::BaseObserver::name_);
    } else {
        logger->error("Failed to register plugin {} ({}) with mediator: {}", base::BaseObserver::getId(), base::BaseObserver::name_, errorMsg);
    }
    
    return success;
}

void PluginBase::unregisterFromMediator() {
    if (!mediator_ || !registered_) {
        return;
    }
    
    std::string errorMsg;
    bool success = mediator_->unregisterObserver(base::BaseObserver::getId(), errorMsg);

    auto logger = Logger::getFileLogger("plugin_base");
    if (success) {
        registered_ = false;
        logger->info("Plugin {} unregistered from mediator successfully", base::BaseObserver::getId());
    } else {
        logger->error("Failed to unregister plugin {} from mediator: {}", base::BaseObserver::getId(), errorMsg);
    }
}

} // namespace plugin
} // namespace zexuan

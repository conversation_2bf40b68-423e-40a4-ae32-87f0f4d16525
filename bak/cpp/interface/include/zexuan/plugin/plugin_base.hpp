/**
 * @file plugin_base.hpp
 * @brief Base class for plugin system implementation
 * <AUTHOR> project
 * @date 2024
 *
 * Simple plugin base class that integrates with the existing mediator pattern
 * architecture. Provides automatic observer/subject registration and basic
 * plugin lifecycle management.
 */

#ifndef ZEXUAN_PLUGIN_BASE_HPP
#define ZEXUAN_PLUGIN_BASE_HPP

#include "zexuan/base/observer.hpp"
#include "zexuan/base/subject.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"
#include <memory>
#include <string>

namespace zexuan {
namespace plugin {

/**
 * @brief Base class for all plugins
 *
 * This class provides a simple foundation for plugin development by:
 * - Inheriting from both BaseObserver and BaseSubject
 * - Automatically registering with the mediator during construction
 * - Providing virtual methods for plugin lifecycle management
 * - Handling cleanup during destruction
 */
class PluginBase : public base::BaseObserver, public base::BaseSubject {
public:
    /**
     * @brief Constructor with dependency injection
     * @param mediator Shared pointer to the mediator instance
     * @param pluginId Unique identifier for this plugin
     * @param name Human-readable name of the plugin
     * @throws std::invalid_argument if mediator is null or pluginId is invalid
     */
    PluginBase(std::shared_ptr<base::Mediator> mediator, 
               int pluginId, 
               const std::string& name);

    /**
     * @brief Virtual destructor
     * Automatically unregisters from mediator and cleans up resources
     */
    virtual ~PluginBase();

    // Disable copy constructor and assignment operator
    PluginBase(const PluginBase&) = delete;
    PluginBase& operator=(const PluginBase&) = delete;

    /**
     * @brief Initialize the plugin
     * Called after construction to perform plugin-specific initialization
     * @return true on success, false on failure
     */
    virtual bool initialize() { return true; }

    /**
     * @brief Shutdown the plugin
     * Called before destruction to perform plugin-specific cleanup
     */
    virtual void shutdown() {}

    /**
     * @brief Handle incoming messages
     * Override this method to process messages received by this plugin
     * @param subject The subject that sent the message (may be nullptr)
     * @param message The message to process
     */
    void onNotify(base::Subject* subject, const base::Message& message) override;

    /**
     * @brief Get the plugin's unique identifier
     * @return Plugin ID
     */
    int getPluginId() const { return base::BaseObserver::getId(); }

    /**
     * @brief Get the plugin's name
     * @return Plugin name
     */
    std::string getPluginName() const { return base::BaseObserver::name_; }

    /**
     * @brief Check if the plugin is properly initialized
     * @return true if initialized, false otherwise
     */
    bool isInitialized() const { return initialized_; }

protected:
    /**
     * @brief Process plugin-specific messages
     * Override this method in derived classes to handle plugin-specific logic
     * @param message The message to process
     */
    virtual void processMessage(const base::Message& message) {}

    /**
     * @brief Send a message to another plugin or component
     * @param message The message to send (target address should be set)
     * @return 0 on success, negative on failure
     */
    int sendPluginMessage(const base::Message& message);

    /**
     * @brief Send a message to another plugin by name
     * @param message The message to send
     * @param targetName Target plugin name
     * @return 0 on success, negative on failure
     */
    int sendPluginMessageToTarget(base::Message message, const std::string& targetName);

protected:
    std::shared_ptr<base::Mediator> mediator_;  ///< Mediator instance for communication
    bool initialized_;                          ///< Initialization status
    bool registered_;                           ///< Registration status with mediator

    /**
     * @brief Register this plugin with the mediator
     * @return true on success, false on failure
     */
    bool registerWithMediator();

    /**
     * @brief Unregister this plugin from the mediator
     */
    void unregisterFromMediator();
};

// Plugin function type definitions (after PluginBase class declaration)
using create_plugin_func = PluginBase* (*)(void*, int, const char*);
using destroy_plugin_func = void (*)(PluginBase*);
using get_plugin_info_func = const char* (*)();

} // namespace plugin
} // namespace zexuan

#endif // ZEXUAN_PLUGIN_BASE_HPP

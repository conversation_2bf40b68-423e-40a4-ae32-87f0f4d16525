/**
 * @file plugin_export.hpp
 * @brief 插件导出辅助宏，简化插件开发
 * <AUTHOR> project
 * @date 2024
 */

#ifndef ZEXUAN_PLUGIN_EXPORT_HPP
#define ZEXUAN_PLUGIN_EXPORT_HPP

#include "zexuan/plugin/plugin_base.hpp"
#include <memory>
#include <string>

/**
 * @brief 简化的插件导出宏
 * 
 * 自动生成所有必需的 extern "C" 导出函数，只需要类名
 * 
 * @param ClassName 插件类名
 * 
 * 使用示例：
 * ZEXUAN_PLUGIN_EXPORT(FilenamePlugin)
 */
#define ZEXUAN_PLUGIN_EXPORT(ClassName) \
extern "C" { \
    \
    /** 创建插件实例 */ \
    zexuan::plugin::PluginBase* create_plugin(void* mediator, int pluginId, const char* name) { \
        try { \
            auto med = static_cast<std::shared_ptr<zexuan::base::Mediator>*>(mediator); \
            return new ClassName(*med, pluginId, std::string(name)); \
        } catch (const std::exception& e) { \
            /* 记录错误但不抛出异常（C接口） */ \
            return nullptr; \
        } catch (...) { \
            return nullptr; \
        } \
    } \
    \
    /** 销毁插件实例 */ \
    void destroy_plugin(zexuan::plugin::PluginBase* plugin) { \
        try { \
            delete plugin; \
        } catch (...) { \
            /* 忽略析构函数中的异常 */ \
        } \
    } \
}

#endif // ZEXUAN_PLUGIN_EXPORT_HPP
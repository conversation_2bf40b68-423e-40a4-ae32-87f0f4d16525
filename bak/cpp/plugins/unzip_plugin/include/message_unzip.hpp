/**
 * @file message_unzip.hpp
 * @brief 解压插件消息定义
 * <AUTHOR> project
 * @date 2024
 */

#ifndef MESSAGE_UNZIP_HPP
#define MESSAGE_UNZIP_HPP

#include <string>
#include <nlohmann/json.hpp>

namespace zexuan {
namespace plugin {
namespace unzip {

/**
 * @brief 生成解压插件可用消息的JSON (Message格式的不可变部分)
 * @return JSON字符串，包含所有可用的message不可变部分
 */
inline std::string getAvailableMessagesJson() {
    nlohmann::json messages = {
        {
            "plugin_name", "unzip_plugin"
        },

        {
            "description", "解压插件 - 批量解压目录下的所有ZIP文件"
        },
        {
            "available_messages", {
                {
                    {
                        "name", "批量ZIP解压"
                    },
                    {
                        "description", "批量解压指定目录下的所有ZIP文件"
                    },
                    {
                        "message_template", {
                            {"typ", 0x01},
                            {"vsq", 0x81},
                            {"cot", 0x06},

                            {"fun", 0x01},
                            {"inf", 0x01}
                        }
                    },
                    {
                        "variable_parts", {
                            "source_directory (string): 包含ZIP文件的源目录路径"
                        }
                    }
                }
            }
        }
    };
    
    return messages.dump(4);
}

} // namespace unzip
} // namespace plugin
} // namespace zexuan

#endif // MESSAGE_UNZIP_HPP

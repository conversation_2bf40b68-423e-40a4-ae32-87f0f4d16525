# classify Plugin CMakeLists.txt

# 创建 unzip_plugin 动态库
add_library(unzip_plugin SHARED
    src/unzip_plugin.cpp
)

# 链接必要的库
target_link_libraries(unzip_plugin
    PRIVATE
        plugin_interface
        libzip::zip
)

# 设置包含目录
target_include_directories(unzip_plugin PRIVATE
    ${CMAKE_SOURCE_DIR}/core/include
    ${CMAKE_SOURCE_DIR}/interface/include
)

# 设置输出目录
set_target_properties(unzip_plugin PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/libs/plugins"
    VERSION 1.0.0
    SOVERSION 1
)

#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/plugin/plugin_export.hpp"
#include "zexuan/base/mediator.hpp"
#include "../include/message_unzip.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/base/message_types.hpp"
#include "zexuan/thread_pool.hpp"
#include "zexuan/logger.hpp"
#include <filesystem>
#include <vector>
#include <algorithm>
#include <iostream>
#include <future>
#include <mutex>
#include <atomic>
#include <thread>
#include <sstream>
#include <zip.h>
#include <cstring>
#include <sys/stat.h>

namespace fs = std::filesystem;
using namespace zexuan;

/**
 * ZipExtractor - 解压zip文件的工具类
 */
class ZipExtractor {
private:
    std::string extract_dir_;
    
    // 创建目录（包括父目录）
    bool create_directory(const std::string& path) {
        try {
            fs::create_directories(path);
            return true;
        } catch (const std::exception& e) {
            std::cerr << "创建目录失败: " << path << " - " << e.what() << std::endl;
            return false;
        }
    }
    
    // 解压单个文件
    bool extract_file(zip_t* zip, zip_uint64_t index, const std::string& output_path) {
        zip_file_t* zip_file = zip_fopen_index(zip, index, 0);
        if (!zip_file) {
            std::cerr << "无法打开zip文件中的文件: " << zip_strerror(zip) << std::endl;
            return false;
        }
        
        // 创建输出目录
        fs::path output_dir = fs::path(output_path).parent_path();
        if (!create_directory(output_dir.string())) {
            zip_fclose(zip_file);
            return false;
        }
        
        // 打开输出文件
        FILE* output_file = fopen(output_path.c_str(), "wb");
        if (!output_file) {
            std::cerr << "无法创建输出文件: " << output_path << std::endl;
            zip_fclose(zip_file);
            return false;
        }
        
        // 复制文件内容
        char buffer[8192];
        zip_int64_t bytes_read;
        while ((bytes_read = zip_fread(zip_file, buffer, sizeof(buffer))) > 0) {
            if (fwrite(buffer, 1, bytes_read, output_file) != static_cast<size_t>(bytes_read)) {
                std::cerr << "写入文件失败: " << output_path << std::endl;
                fclose(output_file);
                zip_fclose(zip_file);
                return false;
            }
        }
        
        fclose(output_file);
        zip_fclose(zip_file);
        
        // 设置文件权限（使用默认权限）
        chmod(output_path.c_str(), 0644);
        
        return true;
    }

public:
    ZipExtractor(const std::string& extract_dir) : extract_dir_(extract_dir) {
        // 确保输出目录存在
        create_directory(extract_dir_);
    }
    
    // 解压zip文件
    bool extract_zip(const std::string& zip_path) {
        auto logger = Logger::getFileLogger("plugin/unzip_plugin");
        logger->info("正在解压: {}", zip_path);
        
        int error_code;
        zip_t* zip = zip_open(zip_path.c_str(), 0, &error_code);
        if (!zip) {
            logger->error("无法打开zip文件: {} - 错误代码: {}", zip_path, error_code);
            return false;
        }
        
        zip_int64_t num_entries = zip_get_num_entries(zip, 0);
        logger->info("  文件数量: {}", num_entries);
        
        bool success = true;
        for (zip_uint64_t i = 0; i < static_cast<zip_uint64_t>(num_entries); i++) {
            zip_stat_t stat;
            if (zip_stat_index(zip, i, 0, &stat) < 0) {
                logger->error("无法获取文件信息: {}", zip_strerror(zip));
                success = false;
                continue;
            }
            
            // 跳过目录
            if (stat.name[strlen(stat.name) - 1] == '/') {
                logger->debug("  跳过目录: {}", stat.name);
                continue;
            }
            
            // 构建输出路径
            std::string output_path = extract_dir_ + "/" + stat.name;
            logger->debug("  解压文件: {} -> {}", stat.name, output_path);
            
            if (!extract_file(zip, i, output_path)) {
                success = false;
            }
        }
        
        zip_close(zip);
        return success;
    }
};

/**
 * Unzip Plugin - 多线程批量解压zip文件插件
 * 功能：使用线程池并行处理目录下的所有zip文件解压
 * 解压完成后发送消息给filename插件进行重命名处理
 */
class UnzipPlugin : public zexuan::plugin::PluginBase {
private:
    std::unique_ptr<zexuan::ThreadPool> threadPool_;
    std::mutex resultMutex_;
    std::atomic<int> processedZips_{0};
    std::atomic<int> successfulExtracts_{0};
    std::atomic<int> failedExtracts_{0};

public:
    UnzipPlugin(std::shared_ptr<zexuan::base::Mediator> mediator, int pluginId, const std::string& name)
        : PluginBase(mediator, pluginId, name),
          threadPool_(std::make_unique<zexuan::ThreadPool>(16)) {  
    }

    bool initialize() override {
        auto logger = Logger::getFileLogger("plugin/unzip_plugin");
        logger->info("UnzipPlugin {} initializing...", getPluginId());
        return true;
    }

    void shutdown() override {
        auto logger = Logger::getFileLogger("plugin/unzip_plugin");
        logger->info("UnzipPlugin {} shutting down...", getPluginId());
    }

protected:
    void processMessage(const zexuan::base::Message& message) override {
        auto logger = Logger::getFileLogger("plugin/unzip_plugin");
        logger->debug("UnzipPlugin {} received message: TYP={}, COT={}, Source={}, Target={}",
                     getPluginId(),
                     static_cast<int>(message.getTyp()),
                     static_cast<int>(message.getCot()),
                     static_cast<int>(message.getSource()),
                     static_cast<int>(message.getTarget()));

        // 处理插件信息请求
        if (message.getCot() == static_cast<uint8_t>(zexuan::base::CauseOfTransmission::INFO_REQUEST)) {
            logger->info("Plugin Name: {}, ID: {}, Description: 解压插件 - 批量解压目录下的所有ZIP文件", 
                        getPluginName(), getPluginId());
            
            // 创建回复消息，交换source和target
            zexuan::base::Message responseMessage = message;
            responseMessage.swapSourceTarget();
            responseMessage.setCot(static_cast<uint8_t>(zexuan::base::CauseOfTransmission::INFO_RESPONSE));
            
            // 设置JSON响应内容
            std::string jsonResponse = zexuan::plugin::unzip::getAvailableMessagesJson();
            responseMessage.setTextContent(jsonResponse);
            
            // 发送回复给服务器
            try {
                int result = sendPluginMessageToTarget(responseMessage, "server");
                if (result == 0) {
                    logger->info("Successfully sent INFO_RESPONSE to server");
                } else {
                    logger->error("Failed to send INFO_RESPONSE to server: error code {}", result);
                }
            } catch (const std::exception& e) {
                logger->error("Error sending INFO_RESPONSE to server: {}", e.what());
            }
            return;
        }

        // 处理解压消息
        if (message.getFun() == zexuan::base::FunctionType::UNZIP_EXTRACT && 
            message.getCot() == static_cast<uint8_t>(zexuan::base::CauseOfTransmission::ACTIVATION)) {
            handleUnzipMessage(message);
        }
    }

private:
    /**
     * @brief 处理解压消息
     * @param message 包含目录路径的消息
     */
    void handleUnzipMessage(const zexuan::base::Message& message) {
        auto logger = Logger::getFileLogger("plugin/unzip_plugin");

        // 从消息的 variableStructure_ 中提取目录路径
        std::string directoryPath = message.getTextContent();

        if (directoryPath.empty()) {
            logger->error("No directory path provided in message");
            return;
        }

        logger->info("UnzipPlugin {} processing directory: {}", getPluginId(), directoryPath);

        // 使用多线程处理目录下的zip文件
        processDirectoryMultiThreaded(directoryPath);
    }

    /**
     * @brief 多线程处理目录中的zip文件解压
     * @param directoryPath 要处理的目录路径
     */
    void processDirectoryMultiThreaded(const std::string& directoryPath) {
        auto logger = Logger::getFileLogger("plugin/unzip_plugin");

        // 检查目录是否存在
        if (!fs::exists(directoryPath) || !fs::is_directory(directoryPath)) {
            logger->error("Invalid directory path: {}", directoryPath);
            return;
        }

        // 重置计数器
        processedZips_ = 0;
        successfulExtracts_ = 0;
        failedExtracts_ = 0;

        // 收集所有zip文件
        std::vector<fs::path> zipFiles;
        try {
            for (const auto& entry : fs::directory_iterator(directoryPath)) {
                if (entry.is_regular_file() && entry.path().extension() == ".zip") {
                    zipFiles.push_back(entry.path());
                }
            }
        } catch (const fs::filesystem_error& e) {
            logger->error("Error reading directory: {}", e.what());
            return;
        }

        if (zipFiles.empty()) {
            logger->info("No zip files found in directory: {}", directoryPath);
            return;
        }

        logger->info("Found {} zip files to process", zipFiles.size());

        // 创建解压目录
        std::string extractBaseDir = directoryPath + "/extracted";
        
        // 提交所有zip文件解压任务到线程池
        std::vector<std::future<void>> futures;
        futures.reserve(zipFiles.size());

        for (const auto& zipPath : zipFiles) {
            auto future = threadPool_->commit([this, zipPath, extractBaseDir]() {
                extractZipThreadSafe(zipPath, extractBaseDir);
            });
            futures.push_back(std::move(future));
        }

        // 等待所有任务完成
        for (auto& future : futures) {
            try {
                future.get();
            } catch (const std::exception& e) {
                logger->error("Zip extraction task error: {}", e.what());
            }
        }

        // 输出处理结果
        logger->info("Unzip processing completed!");
        logger->info("  Total zip files: {}", zipFiles.size());
        logger->info("  Processed: {}", processedZips_.load());
        logger->info("  Successful extracts: {}", successfulExtracts_.load());
        logger->info("  Failed extracts: {}", failedExtracts_.load());

    }

    /**
     * @brief 线程安全的zip文件解压方法
     * @param zipPath 要解压的zip文件路径
     * @param extractBaseDir 解压基础目录
     */
    void extractZipThreadSafe(const fs::path& zipPath, const std::string& extractBaseDir) {
        auto logger = Logger::getFileLogger("plugin/unzip_plugin");
        
        try {
            // 为每个zip文件创建单独的子目录
            std::string zipName = zipPath.stem().string();
            std::string extractDir = extractBaseDir + "/" + zipName;
            
            // 创建解压器并解压
            ZipExtractor extractor(extractDir);
            bool success = extractor.extract_zip(zipPath.string());

            // 线程安全地更新计数器和输出日志
            {
                std::lock_guard<std::mutex> lock(resultMutex_);
                if (success) {
                    successfulExtracts_++;
                    logger->info("UnzipPlugin: Successfully extracted: {} -> {}", 
                               zipPath.filename().string(), extractDir);
                    sendMessageToFilenamePlugin(extractDir);
                } else {
                    failedExtracts_++;
                    logger->error("UnzipPlugin: Failed to extract: {}", 
                                zipPath.filename().string());
                }
            }

        } catch (const std::exception& e) {
            // 线程安全地更新失败计数器和输出错误日志
            {
                std::lock_guard<std::mutex> lock(resultMutex_);
                failedExtracts_++;
                logger->error("UnzipPlugin: Unexpected error extracting {}: {}",
                            zipPath.filename().string(), e.what());
            }
        }

        // 更新处理计数
        processedZips_++;
    }

    /**
     * @brief 发送消息给filename插件
     * @param extractedDir 解压完成的目录路径
     */
    void sendMessageToFilenamePlugin(const std::string& extractedDir) {
        auto logger = Logger::getFileLogger("plugin/unzip_plugin");
        
        try {
            // 创建消息: TYP=1, VSQ=1, COT=6, Source=3, FUN=1, INF=0
            zexuan::base::Message message(0x01, 0x81, 0x06, 0x03, 0x01, 0x00);
            message.setTextContent(extractedDir);  // 设置解压目录路径
            
            // 发送消息到filename_plugin
            int result = sendPluginMessageToTarget(message, "filename_plugin");
            if (result == 0) {
                logger->info("Successfully sent message to filename_plugin with path: {}", extractedDir);
            } else {
                logger->error("Failed to send message to filename_plugin: error code {}", result);
            }
            
        } catch (const std::exception& e) {
            logger->error("Error creating/sending message to FilenamePlugin: {}", e.what());
        }
    }
};

// === 插件导出（使用标准化宏） ===
ZEXUAN_PLUGIN_EXPORT(UnzipPlugin)
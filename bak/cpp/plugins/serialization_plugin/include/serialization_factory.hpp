#pragma once

#include "serialization_base.hpp"
#include "zexuan/factory/factory.hpp"
#include <vector>
#include <string>

namespace serialization {

using SerializationFactory = zexuan::SingletonFactory<SerializationBase>;

class SerializationRegistrar {
public:
    static void registerAll();
    static std::vector<std::string> getRegistered();
    static bool isRegistered(const std::string& name);
};

} // namespace serialization


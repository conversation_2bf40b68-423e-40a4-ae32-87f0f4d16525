#pragma once

#include <string>
#include <vector>
#include <utility>

namespace serialization {

// 统一异常类型
class SerializationException : public std::exception {
private:
    std::string message_;
public:
    explicit SerializationException(const std::string& msg) : message_(msg) {}
    const char* what() const noexcept override { return message_.c_str(); }
};

// 解析结果：仅保留必要字段（格式 + 扁平化的键值对）
struct ParseResult {
    std::string format;                                  // "json" | "xml"
    std::vector<std::pair<std::string,std::string>> items; // 键值对列表（XML: key/value 属性；JSON: 顶层 key / 文本化 value）
};

// 简化后的序列化基类：仅保留格式名与统一解析接口
class SerializationBase {
public:
    virtual ~SerializationBase() = default;
    virtual std::string getFormatName() const = 0;

    // 统一解析接口（op恒定为1：只做解析与读取）
    virtual bool parseFile(const std::string& filePath, int op, ParseResult& out, std::string& err) = 0;
};

} // namespace serialization

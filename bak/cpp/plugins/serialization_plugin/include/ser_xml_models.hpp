#pragma once

#include <string>
#include <vector>
#include "ser_reflect_xml.hpp"

namespace ser_models {

struct SubItem {
    std::string key;
    std::string value;
    std::string desc;
};

struct Item {
    std::string key;
    std::string value;
    std::string desc;
    std::vector<SubItem> subs;
};

struct Config {
    std::string key;
    std::vector<Item> items;
};

} // namespace ser_models

// 生成解析函数（全局命名空间的 parseXml 重载）
SER_BEGIN_REFLECT_XML(ser_models::SubItem, "subItem")
    SER_XML_ATTR(key,   "key");
    SER_XML_ATTR(value, "value");
    SER_XML_ATTR(desc,  "desc");
SER_END_REFLECT_XML()

SER_BEGIN_REFLECT_XML(ser_models::Item, "item")
    SER_XML_ATTR(key,   "key");
    SER_XML_ATTR(value, "value");
    SER_XML_ATTR(desc,  "desc");
    SER_XML_CHILD_VECTOR(subs, ser_models::SubItem, "subItem");
SER_END_REFLECT_XML()

inline bool parseXml(ser_models::Config& out, const tinyxml2::XMLElement* elem) {
    if (!elem) return false;
    const char* k = elem->Attribute("key");
    ser_reflect::assign_from_cstr(out.key, k);
    out.items.clear();
    for (auto* it = elem->FirstChildElement("item"); it; it = it->NextSiblingElement("item")) {
        ser_models::Item item{}; if (!parseXml(item, it)) return false; out.items.push_back(std::move(item));
    }
    return true;
}


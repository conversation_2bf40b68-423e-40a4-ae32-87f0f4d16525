#pragma once

#include <tinyxml2.h>
#include <string>
#include <vector>

namespace ser_reflect {

inline void assign_from_cstr(std::string& dst, const char* v) { dst = v ? std::string(v) : std::string(); }
inline void assign_from_cstr(int& dst, const char* v) { dst = v ? std::stoi(v) : 0; }
inline void assign_from_cstr(long& dst, const char* v) { dst = v ? std::stol(v) : 0L; }
inline void assign_from_cstr(double& dst, const char* v) { dst = v ? std::stod(v) : 0.0; }
inline void assign_from_cstr(bool& dst, const char* v) {
    if (!v) { dst = false; return; }
    std::string s(v);
    for (auto& c : s) c = (char)tolower(c);
    dst = (s == "1" || s == "true" || s == "yes");
}

} // namespace ser_reflect

// 声明：宏生成的 parseXml 在全局命名空间，依类型重载
// 使用方式与 test/reflectxml 类似，但结构体命名避免冲突

template <typename T>
inline bool parseXml(T& out, const tinyxml2::XMLElement* elem);

#define SER_BEGIN_REFLECT_XML(TYPE, ELEMENT_NAME_STR) \
    inline bool parseXml(TYPE& out, const tinyxml2::XMLElement* elem) { \
        if (!elem) return false; \
        bool ok = true; \
        (void)ok; (void)ELEMENT_NAME_STR;

#define SER_XML_ATTR(FIELD, ATTR_NAME_STR) \
        do { const char* v = elem->Attribute(ATTR_NAME_STR); \
             ser_reflect::assign_from_cstr(out.FIELD, v); } while(0)

#define SER_XML_CHILD_VECTOR(FIELD_VEC, CHILD_TYPE, CHILD_ELEM_NAME) \
        do { const tinyxml2::XMLElement* ch = elem->FirstChildElement(CHILD_ELEM_NAME); \
             for (; ch; ch = ch->NextSiblingElement(CHILD_ELEM_NAME)) { \
                 CHILD_TYPE item{}; \
                 if (!::parseXml(item, ch)) ok = false; \
                 out.FIELD_VEC.push_back(std::move(item)); \
             } } while(0)

#define SER_END_REFLECT_XML() \
        return ok; \
    }


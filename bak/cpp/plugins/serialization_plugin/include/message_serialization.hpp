/**
 * @file message_serialization.hpp
 * @brief 序列化插件消息定义
 * <AUTHOR> project
 * @date 2024
 */

#ifndef MESSAGE_SERIALIZATION_HPP
#define MESSAGE_SERIALIZATION_HPP

#include <string>
#include <nlohmann/json.hpp>

namespace zexuan {
namespace plugin {
namespace serialization {

/**
 * @brief 生成序列化插件可用消息的JSON (Message格式的不可变部分)
 * @return JSON字符串，包含所有可用的message不可变部分
 */
inline std::string getAvailableMessagesJson() {
    nlohmann::json messages = {
        {
            "plugin_name", "serialization_plugin"
        },

        {
            "description", "序列化插件 - 支持XML/JSON格式数据序列化"
        },
        {
            "available_messages", {
                {
                    {
                        "name", "XML序列化"
                    },
                    {
                        "description", "将数据序列化为XML格式"
                    },
                    {
                        "message_template", {
                            {"typ", 0x01},
                            {"vsq", 0x81},
                            {"cot", 0x06},

                            {"fun", 0x01},
                            {"inf", 0x01}
                        }
                    },
                    {
                        "variable_parts", {
                            "input_file (string): 输入文件路径"
                        }
                    }
                },
                {
                    {
                        "name", "JSON序列化"
                    },
                    {
                        "description", "将数据序列化为JSON格式"
                    },
                    {
                        "message_template", {
                            {"typ", 0x01},
                            {"vsq", 0x81},
                            {"cot", 0x06},

                            {"fun", 0x02},
                            {"inf", 0x01}
                        }
                    },
                    {
                        "variable_parts", {
                            "input_file (string): 输入文件路径"
                        }
                    }
                }
            }
        }
    };
    
    return messages.dump(4);
}

} // namespace serialization
} // namespace plugin
} // namespace zexuan

#endif // MESSAGE_SERIALIZATION_HPP

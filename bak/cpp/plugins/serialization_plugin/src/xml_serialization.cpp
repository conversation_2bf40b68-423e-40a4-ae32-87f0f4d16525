#include "../include/xml_serialization.hpp"
#include "../include/serialization_base.hpp"
#include "../include/ser_xml_models.hpp"
#include <tinyxml2.h>
#include <fstream>

namespace serialization {

bool XmlSerialization::parseFile(const std::string& filePath, int /*op*/, ParseResult& out, std::string& err) {
    out = ParseResult{}; out.format = "xml";
    try {
        std::ifstream ifs(filePath, std::ios::binary);
        if (!ifs.is_open()) { err = "open file failed"; return false; }
        std::string content((std::istreambuf_iterator<char>(ifs)), std::istreambuf_iterator<char>());
        tinyxml2::XMLDocument doc;
        if (doc.Parse(content.c_str()) != tinyxml2::XML_SUCCESS) { err = "xml parse failed"; return false; }
        // 使用反射风格解析到模型
        auto* root = doc.RootElement();
        if (!root) { err = "no root"; return false; }

        ser_models::Config cfg{};
        if (!parseXml(cfg, root)) { err = "reflect parse failed"; return false; }

        // 扁平化输出：保持原有接口（收集 item/subItem 的 key/value）
        for (const auto& it : cfg.items) {
            out.items.emplace_back(it.key, it.value);
            for (const auto& s : it.subs) {
                out.items.emplace_back(s.key, s.value);
            }
        }
        return true;
    } catch (const std::exception& e) { err = e.what(); return false; }
}

} // namespace serialization

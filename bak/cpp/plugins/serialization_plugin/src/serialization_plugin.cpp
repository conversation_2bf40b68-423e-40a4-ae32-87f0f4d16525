#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/plugin/plugin_export.hpp"
#include "zexuan/base/mediator.hpp"
#include "../include/message_serialization.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/base/message_types.hpp"
#include "zexuan/logger.hpp"
#include "../include/serialization_factory.hpp"
#include <filesystem>

using namespace zexuan;

namespace fs = std::filesystem;

/**
 * Serialization Plugin - 数据序列化/反序列化插件
 * func=1/2 + inf=1/2：
 *   func=1 -> XML 格式；func=2 -> JSON 格式
 *   inf=1 -> 执行序列化；inf=2 -> 执行反序列化
 * 可变结构体中携带要处理的文件路径
 */
class SerializationPlugin : public zexuan::plugin::PluginBase {
public:
    SerializationPlugin(std::shared_ptr<zexuan::base::Mediator> mediator, int pluginId, const std::string& name)
        : PluginBase(mediator, pluginId, name) {
        serialization::SerializationRegistrar::registerAll();
    }

    bool initialize() override {
        auto logger = Logger::getFileLogger("plugin/serialization_plugin");
        logger->info("SerializationPlugin {} initializing...", getPluginId());
        return true;
    }

    void shutdown() override {
        auto logger = Logger::getFileLogger("plugin/serialization_plugin");
        logger->info("SerializationPlugin {} shutting down...", getPluginId());
    }

protected:
    void processMessage(const zexuan::base::Message& message) override {
        auto logger = Logger::getFileLogger("plugin/serialization_plugin");
        logger->debug("SerializationPlugin {} received message: TYP={}, COT={}, FUN={}",
                      getPluginId(), message.getTyp(), message.getCot(), message.getFun());

        // 处理插件信息请求
        if (message.getCot() == static_cast<uint8_t>(zexuan::base::CauseOfTransmission::INFO_REQUEST)) {
            logger->info("Plugin Name: {}, ID: {}, Description: 序列化插件 - 支持XML/JSON格式转换", 
                        getPluginName(), getPluginId());
            
            // 创建回复消息，交换source和target
            zexuan::base::Message responseMessage = message;
            responseMessage.swapSourceTarget();
            responseMessage.setCot(static_cast<uint8_t>(zexuan::base::CauseOfTransmission::INFO_RESPONSE));
            
            // 设置JSON响应内容
            std::string jsonResponse = zexuan::plugin::serialization::getAvailableMessagesJson();
            responseMessage.setTextContent(jsonResponse);
            
            // 发送回复给服务器
            try {
                int result = sendPluginMessageToTarget(responseMessage, "server");
                if (result == 0) {
                    logger->info("Successfully sent INFO_RESPONSE to server");
                } else {
                    logger->error("Failed to send INFO_RESPONSE to server: error code {}", result);
                }
            } catch (const std::exception& e) {
                logger->error("Error sending INFO_RESPONSE to server: {}", e.what());
            }
            return;
        }

        // 处理序列化消息
        if (message.getCot() != static_cast<uint8_t>(zexuan::base::CauseOfTransmission::ACTIVATION)) {
            return;
        }

        const uint8_t func = message.getFun(); // XML或JSON

        std::string filePath = message.getTextContent();
        if (filePath.empty()) {
            logger->error("Empty file path in message text content");
            return;
        }

        const std::string format = (func == zexuan::base::FunctionType::SERIALIZE_XML) ? "xml" : "json";
        auto ser = serialization::SerializationFactory::create(format);
        if (!ser) {
            logger->error("Unsupported format: {}", format);
            return;
        }

        serialization::ParseResult result; std::string err;
        if (!ser->parseFile(filePath, /*op=*/1, result, err)) { // 统一只进行解析与展示
            logger->error("Parse failed: {}", err);
            return;
        }

        logger->info("Process: format={}, file={}", format, filePath);
        // 输出解析到的键值对
        if (!result.items.empty()) {
            std::string joined;
            for (size_t i=0; i<result.items.size(); ++i) {
                if (i) joined += ", ";
                joined += result.items[i].first + "=" + result.items[i].second;
            }
            logger->info("[parse] summary items: {}", joined);
        }

        // XML: 根据 file.trans.type 触发后续流程（从 items 中找该 key）
        if (format == "xml") {
            std::string transType;
            std::string encryptType;
            for (const auto& kv : result.items) {
                if (kv.first == "file.trans.type") transType = kv.second;
                else if (kv.first == "encrypt") encryptType = kv.second;
            }
            if (!encryptType.empty() || !transType.empty()) triggerCryptoAndTransfer(transType, encryptType);
        }
    }

private:
    void triggerCryptoAndTransfer(const std::string& transType, const std::string& encryptType) {
        auto logger = Logger::getFileLogger("plugin/serialization_plugin");
        // 1) crypto_plugin：func=算法, inf=加密, 文本=输入文件路径
        try {
            uint8_t cryptoFun = zexuan::base::FunctionType::CRYPTO_AES; // 默认 aes（对称）
            if (encryptType == "1") cryptoFun = zexuan::base::FunctionType::CRYPTO_RSA;   // 1 非对称 -> rsa
            else if (encryptType == "2") cryptoFun = zexuan::base::FunctionType::CRYPTO_AES; // 2 对称 -> aes
            else if (encryptType == "3") cryptoFun = zexuan::base::FunctionType::CRYPTO_RSA; // 3 非对称+对称（暂以 rsa 代表，若需组合流程请告知）

            zexuan::base::Message cryptoMsg;
            cryptoMsg.setCot(static_cast<uint8_t>(zexuan::base::CauseOfTransmission::ACTIVATION));
            cryptoMsg.setFun(cryptoFun); // 算法选择
            cryptoMsg.setInf(zexuan::base::InformationNumber::OPERATION_EXECUTE);      // 加密
            cryptoMsg.setTextContent("config/message"); // 输入原始文件/目录
            int rc1 = sendPluginMessageToTarget(cryptoMsg, "crypto_plugin");
            if (rc1 < 0) logger->error("Failed to send crypto message");
            else logger->info("Sent crypto message to crypto_plugin: fun={}, dir=config/message", (int)cryptoFun);
        } catch (const std::exception& e) {
            logger->error("Error sending crypto message: {}", e.what());
        }

        // 2) transfer_plugin：根据传输方式传输 config/message_crypto
        try {
            uint8_t transFun = zexuan::base::FunctionType::TRANSFER_SFTP; // 默认 sftp
            if (transType == "1") transFun = zexuan::base::FunctionType::TRANSFER_SFTP;   // 1=sftp
            else if (transType == "2") transFun = zexuan::base::FunctionType::TRANSFER_SCP; // 2=scp
            else if (transType == "3") transFun = zexuan::base::FunctionType::TRANSFER_SCP; // 暂以 scp 代替 java 中转

            zexuan::base::Message transMsg;
            transMsg.setCot(static_cast<uint8_t>(zexuan::base::CauseOfTransmission::ACTIVATION));
            transMsg.setFun(transFun);
            transMsg.setTextContent("config/message_crypto");
            int rc2 = sendPluginMessageToTarget(transMsg, "transfer_plugin");
            if (rc2 < 0) logger->error("Failed to send transfer message");
            else logger->info("Sent transfer message to transfer_plugin: fun={}, file=config/message_crypto", (int)transFun);
        } catch (const std::exception& e) {
            logger->error("Error sending transfer message: {}", e.what());
        }
    }
};

ZEXUAN_PLUGIN_EXPORT(SerializationPlugin)


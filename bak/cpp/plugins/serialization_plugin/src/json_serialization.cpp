#include "../include/json_serialization.hpp"
#include "../include/serialization_base.hpp"
#include <nlohmann/json.hpp>
#include <fstream>

namespace serialization {

bool JsonSerialization::parseFile(const std::string& filePath, int /*op*/, ParseResult& out, std::string& err) {
    out = ParseResult{}; out.format = "json";
    try {
        std::ifstream ifs(filePath, std::ios::binary);
        if (!ifs.is_open()) { err = "open file failed"; return false; }
        std::string content((std::istreambuf_iterator<char>(ifs)), std::istreambuf_iterator<char>());
        auto j = nlohmann::json::parse(content);
        // 扁平化为顶层 key -> 文本化 value
        for (auto it = j.begin(); it != j.end(); ++it) {
            out.items.emplace_back(it.key(), it->dump());
        }
        return true;
    } catch (const std::exception& e) { err = e.what(); return false; }
}

} // namespace serialization

#include "../include/serialization_factory.hpp"
#include "../include/json_serialization.hpp"
#include "../include/xml_serialization.hpp"

namespace serialization {

void SerializationRegistrar::registerAll() {
    ZEXUAN_REGISTER_AS(SerializationFactory, JsonSerialization, "json");
    ZEXUAN_REGISTER_AS(SerializationFactory, XmlSerialization, "xml");
}

std::vector<std::string> SerializationRegistrar::getRegistered() {
    return SerializationFactory::getRegisteredNames();
}

bool SerializationRegistrar::isRegistered(const std::string& name) {
    return SerializationFactory::isRegistered(name);
}

} // namespace serialization


add_library(serialization_plugin SHARED
    src/serialization_plugin.cpp
    src/serialization_factory.cpp
    src/json_serialization.cpp
    src/xml_serialization.cpp
)

# 头文件放入接口目录供插件内部使用

target_link_libraries(serialization_plugin
    PRIVATE
        plugin_interface
        nlohmann_json::nlohmann_json
        tinyxml2::tinyxml2
        yaml-cpp::yaml-cpp
        tomlplusplus::tomlplusplus
        inih::inih
)

target_include_directories(serialization_plugin PRIVATE
    ${CMAKE_SOURCE_DIR}/core/include
    ${CMAKE_SOURCE_DIR}/interface/include
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

set_target_properties(serialization_plugin PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/libs/plugins"
    VERSION 1.0.0
    SOVERSION 1
)


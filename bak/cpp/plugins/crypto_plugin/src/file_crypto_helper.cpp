#include "../include/file_crypto_helper.hpp"
#include "zexuan/logger.hpp"
#include <fstream>
#include <filesystem>
#include <stdexcept>

namespace crypto {

bool FileCryptoHelper::encryptFile(const std::string& inputFilePath,
                                  const std::string& outputFilePath,
                                  std::shared_ptr<CryptoBase> crypto,
                                  const std::string& key) {
    auto logger = zexuan::Logger::getFileLogger("plugin/crypto_plugin");
    
    try {
        if (!fileExists(inputFilePath)) {
            logger->error("Input file does not exist: {}", inputFilePath);
            return false;
        }
        
        // 读取文件内容
        auto data = readFile(inputFilePath);
        logger->debug("Read {} bytes from {}", data.size(), inputFilePath);
        
        // 加密数据
        auto encryptedData = crypto->encrypt(data, key);
        logger->debug("Encrypted data size: {} bytes", encryptedData.size());
        
        // 写入加密后的文件
        bool success = writeFile(outputFilePath, encryptedData);
        if (success) {
            logger->info("Successfully encrypted file: {} -> {}", inputFilePath, outputFilePath);
        } else {
            logger->error("Failed to write encrypted file: {}", outputFilePath);
        }
        
        return success;
        
    } catch (const std::exception& e) {
        logger->error("Error encrypting file {}: {}", inputFilePath, e.what());
        return false;
    }
}

bool FileCryptoHelper::decryptFile(const std::string& inputFilePath,
                                  const std::string& outputFilePath,
                                  std::shared_ptr<CryptoBase> crypto,
                                  const std::string& key) {
    auto logger = zexuan::Logger::getFileLogger("plugin/crypto_plugin");
    
    try {
        if (!fileExists(inputFilePath)) {
            logger->error("Input file does not exist: {}", inputFilePath);
            return false;
        }
        
        // 读取加密文件内容
        auto encryptedData = readFile(inputFilePath);
        logger->debug("Read {} bytes from {}", encryptedData.size(), inputFilePath);
        
        // 解密数据
        auto decryptedData = crypto->decrypt(encryptedData, key);
        logger->debug("Decrypted data size: {} bytes", decryptedData.size());
        
        // 写入解密后的文件
        bool success = writeFile(outputFilePath, decryptedData);
        if (success) {
            logger->info("Successfully decrypted file: {} -> {}", inputFilePath, outputFilePath);
        } else {
            logger->error("Failed to write decrypted file: {}", outputFilePath);
        }
        
        return success;
        
    } catch (const std::exception& e) {
        logger->error("Error decrypting file {}: {}", inputFilePath, e.what());
        return false;
    }
}

std::vector<uint8_t> FileCryptoHelper::readFile(const std::string& filePath) {
    std::ifstream file(filePath, std::ios::binary | std::ios::ate);
    if (!file.is_open()) {
        throw std::runtime_error("Cannot open file: " + filePath);
    }
    
    auto size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    std::vector<uint8_t> data(size);
    if (!file.read(reinterpret_cast<char*>(data.data()), size)) {
        throw std::runtime_error("Cannot read file: " + filePath);
    }
    
    return data;
}

bool FileCryptoHelper::writeFile(const std::string& filePath, const std::vector<uint8_t>& data) {
    try {
        // 确保目录存在
        std::filesystem::path path(filePath);
        std::filesystem::create_directories(path.parent_path());
        
        std::ofstream file(filePath, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        file.write(reinterpret_cast<const char*>(data.data()), data.size());
        return file.good();
        
    } catch (const std::exception&) {
        return false;
    }
}

bool FileCryptoHelper::fileExists(const std::string& filePath) {
    return std::filesystem::exists(filePath) && std::filesystem::is_regular_file(filePath);
}

size_t FileCryptoHelper::getFileSize(const std::string& filePath) {
    try {
        return std::filesystem::file_size(filePath);
    } catch (const std::filesystem::filesystem_error&) {
        return 0;
    }
}

} // namespace crypto
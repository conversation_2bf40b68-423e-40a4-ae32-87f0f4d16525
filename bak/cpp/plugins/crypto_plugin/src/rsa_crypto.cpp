#include "../include/rsa_crypto.hpp"
#include <openssl/err.h>
#include <openssl/bio.h>
#include <iomanip>
#include <sstream>
#include <cstring>

namespace crypto {

RSACrypto::RSACrypto() {
    // RSA初始化
}

RSACrypto::~RSACrypto() = default;

std::vector<uint8_t> RSACrypto::encrypt(const std::vector<uint8_t>& data, const std::string& key) {
    // 对于RSA，需要确定传入的是公钥还是私钥
    // 如果是私钥，需要提取公钥进行加密
    EVP_PKEY* pkey = loadPrivateKeyFromString(key);
    if (pkey) {
        // 传入的是私钥，从私钥中提取公钥
        std::string publicKey = extractPublicKeyFromPrivateKey(pkey);
        EVP_PKEY_free(pkey);
        return encryptWithPublicKey(data, publicKey);
    } else {
        // 尝试作为公钥加载
        return encryptWithPublicKey(data, key);
    }
}

std::vector<uint8_t> RSACrypto::decrypt(const std::vector<uint8_t>& data, const std::string& key) {
    // 对于RSA，默认使用私钥解密
    return decryptWithPrivateKey(data, key);
}

std::string RSACrypto::generateKey() {
    // 生成密钥对并返回私钥
    auto keyPair = generateKeyPair();
    return keyPair.second; // 返回私钥
}

bool RSACrypto::validateKey(const std::string& key) const {
    try {
        // 尝试加载密钥来验证格式
        EVP_PKEY* pkey = loadPrivateKeyFromString(key);
        if (!pkey) {
            pkey = loadPublicKeyFromString(key);
        }
        if (pkey) {
            EVP_PKEY_free(pkey);
            return true;
        }
    } catch (...) {
        // 忽略异常
    }
    return false;
}

std::pair<std::string, std::string> RSACrypto::generateKeyPair() {
    EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA, nullptr);
    if (!ctx) {
        throw KeyGenerationException("创建RSA上下文失败");
    }
    
    if (EVP_PKEY_keygen_init(ctx) <= 0) {
        EVP_PKEY_CTX_free(ctx);
        throw KeyGenerationException("初始化RSA密钥生成失败");
    }
    
    if (EVP_PKEY_CTX_set_rsa_keygen_bits(ctx, KEY_SIZE) <= 0) {
        EVP_PKEY_CTX_free(ctx);
        throw KeyGenerationException("设置RSA密钥长度失败");
    }
    
    EVP_PKEY* pkey = nullptr;
    if (EVP_PKEY_keygen(ctx, &pkey) <= 0) {
        EVP_PKEY_CTX_free(ctx);
        throw KeyGenerationException("生成RSA密钥对失败");
    }
    
    EVP_PKEY_CTX_free(ctx);
    
    std::string publicKey = keyToString(pkey, false);
    std::string privateKey = keyToString(pkey, true);
    
    EVP_PKEY_free(pkey);
    
    return std::make_pair(publicKey, privateKey);
}

std::vector<uint8_t> RSACrypto::encryptWithPublicKey(const std::vector<uint8_t>& data, 
                                                    const std::string& publicKey) {
    EVP_PKEY* pkey = loadPublicKeyFromString(publicKey);
    if (!pkey) {
        throw EncryptionException("加载公钥失败");
    }
    
    EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new(pkey, nullptr);
    if (!ctx) {
        EVP_PKEY_free(pkey);
        throw EncryptionException("创建加密上下文失败");
    }
    
    if (EVP_PKEY_encrypt_init(ctx) <= 0) {
        EVP_PKEY_CTX_free(ctx);
        EVP_PKEY_free(pkey);
        throw EncryptionException("初始化加密失败");
    }
    
    size_t outlen;
    if (EVP_PKEY_encrypt(ctx, nullptr, &outlen, data.data(), data.size()) <= 0) {
        EVP_PKEY_CTX_free(ctx);
        EVP_PKEY_free(pkey);
        throw EncryptionException("计算加密输出大小失败");
    }
    
    std::vector<uint8_t> encrypted(outlen);
    if (EVP_PKEY_encrypt(ctx, encrypted.data(), &outlen, data.data(), data.size()) <= 0) {
        EVP_PKEY_CTX_free(ctx);
        EVP_PKEY_free(pkey);
        throw EncryptionException("RSA加密失败");
    }
    
    encrypted.resize(outlen);
    
    EVP_PKEY_CTX_free(ctx);
    EVP_PKEY_free(pkey);
    
    return encrypted;
}

std::vector<uint8_t> RSACrypto::decryptWithPrivateKey(const std::vector<uint8_t>& data, 
                                                     const std::string& privateKey) {
    EVP_PKEY* pkey = loadPrivateKeyFromString(privateKey);
    if (!pkey) {
        throw DecryptionException("加载私钥失败");
    }
    
    EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new(pkey, nullptr);
    if (!ctx) {
        EVP_PKEY_free(pkey);
        throw DecryptionException("创建解密上下文失败");
    }
    
    if (EVP_PKEY_decrypt_init(ctx) <= 0) {
        EVP_PKEY_CTX_free(ctx);
        EVP_PKEY_free(pkey);
        throw DecryptionException("初始化解密失败");
    }
    
    size_t outlen;
    if (EVP_PKEY_decrypt(ctx, nullptr, &outlen, data.data(), data.size()) <= 0) {
        EVP_PKEY_CTX_free(ctx);
        EVP_PKEY_free(pkey);
        throw DecryptionException("计算解密输出大小失败");
    }
    
    std::vector<uint8_t> decrypted(outlen);
    if (EVP_PKEY_decrypt(ctx, decrypted.data(), &outlen, data.data(), data.size()) <= 0) {
        EVP_PKEY_CTX_free(ctx);
        EVP_PKEY_free(pkey);
        throw DecryptionException("RSA解密失败");
    }
    
    decrypted.resize(outlen);
    
    EVP_PKEY_CTX_free(ctx);
    EVP_PKEY_free(pkey);
    
    return decrypted;
}

std::vector<uint8_t> RSACrypto::sign(const std::vector<uint8_t>& data, 
                                    const std::string& privateKey) {
    EVP_PKEY* pkey = loadPrivateKeyFromString(privateKey);
    if (!pkey) {
        throw EncryptionException("加载私钥失败");
    }
    
    EVP_MD_CTX* mdctx = EVP_MD_CTX_new();
    if (!mdctx) {
        EVP_PKEY_free(pkey);
        throw EncryptionException("创建签名上下文失败");
    }
    
    if (EVP_DigestSignInit(mdctx, nullptr, EVP_sha256(), nullptr, pkey) <= 0) {
        EVP_MD_CTX_free(mdctx);
        EVP_PKEY_free(pkey);
        throw EncryptionException("初始化签名失败");
    }
    
    if (EVP_DigestSignUpdate(mdctx, data.data(), data.size()) <= 0) {
        EVP_MD_CTX_free(mdctx);
        EVP_PKEY_free(pkey);
        throw EncryptionException("更新签名数据失败");
    }
    
    size_t siglen;
    if (EVP_DigestSignFinal(mdctx, nullptr, &siglen) <= 0) {
        EVP_MD_CTX_free(mdctx);
        EVP_PKEY_free(pkey);
        throw EncryptionException("计算签名大小失败");
    }
    
    std::vector<uint8_t> signature(siglen);
    if (EVP_DigestSignFinal(mdctx, signature.data(), &siglen) <= 0) {
        EVP_MD_CTX_free(mdctx);
        EVP_PKEY_free(pkey);
        throw EncryptionException("生成签名失败");
    }
    
    signature.resize(siglen);
    
    EVP_MD_CTX_free(mdctx);
    EVP_PKEY_free(pkey);
    
    return signature;
}

bool RSACrypto::verify(const std::vector<uint8_t>& data, 
                      const std::vector<uint8_t>& signature, 
                      const std::string& publicKey) {
    EVP_PKEY* pkey = loadPublicKeyFromString(publicKey);
    if (!pkey) {
        return false;
    }
    
    EVP_MD_CTX* mdctx = EVP_MD_CTX_new();
    if (!mdctx) {
        EVP_PKEY_free(pkey);
        return false;
    }
    
    if (EVP_DigestVerifyInit(mdctx, nullptr, EVP_sha256(), nullptr, pkey) <= 0) {
        EVP_MD_CTX_free(mdctx);
        EVP_PKEY_free(pkey);
        return false;
    }
    
    if (EVP_DigestVerifyUpdate(mdctx, data.data(), data.size()) <= 0) {
        EVP_MD_CTX_free(mdctx);
        EVP_PKEY_free(pkey);
        return false;
    }
    
    int result = EVP_DigestVerifyFinal(mdctx, signature.data(), signature.size());
    
    EVP_MD_CTX_free(mdctx);
    EVP_PKEY_free(pkey);
    
    return result == 1;
}

// 私有辅助方法实现
EVP_PKEY* RSACrypto::loadPublicKeyFromString(const std::string& publicKeyStr) const {
    BIO* bio = BIO_new_mem_buf(publicKeyStr.c_str(), -1);
    if (!bio) {
        return nullptr;
    }
    
    EVP_PKEY* pkey = PEM_read_bio_PUBKEY(bio, nullptr, nullptr, nullptr);
    BIO_free(bio);
    
    return pkey;
}

EVP_PKEY* RSACrypto::loadPrivateKeyFromString(const std::string& privateKeyStr) const {
    BIO* bio = BIO_new_mem_buf(privateKeyStr.c_str(), -1);
    if (!bio) {
        return nullptr;
    }
    
    EVP_PKEY* pkey = PEM_read_bio_PrivateKey(bio, nullptr, nullptr, nullptr);
    BIO_free(bio);
    
    return pkey;
}

std::string RSACrypto::keyToString(EVP_PKEY* pkey, bool isPrivate) {
    BIO* bio = BIO_new(BIO_s_mem());
    if (!bio) {
        throw KeyGenerationException("创建内存BIO失败");
    }
    
    int result;
    if (isPrivate) {
        result = PEM_write_bio_PrivateKey(bio, pkey, nullptr, nullptr, 0, nullptr, nullptr);
    } else {
        result = PEM_write_bio_PUBKEY(bio, pkey);
    }
    
    if (result != 1) {
        BIO_free(bio);
        throw KeyGenerationException("写入密钥到BIO失败");
    }
    
    char* keyData;
    long keyLen = BIO_get_mem_data(bio, &keyData);
    
    std::string keyString(keyData, keyLen);
    BIO_free(bio);
    
    return keyString;
}

std::string RSACrypto::extractPublicKeyFromPrivateKey(EVP_PKEY* privateKey) {
    if (!privateKey) {
        throw EncryptionException("私钥为空，无法提取公钥");
    }
    
    // 从私钥中提取公钥
    return keyToString(privateKey, false);
}

std::vector<uint8_t> RSACrypto::hexStringToBytes(const std::string& hex) {
    std::vector<uint8_t> bytes;
    bytes.reserve(hex.length() / 2);
    
    for (size_t i = 0; i < hex.length(); i += 2) {
        std::string byteString = hex.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoul(byteString, nullptr, 16));
        bytes.push_back(byte);
    }
    
    return bytes;
}

std::string RSACrypto::bytesToHexString(const std::vector<uint8_t>& bytes) {
    std::ostringstream oss;
    oss << std::hex << std::setfill('0');
    
    for (uint8_t byte : bytes) {
        oss << std::setw(2) << static_cast<unsigned int>(byte);
    }
    
    return oss.str();
}

} // namespace crypto
#include "../include/base64_crypto.hpp"
#include <openssl/err.h>
#include <cstring>

namespace crypto {

Base64Crypto::Base64Crypto() {
    // Base64初始化
}

Base64Crypto::~Base64Crypto() = default;

std::string Base64Crypto::encode(const std::vector<uint8_t>& data) {
    return base64Encode(data.data(), data.size());
}

std::vector<uint8_t> Base64Crypto::decode(const std::string& encodedData) {
    return base64Decode(encodedData);
}

std::string Base64Crypto::base64Encode(const uint8_t* data, size_t length) {
    BIO* bio = BIO_new(BIO_s_mem());
    BIO* b64 = BIO_new(BIO_f_base64());
    
    if (!bio || !b64) {
        if (bio) BIO_free(bio);
        if (b64) BIO_free(b64);
        throw EncryptionException("创建Base64 BIO失败");
    }
    
    // 不要换行
    BIO_set_flags(b64, BIO_FLAGS_BASE64_NO_NL);
    
    bio = BIO_push(b64, bio);
    
    if (BIO_write(bio, data, length) <= 0) {
        BIO_free_all(bio);
        throw EncryptionException("Base64编码写入失败");
    }
    
    if (BIO_flush(bio) <= 0) {
        BIO_free_all(bio);
        throw EncryptionException("Base64编码刷新失败");
    }
    
    char* encodedData;
    long encodedLength = BIO_get_mem_data(bio, &encodedData);
    
    std::string result(encodedData, encodedLength);
    BIO_free_all(bio);
    
    return result;
}

std::vector<uint8_t> Base64Crypto::base64Decode(const std::string& encoded) {
    BIO* bio = BIO_new_mem_buf(encoded.c_str(), -1);
    BIO* b64 = BIO_new(BIO_f_base64());
    
    if (!bio || !b64) {
        if (bio) BIO_free(bio);
        if (b64) BIO_free(b64);
        throw DecryptionException("创建Base64解码BIO失败");
    }
    
    // 不要换行
    BIO_set_flags(b64, BIO_FLAGS_BASE64_NO_NL);
    
    bio = BIO_push(b64, bio);
    
    // 计算最大可能的解码长度
    size_t maxDecodedLength = (encoded.length() * 3) / 4 + 1;
    std::vector<uint8_t> decoded(maxDecodedLength);
    
    int decodedLength = BIO_read(bio, decoded.data(), maxDecodedLength);
    
    if (decodedLength < 0) {
        BIO_free_all(bio);
        throw DecryptionException("Base64解码失败");
    }
    
    decoded.resize(decodedLength);
    BIO_free_all(bio);
    
    return decoded;
}

} // namespace crypto
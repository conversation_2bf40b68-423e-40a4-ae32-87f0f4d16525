#include "../include/crypto_factory.hpp"
#include "../include/aes_crypto.hpp"
#include "../include/rsa_crypto.hpp"
#include "../include/sha256_crypto.hpp"
#include "../include/base64_crypto.hpp"
#include "../include/hex_crypto.hpp"

namespace crypto {

void CryptoRegistrar::registerAllAlgorithms() {
    // 注册所有加密算法，使用小写名称
    ZEXUAN_REGISTER_AS(CryptoFactory, AESCrypto, "aes");
    ZEXUAN_REGISTER_AS(CryptoFactory, RSACrypto, "rsa");
    ZEXUAN_REGISTER_AS(CryptoFactory, SHA256Crypto, "sha256");
    ZEXUAN_REGISTER_AS(CryptoFactory, Base64Crypto, "base64");
    ZEXUAN_REGISTER_AS(CryptoFactory, HexCrypto, "hex");
}

std::vector<std::string> CryptoRegistrar::getRegisteredAlgorithms() {
    return CryptoFactory::getRegisteredNames();
}

bool CryptoRegistrar::isAlgorithmRegistered(const std::string& algorithm) {
    return CryptoFactory::isRegistered(algorithm);
}

} // namespace crypto
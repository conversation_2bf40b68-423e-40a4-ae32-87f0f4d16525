#include "../include/aes_crypto.hpp"
#include <openssl/err.h>
#include <iomanip>
#include <sstream>
#include <cstring>

namespace crypto {

AESCrypto::AESCrypto() : cipher_(EVP_aes_256_gcm()) {
    if (!cipher_) {
        throw OpenSSLException("EVP_aes_256_gcm");
    }
    generateRandomIV();
}

AESCrypto::~AESCrypto() = default;

std::vector<uint8_t> AESCrypto::encrypt(const std::vector<uint8_t>& data, const std::string& key) {
    if (key.empty()) {
        throw InvalidKeyException("密钥不能为空");
    }
    
    // 验证密钥
    if (!validateKey(key)) {
        throw InvalidKeyException("密钥格式无效");
    }
    
    // 将十六进制密钥转换为字节
    auto keyBytes = hexStringToBytes(key);
    
    // 生成新的IV
    generateRandomIV();
    
    EVPCipherContext ctx;
    
    // 初始化加密
    if (EVP_EncryptInit_ex(ctx.get(), cipher_, nullptr, keyBytes.data(), iv_.data()) != 1) {
        throw EncryptionException("初始化加密失败");
    }
    
    std::vector<uint8_t> ciphertext;
    ciphertext.resize(data.size() + EVP_CIPHER_block_size(cipher_));
    
    int len = 0;
    int ciphertext_len = 0;
    
    // 执行加密
    if (EVP_EncryptUpdate(ctx.get(), ciphertext.data(), &len, data.data(), data.size()) != 1) {
        throw EncryptionException("加密数据失败");
    }
    ciphertext_len = len;
    
    // 完成加密
    if (EVP_EncryptFinal_ex(ctx.get(), ciphertext.data() + len, &len) != 1) {
        throw EncryptionException("完成加密失败");
    }
    ciphertext_len += len;
    
    // 获取认证标签
    std::vector<uint8_t> tag(16);
    if (EVP_CIPHER_CTX_ctrl(ctx.get(), EVP_CTRL_GCM_GET_TAG, 16, tag.data()) != 1) {
        throw EncryptionException("获取认证标签失败");
    }
    
    // 调整密文大小
    ciphertext.resize(ciphertext_len);
    
    // 构造最终结果：IV(12) + 密文 + TAG(16)
    std::vector<uint8_t> result;
    result.reserve(iv_.size() + ciphertext.size() + tag.size());
    result.insert(result.end(), iv_.begin(), iv_.end());
    result.insert(result.end(), ciphertext.begin(), ciphertext.end());
    result.insert(result.end(), tag.begin(), tag.end());
    
    return result;
}

std::vector<uint8_t> AESCrypto::decrypt(const std::vector<uint8_t>& data, const std::string& key) {
    if (key.empty()) {
        throw InvalidKeyException("密钥不能为空");
    }
    
    if (!validateKey(key)) {
        throw InvalidKeyException("密钥格式无效");
    }
    
    // 检查数据长度（至少需要IV(12) + TAG(16) = 28字节）
    if (data.size() < 28) {
        throw DecryptionException("数据长度不足");
    }
    
    // 提取IV、密文和TAG
    std::vector<uint8_t> iv(data.begin(), data.begin() + 12);
    std::vector<uint8_t> tag(data.end() - 16, data.end());
    std::vector<uint8_t> ciphertext(data.begin() + 12, data.end() - 16);
    
    // 将十六进制密钥转换为字节
    auto keyBytes = hexStringToBytes(key);
    
    EVPCipherContext ctx;
    
    // 初始化解密
    if (EVP_DecryptInit_ex(ctx.get(), cipher_, nullptr, keyBytes.data(), iv.data()) != 1) {
        throw DecryptionException("初始化解密失败");
    }
    
    std::vector<uint8_t> plaintext;
    plaintext.resize(ciphertext.size());
    
    int len = 0;
    int plaintext_len = 0;
    
    // 执行解密
    if (EVP_DecryptUpdate(ctx.get(), plaintext.data(), &len, ciphertext.data(), ciphertext.size()) != 1) {
        throw DecryptionException("解密数据失败");
    }
    plaintext_len = len;
    
    // 设置认证标签
    if (EVP_CIPHER_CTX_ctrl(ctx.get(), EVP_CTRL_GCM_SET_TAG, 16, tag.data()) != 1) {
        throw DecryptionException("设置认证标签失败");
    }
    
    // 完成解密并验证标签
    if (EVP_DecryptFinal_ex(ctx.get(), plaintext.data() + len, &len) != 1) {
        throw DecryptionException("解密失败或认证标签验证失败");
    }
    plaintext_len += len;
    
    // 调整明文大小
    plaintext.resize(plaintext_len);
    
    return plaintext;
}

std::string AESCrypto::generateKey() {
    std::vector<uint8_t> key(32); // 256位密钥
    
    if (RAND_bytes(key.data(), key.size()) != 1) {
        throw KeyGenerationException("生成随机密钥失败");
    }
    
    return bytesToHexString(key);
}

bool AESCrypto::validateKey(const std::string& key) const {
    // AES-256密钥应该是64个十六进制字符（32字节 * 2）
    if (key.length() != 64) {
        return false;
    }
    
    // 检查是否都是十六进制字符
    for (char c : key) {
        if (!std::isxdigit(c)) {
            return false;
        }
    }
    
    return true;
}

void AESCrypto::setInitializationVector(const std::vector<uint8_t>& iv) {
    if (iv.size() != 12) { // GCM模式推荐12字节IV
        throw InvalidKeyException("IV长度必须为12字节");
    }
    iv_ = iv;
}

std::vector<uint8_t> AESCrypto::getInitializationVector() const {
    return iv_;
}

std::vector<uint8_t> AESCrypto::hexStringToBytes(const std::string& hex) {
    std::vector<uint8_t> bytes;
    bytes.reserve(hex.length() / 2);
    
    for (size_t i = 0; i < hex.length(); i += 2) {
        std::string byteString = hex.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoul(byteString, nullptr, 16));
        bytes.push_back(byte);
    }
    
    return bytes;
}

std::string AESCrypto::bytesToHexString(const std::vector<uint8_t>& bytes) {
    std::ostringstream oss;
    oss << std::hex << std::setfill('0');
    
    for (uint8_t byte : bytes) {
        oss << std::setw(2) << static_cast<unsigned int>(byte);
    }
    
    return oss.str();
}

void AESCrypto::generateRandomIV() {
    iv_.resize(12); // GCM模式推荐12字节IV
    
    if (RAND_bytes(iv_.data(), iv_.size()) != 1) {
        throw KeyGenerationException("生成随机IV失败");
    }
}

} // namespace crypto
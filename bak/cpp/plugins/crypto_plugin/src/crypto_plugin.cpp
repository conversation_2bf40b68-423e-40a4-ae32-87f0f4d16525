#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/plugin/plugin_export.hpp"
#include "zexuan/base/mediator.hpp"
#include "../include/message_crypto.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/base/message_types.hpp"
#include "zexuan/logger.hpp"
#include "../include/crypto_manager.hpp"
#include "../include/file_crypto_helper.hpp"
#include <filesystem>
#include <vector>
#include <iostream>
#include <mutex>
#include <fstream>

namespace fs = std::filesystem;
using namespace zexuan;

/**
 * Crypto Plugin - 加密解密插件
 * 功能：使用多种加密算法对文件进行加密和解密
 * 支持的算法：AES, RSA, SHA256, Base64等
 */
class CryptoPlugin : public zexuan::plugin::PluginBase {
private:
    std::unique_ptr<crypto::CryptoManager> cryptoManager_;
    std::mutex operationMutex_;

public:
    CryptoPlugin(std::shared_ptr<zexuan::base::Mediator> mediator, int pluginId, const std::string& name)
        : PluginBase(mediator, pluginId, name),
          cryptoManager_(std::make_unique<crypto::CryptoManager>()) {  
    }

    bool initialize() override {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        logger->info("CryptoPlugin {} initializing...", getPluginId());
        
        try {
            // 获取支持的算法列表
            auto algorithms = cryptoManager_->getSupportedAlgorithms();
            logger->info("Loaded {} crypto algorithms", algorithms.size());
            
            return true;
        } catch (const std::exception& e) {
            logger->error("Failed to initialize crypto plugin: {}", e.what());
            return false;
        }
    }

    void shutdown() override {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        logger->info("CryptoPlugin {} shutting down...", getPluginId());
        
        // 清理缓存
        if (cryptoManager_) {
            cryptoManager_->clearCache();
        }
        
        logger->info("CryptoPlugin shutdown complete");
    }

protected:
    void processMessage(const zexuan::base::Message& message) override {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        logger->debug("CryptoPlugin {} received message: TYP={}, COT={}, Source={}, Target={}",
                     getPluginId(),
                     static_cast<int>(message.getTyp()),
                     static_cast<int>(message.getCot()),
                     static_cast<int>(message.getSource()),
                     static_cast<int>(message.getTarget()));

        // 处理插件信息请求
        if (message.getCot() == static_cast<uint8_t>(zexuan::base::CauseOfTransmission::INFO_REQUEST)) {
            logger->info("Plugin Name: {}, ID: {}, Description: 加密解密插件 - 支持AES/RSA/SHA256/Base64等算法", 
                        getPluginName(), getPluginId());
            
            // 创建回复消息，交换source和target
            zexuan::base::Message responseMessage = message;
            responseMessage.swapSourceTarget();
            responseMessage.setCot(static_cast<uint8_t>(zexuan::base::CauseOfTransmission::INFO_RESPONSE));
            
            // 设置JSON响应内容
            std::string jsonResponse = zexuan::plugin::crypto::getAvailableMessagesJson();
            responseMessage.setTextContent(jsonResponse);
            
            // 发送回复给服务器
            try {
                int result = sendPluginMessageToTarget(responseMessage, "server");
                if (result == 0) {
                    logger->info("Successfully sent INFO_RESPONSE to server");
                } else {
                    logger->error("Failed to send INFO_RESPONSE to server: error code {}", result);
                }
            } catch (const std::exception& e) {
                logger->error("Error sending INFO_RESPONSE to server: {}", e.what());
            }
            return;
        }

        // 处理加密/解密消息
        if (message.getCot() != static_cast<uint8_t>(zexuan::base::CauseOfTransmission::ACTIVATION)) {
            return;
        }
        uint8_t fun = message.getFun();
        uint8_t inf = message.getInf();
        std::string directory = message.getTextContent();
        if (directory.empty()) {
            logger->error("No directory specified in message text content");
            return;
        }
        std::string algorithm = algorithmFromFun(fun);
        if (!cryptoManager_->isAlgorithmSupported(algorithm)) {
            logger->error("Unsupported algorithm mapped from func={}: {}", (int)fun, algorithm);
            return;
        }
        if (inf == zexuan::base::InformationNumber::OPERATION_EXECUTE) { // 加密
            handleEncryptionMessage(algorithm, directory);
        } else if (inf == zexuan::base::InformationNumber::OPERATION_REVERSE) { // 解密
            handleDecryptionMessage(algorithm, directory);
        } else {
            logger->warn("Unknown INF={} - expected {}(encrypt) or {}(decrypt)", 
                        (int)inf, 
                        (int)zexuan::base::InformationNumber::OPERATION_EXECUTE,
                        (int)zexuan::base::InformationNumber::OPERATION_REVERSE);
        }
    }

private:
    // === 核心功能方法 ===

    // 将 func 映射为算法名（使用message_types中的常量）
    std::string algorithmFromFun(uint8_t fun) {
        switch (fun) {
            case zexuan::base::FunctionType::CRYPTO_AES: return "aes";
            case zexuan::base::FunctionType::CRYPTO_RSA: return "rsa";
            case zexuan::base::FunctionType::CRYPTO_BASE64: return "base64";
            case zexuan::base::FunctionType::CRYPTO_SHA256: return "sha256";
            default: return "aes";
        }
    }

    /**
     * @brief 加密（新签名）
     */
    void handleEncryptionMessage(const std::string& algorithm, const std::string& directory) {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        try {
            logger->info("Encrypt dir={} with algorithm={}", directory, algorithm);
            performFileEncryption(algorithm, directory);
        } catch (const std::exception& e) {
            logger->error("Error in handleEncryptionMessage: {}", e.what());
        }
    }

    /**
     * @brief 处理文件解密消息
     * @param message 包含解密参数的消息
     */
    void handleDecryptionMessage(const std::string& algorithm, const std::string& directory) {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        try {
            logger->info("Decrypt dir={} with algorithm={}", directory, algorithm);
            performFileDecryption(algorithm, directory);
        } catch (const std::exception& e) {
            logger->error("Error in handleDecryptionMessage: {}", e.what());
        }
    }

    // === 核心加密解密方法 ===
    
    /**
     * @brief 执行文件加密操作
     * @param algorithm 加密算法名称
     */
    void performFileEncryption(const std::string& algorithm, const std::string& directory) {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        std::lock_guard<std::mutex> lock(operationMutex_);
        try {
            auto crypto = cryptoManager_->createCrypto(algorithm);
            if (!crypto) { logger->error("Failed to create crypto for {}", algorithm); return; }
            std::string key = crypto->generateKey();
            logger->info("Generated key for {}", algorithm);
            std::string inputFile = (directory.empty()? std::string("config/message") : directory);
            std::string outputFile = inputFile + "_crypto";
            if (!crypto::FileCryptoHelper::fileExists(inputFile)) { logger->error("Input not exist: {}", inputFile); return; }
            bool success = crypto::FileCryptoHelper::encryptFile(inputFile, outputFile, crypto, key);
            if (success) { logger->info("Encrypted {} -> {} using {}", inputFile, outputFile, algorithm); saveEncryptionKey(algorithm, key); }
            else { logger->error("Encrypt failed for {}", algorithm); }
        } catch (const crypto::AlgorithmNotFoundException& e) { logger->error("Algorithm not found: {}", e.what()); }
        catch (const std::exception& e) { logger->error("Encryption error: {}", e.what()); }
    }

    /**
     * @brief 执行文件解密操作
     * @param algorithm 解密算法名称
     */
    void performFileDecryption(const std::string& algorithm, const std::string& directory) {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        std::lock_guard<std::mutex> lock(operationMutex_);
        try {
            auto crypto = cryptoManager_->createCrypto(algorithm);
            if (!crypto) { logger->error("Failed to create crypto for {}", algorithm); return; }
            std::string key = loadEncryptionKey(algorithm);
            if (crypto->supportsKeyGeneration() && key.empty()) { logger->error("No key for {}", algorithm); return; }
            std::string inputFile = (directory.empty()? std::string("config/message_crypto") : directory);
            std::string outputFile = inputFile + "_uncrypto";
            if (!crypto::FileCryptoHelper::fileExists(inputFile)) { logger->error("Encrypted file not exist: {}", inputFile); return; }
            bool success = crypto::FileCryptoHelper::decryptFile(inputFile, outputFile, crypto, key);
            if (success) { logger->info("Decrypted {} -> {} using {}", inputFile, outputFile, algorithm); }
            else { logger->error("Decrypt failed for {}", algorithm); }
        } catch (const crypto::AlgorithmNotFoundException& e) { logger->error("Algorithm not found: {}", e.what()); }
        catch (const std::exception& e) { logger->error("Decryption error: {}", e.what()); }
    }

    // === 密钥管理方法 ===
    
    /**
     * @brief 保存加密密钥
     * @param algorithm 算法名称
     * @param key 加密密钥
     */
    void saveEncryptionKey(const std::string& algorithm, const std::string& key) {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        
        try {
            // 对于不需要密钥的算法（如编码和哈希算法），跳过密钥保存
            if (key.empty()) {
                logger->info("Skipping key save for algorithm {} (no key required)", algorithm);
                return;
            }
            
            // 创建密钥文件路径
            std::string keyFile = "config/keys/" + algorithm + ".key";
            
            // 确保目录存在
            fs::create_directories(fs::path(keyFile).parent_path());
            
            // 将密钥写入文件
            std::ofstream file(keyFile);
            if (file.is_open()) {
                file << key;
                file.close();
                logger->info("Saved encryption key for algorithm: {}", algorithm);
            } else {
                logger->error("Failed to save key file: {}", keyFile);
            }
            
        } catch (const std::exception& e) {
            logger->error("Error saving encryption key: {}", e.what());
        }
    }
    
    /**
     * @brief 加载加密密钥
     * @param algorithm 算法名称
     * @return 加密密钥，如果不存在返回空字符串
     */
    std::string loadEncryptionKey(const std::string& algorithm) {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        
        try {
            // 检查算法是否需要密钥
            auto crypto = cryptoManager_->createCrypto(algorithm);
            if (crypto && !crypto->supportsKeyGeneration()) {
                logger->info("Algorithm {} does not require key, returning empty string", algorithm);
                return "";
            }
            
            std::string keyFile = "config/keys/" + algorithm + ".key";
            
            if (!fs::exists(keyFile)) {
                logger->warn("Key file does not exist: {}", keyFile);
                return "";
            }
            
            std::ifstream file(keyFile);
            if (file.is_open()) {
                // 读取整个文件内容（用于支持多行密钥如RSA PEM格式）
                std::string key;
                std::string line;
                bool firstLine = true;
                while (std::getline(file, line)) {
                    if (!firstLine) {
                        key += "\n";
                    }
                    key += line;
                    firstLine = false;
                }
                file.close();
                logger->info("Loaded encryption key for algorithm: {}", algorithm);
                return key;
            } else {
                logger->error("Failed to open key file: {}", keyFile);
                return "";
            }
            
        } catch (const std::exception& e) {
            logger->error("Error loading encryption key: {}", e.what());
            return "";
        }
    }
};

// === 插件导出（使用标准化宏） ===
ZEXUAN_PLUGIN_EXPORT(CryptoPlugin)

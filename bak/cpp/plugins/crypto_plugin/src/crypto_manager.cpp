#include "../include/crypto_manager.hpp"
#include "zexuan/logger.hpp"

namespace crypto {

CryptoManager::CryptoManager() : cacheEnabled_(true) {
    initializeAlgorithms();
}

std::shared_ptr<CryptoBase> CryptoManager::createCrypto(const CryptoConfig& config) {
    return createCrypto(config.algorithm);
}

std::shared_ptr<CryptoBase> CryptoManager::createCrypto(const std::string& algorithm) {
    // 如果启用缓存，先尝试从缓存获取
    if (cacheEnabled_) {
        auto cached = getCachedCrypto(algorithm);
        if (cached) {
            return cached;
        }
    }
    
    // 从工厂创建新实例
    auto crypto = CryptoFactory::create(algorithm);
    if (!crypto) {
        throw AlgorithmNotFoundException(algorithm);
    }
    
    // 如果启用缓存，将新实例添加到缓存
    if (cacheEnabled_) {
        std::unique_lock<std::shared_mutex> lock(cacheMutex_);
        cache_[algorithm] = crypto;
    }
    
    auto logger = zexuan::Logger::getFileLogger("plugin/crypto_plugin");
    logger->debug("Created crypto instance for algorithm: {}", algorithm);
    
    return crypto;
}

std::shared_ptr<CryptoBase> CryptoManager::getCachedCrypto(const std::string& algorithm) {
    std::shared_lock<std::shared_mutex> lock(cacheMutex_);
    auto it = cache_.find(algorithm);
    return (it != cache_.end()) ? it->second : nullptr;
}

void CryptoManager::clearCache() {
    std::unique_lock<std::shared_mutex> lock(cacheMutex_);
    cache_.clear();
    
    auto logger = zexuan::Logger::getFileLogger("plugin/crypto_plugin");
    logger->info("Crypto cache cleared");
}

std::vector<std::string> CryptoManager::getSupportedAlgorithms() {
    return CryptoRegistrar::getRegisteredAlgorithms();
}

bool CryptoManager::isAlgorithmSupported(const std::string& algorithm) {
    return CryptoRegistrar::isAlgorithmRegistered(algorithm);
}

void CryptoManager::initializeAlgorithms() {
    // 注册所有算法
    CryptoRegistrar::registerAllAlgorithms();
    
    auto logger = zexuan::Logger::getFileLogger("plugin/crypto_plugin");
    auto algorithms = getSupportedAlgorithms();
    logger->info("Initialized {} crypto algorithms", algorithms.size());
    
    for (const auto& algo : algorithms) {
        logger->debug("Registered algorithm: {}", algo);
    }
}

} // namespace crypto
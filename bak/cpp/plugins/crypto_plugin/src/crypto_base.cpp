#include "../include/crypto_base.hpp"
#include <sstream>
#include <iomanip>

namespace crypto {

// CryptoBase默认实现
std::string CryptoBase::encryptString(const std::string& plaintext, const std::string& key) {
    std::vector<uint8_t> data(plaintext.begin(), plaintext.end());
    auto encrypted = encrypt(data, key);
    
    // 转换为十六进制字符串
    std::ostringstream oss;
    oss << std::hex << std::setfill('0');
    for (uint8_t byte : encrypted) {
        oss << std::setw(2) << static_cast<unsigned int>(byte);
    }
    
    return oss.str();
}

std::string CryptoBase::decryptString(const std::string& ciphertext, const std::string& key) {
    // 将十六进制字符串转换为字节
    std::vector<uint8_t> data;
    data.reserve(ciphertext.length() / 2);
    
    for (size_t i = 0; i < ciphertext.length(); i += 2) {
        std::string byteString = ciphertext.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(std::stoul(byteString, nullptr, 16));
        data.push_back(byte);
    }
    
    auto decrypted = decrypt(data, key);
    return std::string(decrypted.begin(), decrypted.end());
}

// EncodingCrypto默认实现
std::vector<uint8_t> EncodingCrypto::encrypt(const std::vector<uint8_t>& data, const std::string& key) {
    std::string encoded = encode(data);
    return std::vector<uint8_t>(encoded.begin(), encoded.end());
}

std::vector<uint8_t> EncodingCrypto::decrypt(const std::vector<uint8_t>& data, const std::string& key) {
    std::string encodedData(data.begin(), data.end());
    return decode(encodedData);
}

} // namespace crypto
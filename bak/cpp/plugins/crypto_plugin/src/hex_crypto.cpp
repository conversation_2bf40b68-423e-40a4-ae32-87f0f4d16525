#include "../include/hex_crypto.hpp"
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <cctype>

namespace crypto {

HexCrypto::HexCrypto() {
    // Hex初始化
}

HexCrypto::~HexCrypto() = default;

std::string HexCrypto::encode(const std::vector<uint8_t>& data) {
    std::ostringstream oss;
    oss << std::hex << std::setfill('0');
    
    for (uint8_t byte : data) {
        oss << std::setw(2) << static_cast<unsigned int>(byte);
    }
    
    return oss.str();
}

std::vector<uint8_t> HexCrypto::decode(const std::string& encodedData) {
    // 移除空格并转为小写
    std::string cleanHex;
    for (char c : encodedData) {
        if (c != ' ' && c != '\t' && c != '\n' && c != '\r') {
            cleanHex += std::tolower(c);
        }
    }
    
    // 检查长度是否为偶数
    if (cleanHex.length() % 2 != 0) {
        throw DecryptionException("Hex字符串长度必须为偶数");
    }
    
    // 检查是否都是有效的十六进制字符
    for (char c : cleanHex) {
        if (!isHexChar(c)) {
            throw DecryptionException("包含无效的十六进制字符: " + std::string(1, c));
        }
    }
    
    std::vector<uint8_t> decoded;
    decoded.reserve(cleanHex.length() / 2);
    
    for (size_t i = 0; i < cleanHex.length(); i += 2) {
        uint8_t highNibble = hexCharToValue(cleanHex[i]);
        uint8_t lowNibble = hexCharToValue(cleanHex[i + 1]);
        uint8_t byte = (highNibble << 4) | lowNibble;
        decoded.push_back(byte);
    }
    
    return decoded;
}

bool HexCrypto::isHexChar(char c) {
    return (c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F');
}

uint8_t HexCrypto::hexCharToValue(char c) {
    if (c >= '0' && c <= '9') {
        return c - '0';
    }
    if (c >= 'a' && c <= 'f') {
        return c - 'a' + 10;
    }
    if (c >= 'A' && c <= 'F') {
        return c - 'A' + 10;
    }
    throw DecryptionException("无效的十六进制字符");
}

} // namespace crypto
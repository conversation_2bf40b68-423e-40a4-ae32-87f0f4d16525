#include "../include/sha256_crypto.hpp"
#include <openssl/err.h>
#include <iomanip>
#include <sstream>

namespace crypto {

SHA256Crypto::SHA256Crypto() {
    // SHA256初始化
}

SHA256Crypto::~SHA256Crypto() = default;

std::vector<uint8_t> SHA256Crypto::hash(const std::vector<uint8_t>& data) {
    EVPMDContext ctx;
    
    // 初始化哈希
    if (EVP_DigestInit_ex(ctx.get(), EVP_sha256(), nullptr) != 1) {
        throw EncryptionException("初始化SHA256失败");
    }
    
    // 更新哈希数据
    if (EVP_DigestUpdate(ctx.get(), data.data(), data.size()) != 1) {
        throw EncryptionException("更新SHA256数据失败");
    }
    
    // 完成哈希
    std::vector<uint8_t> hash(SHA256_DIGEST_LENGTH);
    unsigned int hashLen;
    if (EVP_DigestFinal_ex(ctx.get(), hash.data(), &hashLen) != 1) {
        throw EncryptionException("完成SHA256哈希失败");
    }
    
    hash.resize(hashLen);
    return hash;
}

std::string SHA256Crypto::hashString(const std::string& data) {
    std::vector<uint8_t> dataBytes(data.begin(), data.end());
    auto hashBytes = hash(dataBytes);
    return bytesToHexString(hashBytes);
}

std::string SHA256Crypto::bytesToHexString(const std::vector<uint8_t>& bytes) {
    std::ostringstream oss;
    oss << std::hex << std::setfill('0');
    
    for (uint8_t byte : bytes) {
        oss << std::setw(2) << static_cast<unsigned int>(byte);
    }
    
    return oss.str();
}

} // namespace crypto
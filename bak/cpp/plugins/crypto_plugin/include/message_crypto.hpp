/**
 * @file message_crypto.hpp
 * @brief 加密插件消息定义
 * <AUTHOR> project
 * @date 2024
 */

#ifndef MESSAGE_CRYPTO_HPP
#define MESSAGE_CRYPTO_HPP

#include <string>
#include <nlohmann/json.hpp>

namespace zexuan {
namespace plugin {
namespace crypto {

/**
 * @brief 生成加密插件可用消息的JSON (Message格式的不可变部分)
 * @return JSON字符串，包含所有可用的message不可变部分
 */
inline std::string getAvailableMessagesJson() {
    nlohmann::json messages = {
        {
            "plugin_name", "crypto_plugin"
        },

        {
            "description", "加密解密插件 - 支持AES/RSA/SHA256/Base64等算法"
        },
        {
            "available_messages", {
                {
                    {
                        "name", "AES加密"
                    },
                    {
                        "description", "使用AES算法加密文件"
                    },
                    {
                        "message_template", {
                            {"typ", 0x01},
                            {"vsq", 0x81},
                            {"cot", 0x06},

                            {"fun", 0x01},
                            {"inf", 0x01}
                        }
                    },
                    {
                        "variable_parts", {
                            "file_path (string): 待加密的文件路径"
                        }
                    }
                },
                {
                    {
                        "name", "RSA加密"
                    },
                    {
                        "description", "使用RSA算法加密文件"
                    },
                    {
                        "message_template", {
                            {"typ", 0x01},
                            {"vsq", 0x81},
                            {"cot", 0x06},

                            {"fun", 0x02},
                            {"inf", 0x01}
                        }
                    },
                    {
                        "variable_parts", {
                            "file_path (string): 待加密的文件路径"
                        }
                    }
                },
                {
                    {
                        "name", "Base64编码"
                    },
                    {
                        "description", "使用Base64编码文件"
                    },
                    {
                        "message_template", {
                            {"typ", 0x01},
                            {"vsq", 0x81},
                            {"cot", 0x06},

                            {"fun", 0x03},
                            {"inf", 0x01}
                        }
                    },
                    {
                        "variable_parts", {
                            "file_path (string): 待编码的文件路径"
                        }
                    }
                },
                {
                    {
                        "name", "SHA256哈希"
                    },
                    {
                        "description", "计算文件SHA256哈希值"
                    },
                    {
                        "message_template", {
                            {"typ", 0x01},
                            {"vsq", 0x81},
                            {"cot", 0x06},

                            {"fun", 0x04},
                            {"inf", 0x01}
                        }
                    },
                    {
                        "variable_parts", {
                            "file_path (string): 待计算哈希的文件路径"
                        }
                    }
                }
            }
        }
    };
    
    return messages.dump(4);
}

} // namespace crypto
} // namespace plugin
} // namespace zexuan

#endif // MESSAGE_CRYPTO_HPP

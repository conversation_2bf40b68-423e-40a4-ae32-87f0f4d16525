#pragma once

#include "crypto_base.hpp"
#include "crypto_factory.hpp"
#include "crypto_exceptions.hpp"
#include <memory>
#include <string>
#include <unordered_map>
#include <shared_mutex>
#include <mutex>

namespace crypto {

/**
 * 加密配置类
 */
class CryptoConfig {
public:
    std::string algorithm;      // "aes-256", "rsa", "sha256"
    std::string mode;          // "cbc", "gcm", "ecb" (for symmetric)
    std::string padding;       // "pkcs7", "none"
    size_t keySize;           // 密钥长度
    std::unordered_map<std::string, std::string> parameters; // 其他参数
    
    CryptoConfig() : keySize(0) {}
    
    CryptoConfig(const std::string& algo) 
        : algorithm(algo), keySize(0) {}
};

/**
 * 加密管理器
 * 负责创建和管理加密算法实例
 */
class CryptoManager {
public:
    CryptoManager();
    ~CryptoManager() = default;
    
    /**
     * 根据配置创建加密器
     */
    std::shared_ptr<CryptoBase> createCrypto(const CryptoConfig& config);
    
    /**
     * 根据算法名称创建加密器
     */
    std::shared_ptr<CryptoBase> createCrypto(const std::string& algorithm);
    
    /**
     * 获取缓存的加密器（如果启用缓存）
     */
    std::shared_ptr<CryptoBase> getCachedCrypto(const std::string& algorithm);
    
    /**
     * 启用/禁用缓存
     */
    void setCacheEnabled(bool enabled) { cacheEnabled_ = enabled; }
    
    /**
     * 清空缓存
     */
    void clearCache();
    
    /**
     * 获取支持的算法列表
     */
    std::vector<std::string> getSupportedAlgorithms();
    
    /**
     * 检查算法是否支持
     */
    bool isAlgorithmSupported(const std::string& algorithm);

private:
    bool cacheEnabled_;
    std::unordered_map<std::string, std::shared_ptr<CryptoBase>> cache_;
    mutable std::shared_mutex cacheMutex_;
    
    /**
     * 初始化算法注册
     */
    void initializeAlgorithms();
};

/**
 * 线程安全的加密工厂
 */
class ThreadSafeCryptoFactory {
private:
    mutable std::shared_mutex factoryMutex_;
    
public:
    std::shared_ptr<CryptoBase> createCrypto(const std::string& algorithm) {
        std::shared_lock<std::shared_mutex> lock(factoryMutex_);
        return CryptoFactory::create(algorithm);
    }
    
    std::vector<std::string> getRegisteredNames() {
        std::shared_lock<std::shared_mutex> lock(factoryMutex_);
        return CryptoFactory::getRegisteredNames();
    }
};

} // namespace crypto
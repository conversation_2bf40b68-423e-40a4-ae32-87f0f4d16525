#pragma once

#include "crypto_base.hpp"
#include "crypto_exceptions.hpp"
#include <memory>

namespace crypto {

/**
 * Hex编码算法实现
 */
class HexCrypto : public EncodingCrypto {
public:
    HexCrypto();
    ~HexCrypto() override;
    
    // CryptoBase接口实现
    std::string getAlgorithmName() const override { return "Hex"; }
    std::string getDescription() const override { return "十六进制编码算法"; }
    
    // EncodingCrypto接口实现
    std::string encode(const std::vector<uint8_t>& data) override;
    std::vector<uint8_t> decode(const std::string& encodedData) override;

private:
    // 辅助方法
    bool isHexChar(char c);
    uint8_t hexCharToValue(char c);
};

} // namespace crypto
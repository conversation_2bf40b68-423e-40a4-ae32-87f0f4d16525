#pragma once

#include <stdexcept>
#include <string>

namespace crypto {

/**
 * 加密异常基类
 */
class CryptoException : public std::exception {
public:
    explicit CryptoException(const std::string& message) : message_(message) {}
    
    const char* what() const noexcept override {
        return message_.c_str();
    }

private:
    std::string message_;
};

/**
 * 算法未找到异常
 */
class AlgorithmNotFoundException : public CryptoException {
public:
    explicit AlgorithmNotFoundException(const std::string& algorithm) 
        : CryptoException("算法未找到: " + algorithm) {}
};

/**
 * 无效密钥异常
 */
class InvalidKeyException : public CryptoException {
public:
    explicit InvalidKeyException(const std::string& reason) 
        : CryptoException("无效密钥: " + reason) {}
};

/**
 * 加密失败异常
 */
class EncryptionException : public CryptoException {
public:
    explicit EncryptionException(const std::string& details) 
        : CryptoException("加密失败: " + details) {}
};

/**
 * 解密失败异常
 */
class DecryptionException : public CryptoException {
public:
    explicit DecryptionException(const std::string& details) 
        : CryptoException("解密失败: " + details) {}
};

/**
 * 密钥生成失败异常
 */
class KeyGenerationException : public CryptoException {
public:
    explicit KeyGenerationException(const std::string& details) 
        : CryptoException("密钥生成失败: " + details) {}
};

/**
 * OpenSSL错误异常
 */
class OpenSSLException : public CryptoException {
public:
    explicit OpenSSLException(const std::string& operation) 
        : CryptoException("OpenSSL错误 in " + operation) {}
};

/**
 * 错误码枚举
 */
enum class CryptoErrorCode {
    SUCCESS = 0,
    ALGORITHM_NOT_FOUND = 1001,
    INVALID_KEY = 1002,
    ENCRYPTION_FAILED = 1003,
    DECRYPTION_FAILED = 1004,
    KEY_GENERATION_FAILED = 1005,
    OPENSSL_ERROR = 1006
};

} // namespace crypto
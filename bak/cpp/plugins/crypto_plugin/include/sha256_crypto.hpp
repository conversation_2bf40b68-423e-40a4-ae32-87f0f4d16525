#pragma once

#include "crypto_base.hpp"
#include "crypto_exceptions.hpp"
#include <openssl/evp.h>
#include <openssl/sha.h>
#include <memory>

namespace crypto {

/**
 * SHA256哈希算法实现
 */
class SHA256Crypto : public HashCrypto {
public:
    SHA256Crypto();
    ~SHA256Crypto() override;
    
    // CryptoBase接口实现
    std::string getAlgorithmName() const override { return "SHA256"; }
    std::string getDescription() const override { return "SHA-256哈希算法"; }
    bool supportsKeyGeneration() const override { return false; }
    
    std::string generateKey() override { return ""; } // 哈希不需要密钥
    bool validateKey(const std::string& key) const override { return true; }
    
    // HashCrypto接口实现
    std::vector<uint8_t> hash(const std::vector<uint8_t>& data) override;
    std::string hashString(const std::string& data) override;
    size_t getHashSize() const override { return SHA256_DIGEST_LENGTH; }

private:
    // 辅助方法
    std::string bytesToHexString(const std::vector<uint8_t>& bytes);
    
    // OpenSSL上下文管理
    class EVPMDContext {
    public:
        EVPMDContext() : ctx_(EVP_MD_CTX_new()) {
            if (!ctx_) {
                throw OpenSSLException("EVP_MD_CTX_new");
            }
        }
        
        ~EVPMDContext() {
            if (ctx_) {
                EVP_MD_CTX_free(ctx_);
            }
        }
        
        EVP_MD_CTX* get() { return ctx_; }
        
        // 禁止拷贝
        EVPMDContext(const EVPMDContext&) = delete;
        EVPMDContext& operator=(const EVPMDContext&) = delete;
        
    private:
        EVP_MD_CTX* ctx_;
    };
};

} // namespace crypto
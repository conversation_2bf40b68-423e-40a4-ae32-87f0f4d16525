#pragma once

#include "crypto_base.hpp"
#include "crypto_exceptions.hpp"
#include <openssl/evp.h>
#include <openssl/rsa.h>
#include <openssl/pem.h>
#include <openssl/rand.h>
#include <memory>

namespace crypto {

/**
 * RSA加密算法实现
 * 支持RSA-2048密钥对生成、加密、解密和签名
 */
class RSACrypto : public AsymmetricCrypto {
public:
    RSACrypto();
    ~RSACrypto() override;
    
    // CryptoBase接口实现
    std::vector<uint8_t> encrypt(const std::vector<uint8_t>& data, 
                                const std::string& key = "") override;
    std::vector<uint8_t> decrypt(const std::vector<uint8_t>& data, 
                                const std::string& key = "") override;
    
    std::string getAlgorithmName() const override { return "RSA-2048"; }
    std::string getDescription() const override { return "RSA 2048位非对称加密"; }
    bool supportsKeyGeneration() const override { return true; }
    
    std::string generateKey() override;
    bool validateKey(const std::string& key) const override;
    
    // AsymmetricCrypto接口实现
    std::pair<std::string, std::string> generateKeyPair() override;
    std::vector<uint8_t> encryptWithPublicKey(const std::vector<uint8_t>& data, 
                                             const std::string& publicKey) override;
    std::vector<uint8_t> decryptWithPrivateKey(const std::vector<uint8_t>& data, 
                                              const std::string& privateKey) override;
    std::vector<uint8_t> sign(const std::vector<uint8_t>& data, 
                             const std::string& privateKey) override;
    bool verify(const std::vector<uint8_t>& data, 
               const std::vector<uint8_t>& signature, 
               const std::string& publicKey) override;

private:
    static const int KEY_SIZE = 2048;
    
    // 辅助方法
    EVP_PKEY* loadPublicKeyFromString(const std::string& publicKeyStr) const;
    EVP_PKEY* loadPrivateKeyFromString(const std::string& privateKeyStr) const;
    std::string keyToString(EVP_PKEY* pkey, bool isPrivate);
    std::string extractPublicKeyFromPrivateKey(EVP_PKEY* privateKey);
    std::vector<uint8_t> hexStringToBytes(const std::string& hex);
    std::string bytesToHexString(const std::vector<uint8_t>& bytes);
    
    // OpenSSL上下文管理
    class EVPKeyContext {
    public:
        EVPKeyContext() : pkey_(nullptr) {}
        
        ~EVPKeyContext() {
            if (pkey_) {
                EVP_PKEY_free(pkey_);
            }
        }
        
        EVP_PKEY* get() { return pkey_; }
        void set(EVP_PKEY* pkey) { 
            if (pkey_) EVP_PKEY_free(pkey_);
            pkey_ = pkey; 
        }
        
        // 禁止拷贝
        EVPKeyContext(const EVPKeyContext&) = delete;
        EVPKeyContext& operator=(const EVPKeyContext&) = delete;
        
    private:
        EVP_PKEY* pkey_;
    };
};

} // namespace crypto
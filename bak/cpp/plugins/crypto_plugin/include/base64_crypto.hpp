#pragma once

#include "crypto_base.hpp"
#include "crypto_exceptions.hpp"
#include <openssl/evp.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>
#include <memory>

namespace crypto {

/**
 * Base64编码算法实现
 */
class Base64Crypto : public EncodingCrypto {
public:
    Base64Crypto();
    ~Base64Crypto() override;
    
    // CryptoBase接口实现
    std::string getAlgorithmName() const override { return "Base64"; }
    std::string getDescription() const override { return "Base64编码算法"; }
    
    // EncodingCrypto接口实现
    std::string encode(const std::vector<uint8_t>& data) override;
    std::vector<uint8_t> decode(const std::string& encodedData) override;

private:
    // 辅助方法
    std::string base64Encode(const uint8_t* data, size_t length);
    std::vector<uint8_t> base64Decode(const std::string& encoded);
};

} // namespace crypto
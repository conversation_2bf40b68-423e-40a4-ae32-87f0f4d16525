#pragma once

#include "crypto_base.hpp"
#include "zexuan/factory/factory.hpp"

namespace crypto {

// 主工厂类型定义
typedef zexuan::SingletonFactory<CryptoBase> CryptoFactory;

/**
 * 加密算法注册器
 * 负责注册所有可用的加密算法到工厂中
 */
class CryptoRegistrar {
public:
    /**
     * 注册所有加密算法
     */
    static void registerAllAlgorithms();
    
    /**
     * 获取所有已注册的算法名称
     */
    static std::vector<std::string> getRegisteredAlgorithms();
    
    /**
     * 检查算法是否已注册
     */
    static bool isAlgorithmRegistered(const std::string& algorithm);
};

} // namespace crypto
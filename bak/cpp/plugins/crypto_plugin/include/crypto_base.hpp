#pragma once

#include <vector>
#include <string>
#include <memory>

namespace crypto {

/**
 * 加密算法基础接口类
 * 提供所有加密算法的统一接口
 */
class CryptoBase {
public:
    virtual ~CryptoBase() = default;
    
    // 核心加解密接口
    virtual std::vector<uint8_t> encrypt(const std::vector<uint8_t>& data, 
                                        const std::string& key = "") = 0;
    virtual std::vector<uint8_t> decrypt(const std::vector<uint8_t>& data, 
                                        const std::string& key = "") = 0;
    
    // 字符串便利接口
    virtual std::string encryptString(const std::string& plaintext, 
                                     const std::string& key = "");
    virtual std::string decryptString(const std::string& ciphertext, 
                                     const std::string& key = "");
    
    // 算法信息
    virtual std::string getAlgorithmName() const = 0;
    virtual std::string getDescription() const = 0;
    virtual bool supportsKeyGeneration() const = 0;
    
    // 密钥管理
    virtual std::string generateKey() = 0;
    virtual bool validateKey(const std::string& key) const = 0;
};

/**
 * 对称加密算法接口
 */
class SymmetricCrypto : public CryptoBase {
public:
    // 对称加密特有的方法
    virtual void setInitializationVector(const std::vector<uint8_t>& iv) = 0;
    virtual std::vector<uint8_t> getInitializationVector() const = 0;
    virtual size_t getKeySize() const = 0;
    virtual size_t getBlockSize() const = 0;
};

/**
 * 非对称加密算法接口
 */
class AsymmetricCrypto : public CryptoBase {
public:
    // 非对称加密特有的方法
    virtual std::pair<std::string, std::string> generateKeyPair() = 0;
    virtual std::vector<uint8_t> encryptWithPublicKey(const std::vector<uint8_t>& data, 
                                                     const std::string& publicKey) = 0;
    virtual std::vector<uint8_t> decryptWithPrivateKey(const std::vector<uint8_t>& data, 
                                                      const std::string& privateKey) = 0;
    virtual std::vector<uint8_t> sign(const std::vector<uint8_t>& data, 
                                     const std::string& privateKey) = 0;
    virtual bool verify(const std::vector<uint8_t>& data, 
                       const std::vector<uint8_t>& signature, 
                       const std::string& publicKey) = 0;
};

/**
 * 哈希算法接口
 */
class HashCrypto : public CryptoBase {
public:
    // 哈希特有的方法
    virtual std::vector<uint8_t> hash(const std::vector<uint8_t>& data) = 0;
    virtual std::string hashString(const std::string& data) = 0;
    virtual size_t getHashSize() const = 0;
    
    // 对于哈希算法，encrypt/decrypt实际上是hash操作
    std::vector<uint8_t> encrypt(const std::vector<uint8_t>& data, 
                                const std::string& key = "") override {
        return hash(data);
    }
    
    std::vector<uint8_t> decrypt(const std::vector<uint8_t>& data, 
                                const std::string& key = "") override {
        // 哈希不支持解密，返回原数据
        return data;
    }
};

/**
 * 编码算法接口
 */
class EncodingCrypto : public CryptoBase {
public:
    // 编码特有的方法
    virtual std::string encode(const std::vector<uint8_t>& data) = 0;
    virtual std::vector<uint8_t> decode(const std::string& encodedData) = 0;
    
    // 对于编码算法，encrypt/decrypt实际上是encode/decode操作
    std::vector<uint8_t> encrypt(const std::vector<uint8_t>& data, 
                                const std::string& key = "") override;
    
    std::vector<uint8_t> decrypt(const std::vector<uint8_t>& data, 
                                const std::string& key = "") override;
    
    // 编码算法不需要密钥
    std::string generateKey() override { return ""; }
    bool validateKey(const std::string& key) const override { return true; }
    bool supportsKeyGeneration() const override { return false; }
};

} // namespace crypto
#pragma once

#include "crypto_base.hpp"
#include "crypto_exceptions.hpp"
#include <string>
#include <vector>
#include <memory>

namespace crypto {

/**
 * 文件加密辅助类
 * 负责处理文件的加密和解密操作
 */
class FileCryptoHelper {
public:
    /**
     * 加密文件
     * @param inputFilePath 输入文件路径
     * @param outputFilePath 输出文件路径
     * @param crypto 加密算法实例
     * @param key 加密密钥
     * @return 加密是否成功
     */
    static bool encryptFile(const std::string& inputFilePath,
                           const std::string& outputFilePath,
                           std::shared_ptr<CryptoBase> crypto,
                           const std::string& key);
    
    /**
     * 解密文件
     * @param inputFilePath 输入文件路径
     * @param outputFilePath 输出文件路径
     * @param crypto 加密算法实例
     * @param key 解密密钥
     * @return 解密是否成功
     */
    static bool decryptFile(const std::string& inputFilePath,
                           const std::string& outputFilePath,
                           std::shared_ptr<CryptoBase> crypto,
                           const std::string& key);
    
    /**
     * 读取文件内容
     * @param filePath 文件路径
     * @return 文件内容
     */
    static std::vector<uint8_t> readFile(const std::string& filePath);
    
    /**
     * 写入文件内容
     * @param filePath 文件路径
     * @param data 要写入的数据
     * @return 写入是否成功
     */
    static bool writeFile(const std::string& filePath, const std::vector<uint8_t>& data);
    
    /**
     * 检查文件是否存在
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    static bool fileExists(const std::string& filePath);
    
    /**
     * 获取文件大小
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    static size_t getFileSize(const std::string& filePath);

private:
    // 私有构造函数，防止实例化
    FileCryptoHelper() = delete;
};

} // namespace crypto
#pragma once

#include "crypto_base.hpp"
#include "crypto_exceptions.hpp"
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/aes.h>
#include <memory>

namespace crypto {

/**
 * AES加密算法实现
 * 支持AES-256-GCM模式，提供认证加密
 */
class AESCrypto : public SymmetricCrypto {
public:
    AESCrypto();
    ~AESCrypto() override;
    
    // CryptoBase接口实现
    std::vector<uint8_t> encrypt(const std::vector<uint8_t>& data, 
                                const std::string& key = "") override;
    std::vector<uint8_t> decrypt(const std::vector<uint8_t>& data, 
                                const std::string& key = "") override;
    
    std::string getAlgorithmName() const override { return "AES-256-GCM"; }
    std::string getDescription() const override { return "AES 256位 GCM模式加密"; }
    bool supportsKeyGeneration() const override { return true; }
    
    std::string generateKey() override;
    bool validateKey(const std::string& key) const override;
    
    // SymmetricCrypto接口实现
    void setInitializationVector(const std::vector<uint8_t>& iv) override;
    std::vector<uint8_t> getInitializationVector() const override;
    size_t getKeySize() const override { return 32; } // 256位 = 32字节
    size_t getBlockSize() const override { return 16; } // AES块大小为128位 = 16字节

private:
    std::vector<uint8_t> iv_;
    const EVP_CIPHER* cipher_;
    
    // 辅助方法
    std::vector<uint8_t> hexStringToBytes(const std::string& hex);
    std::string bytesToHexString(const std::vector<uint8_t>& bytes);
    void generateRandomIV();
    
    // OpenSSL上下文管理
    class EVPCipherContext {
    public:
        EVPCipherContext() : ctx_(EVP_CIPHER_CTX_new()) {
            if (!ctx_) {
                throw OpenSSLException("EVP_CIPHER_CTX_new");
            }
        }
        
        ~EVPCipherContext() {
            if (ctx_) {
                EVP_CIPHER_CTX_free(ctx_);
            }
        }
        
        EVP_CIPHER_CTX* get() { return ctx_; }
        
        // 禁止拷贝
        EVPCipherContext(const EVPCipherContext&) = delete;
        EVPCipherContext& operator=(const EVPCipherContext&) = delete;
        
    private:
        EVP_CIPHER_CTX* ctx_;
    };
};

} // namespace crypto
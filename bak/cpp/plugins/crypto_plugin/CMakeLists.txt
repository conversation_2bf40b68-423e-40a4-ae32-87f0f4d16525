# crypto Plugin CMakeLists.txt

# 查找OpenSSL
find_package(OpenSSL REQUIRED)

# 创建 crypto_plugin 动态库
add_library(crypto_plugin SHARED
    src/crypto_plugin.cpp
    src/crypto_base.cpp
    src/crypto_manager.cpp
    src/crypto_factory.cpp
    src/aes_crypto.cpp
    src/rsa_crypto.cpp
    src/sha256_crypto.cpp
    src/base64_crypto.cpp
    src/hex_crypto.cpp
    src/file_crypto_helper.cpp
)

# 链接必要的库
target_link_libraries(crypto_plugin
    PRIVATE
        plugin_interface
        openssl::openssl
)

# 设置包含目录
target_include_directories(crypto_plugin PRIVATE
    ${CMAKE_SOURCE_DIR}/core/include
    ${CMAKE_SOURCE_DIR}/interface/include
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 设置输出目录
set_target_properties(crypto_plugin PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/libs/plugins"
    VERSION 1.0.0
    SOVERSION 1
)

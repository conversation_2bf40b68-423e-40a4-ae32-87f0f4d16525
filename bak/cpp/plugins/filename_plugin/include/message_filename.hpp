/**
 * @file message_filename.hpp
 * @brief 文件名插件消息定义
 * <AUTHOR> project
 * @date 2024
 */

#ifndef MESSAGE_FILENAME_HPP
#define MESSAGE_FILENAME_HPP

#include <string>
#include <nlohmann/json.hpp>

namespace zexuan {
namespace plugin {
namespace filename {

/**
 * @brief 生成文件名插件可用消息的JSON (Message格式的不可变部分)
 * @return JSON字符串，包含所有可用的message不可变部分
 */
inline std::string getAvailableMessagesJson() {
    nlohmann::json messages = {
        {
            "plugin_name", "filename_plugin"
        },

        {
            "description", "文件重命名插件 - 批量重命名目录下的所有文件"
        },
        {
            "available_messages", {
                {
                    {
                        "name", "批量文件重命名"
                    },
                    {
                        "description", "批量重命名指定目录下的所有文件"
                    },
                    {
                        "message_template", {
                            {"typ", 0x01},
                            {"vsq", 0x81},
                            {"cot", 0x06},
                            {"fun", 0x01},
                            {"inf", 0x01}
                        }
                    },
                    {
                        "variable_parts", {
                            "directory_path (string): 要重命名文件的目录路径"
                        }
                    }
                }
            }
        }
    };
    
    return messages.dump(4);
}

} // namespace filename
} // namespace plugin
} // namespace zexuan

#endif // MESSAGE_FILENAME_HPP

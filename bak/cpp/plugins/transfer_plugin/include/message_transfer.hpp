/**
 * @file message_transfer.hpp
 * @brief 传输插件消息定义
 * <AUTHOR> project
 * @date 2024
 */

#ifndef MESSAGE_TRANSFER_HPP
#define MESSAGE_TRANSFER_HPP

#include <string>
#include <nlohmann/json.hpp>

namespace zexuan {
namespace plugin {
namespace transfer {

/**
 * @brief 生成传输插件可用消息的JSON (Message格式的不可变部分)
 * @return JSON字符串，包含所有可用的message不可变部分
 */
inline std::string getAvailableMessagesJson() {
    nlohmann::json messages = {
        {
            "plugin_name", "transfer_plugin"
        },

        {
            "description", "传输插件 - 支持SCP/SFTP协议文件传输"
        },
        {
            "available_messages", {
                {
                    {
                        "name", "SCP传输"
                    },
                    {
                        "description", "使用SCP协议传输文件"
                    },
                    {
                        "message_template", {
                            {"typ", 0x01},
                            {"vsq", 0x81},
                            {"cot", 0x06},

                            {"fun", 0x01},
                            {"inf", 0x01}
                        }
                    },
                    {
                        "variable_parts", {
                            "source_path (string): 源文件路径"
                        }
                    }
                },
                {
                    {
                        "name", "SFTP传输"
                    },
                    {
                        "description", "使用SFTP协议传输文件"
                    },
                    {
                        "message_template", {
                            {"typ", 0x01},
                            {"vsq", 0x81},
                            {"cot", 0x06},

                            {"fun", 0x02},
                            {"inf", 0x01}
                        }
                    },
                    {
                        "variable_parts", {
                            "source_path (string): 源文件路径"
                        }
                    }
                }
            }
        }
    };
    
    return messages.dump(4);
}

} // namespace transfer
} // namespace plugin
} // namespace zexuan

#endif // MESSAGE_TRANSFER_HPP

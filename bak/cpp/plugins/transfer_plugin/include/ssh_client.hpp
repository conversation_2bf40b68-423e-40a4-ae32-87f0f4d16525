#pragma once

#include <libssh2.h>
#include <libssh2_sftp.h>
#include <string>
#include <vector>

namespace transfer {

class SSHClient {
public:
    SSHClient();
    ~SSHClient();

    // 连接并认证（阻塞）
    bool connectAndAuth(const std::string& host, int port,
                        const std::string& user, const std::string& password,
                        std::string& errMsg);

    // 确保远端目录存在（mkdir -p）
    bool ensureRemoteDir(const std::string& remoteDir, std::string& errMsg);

    // 使用 SCP 上传一个文件到 remoteDir/filename
    bool scpUploadFile(const std::string& localFile, const std::string& remoteDir, std::string& errMsg);

    // 使用 SFTP 上传一个文件到 remoteDir/filename（会自动创建目录）
    bool sftpUploadFile(const std::string& localFile, const std::string& remoteDir, std::string& errMsg);

private:
    int sockfd_;
    LIBSSH2_SESSION* session_;
    LIBSSH2_SFTP* sftp_;

    bool openSftp(std::string& errMsg);
};

} // namespace transfer


#pragma once

#include "transfer_base.hpp"
#include "zexuan/factory/factory.hpp"
#include <vector>
#include <string>

namespace transfer {

// 主工厂
using TransferFactory = zexuan::SingletonFactory<TransferBase>;

class TransferRegistrar {
public:
    static void registerAll();
    static std::vector<std::string> getRegistered();
    static bool isRegistered(const std::string& name);
};

} // namespace transfer


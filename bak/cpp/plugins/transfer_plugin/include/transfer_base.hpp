#pragma once

#include <string>
#include <filesystem>
#include <vector>
#include <memory>

namespace transfer {

class TransferBase {
public:
    virtual ~TransferBase() = default;

    // 传输入口：src 可以是文件或目录；dst 必须是目录
    virtual bool transfer(const std::string& src, const std::string& dstDir) = 0;

protected:
    // 工具：收集目录下所有普通文件（不递归）
    static std::vector<std::filesystem::path> listFiles(const std::filesystem::path& dir);

    // 工具：确保目录存在
    static bool ensureDir(const std::filesystem::path& dir);

    // 本地回退：直接复制
    static bool copyLocal(const std::filesystem::path& src, const std::filesystem::path& dstDir);
};

} // namespace transfer


#include "../include/sftp_transfer.hpp"
#include "../include/ssh_client.hpp"
#include "zexuan/logger.hpp"
#include <filesystem>
#include <cstdlib>

namespace fs = std::filesystem;

namespace transfer {

bool SftpTransfer::transfer(const std::string& src, const std::string& dstDir) {
    auto logger = zexuan::Logger::getFileLogger("plugin/transfer_plugin");
    fs::path srcPath(src);

    if (!fs::exists(srcPath)) {
        logger->error("SFTP: source not exists: {}", src);
        return false;
    }

    // 连接参数（可用环境变量覆盖）
    const char* host = std::getenv("ZEXUAN_SSH_HOST");
    const char* portEnv = std::getenv("ZEXUAN_SSH_PORT");
    const char* user = std::getenv("ZEXUAN_SSH_USER");
    const char* pass = std::getenv("ZEXUAN_SSH_PASS");
    std::string hostStr = host ? host : "zx.zexuan.online";
    int port = portEnv ? std::atoi(portEnv) : 3422;
    std::string userStr = user ? user : "root";
    std::string passStr = pass ? pass : "!furryzexuan0";

    SSHClient client;
    std::string err;
    if (!client.connectAndAuth(hostStr, port, userStr, passStr, err)) {
        logger->error("SFTP connect/auth failed: {}", err);
        return false;
    }

    if (fs::is_regular_file(srcPath)) {
        bool ok = client.sftpUploadFile(srcPath.string(), dstDir, err);
        if (!ok) logger->error("SFTP upload failed: {} -> {}: {}", srcPath.string(), dstDir, err);
        return ok;
    }

    auto files = listFiles(srcPath);
    if (files.empty()) {
        logger->warn("SFTP: no files in directory: {}", src);
    }
    bool okAll = true;
    for (auto& f : files) {
        bool ok = client.sftpUploadFile(f.string(), dstDir, err);
        if (!ok) { logger->error("SFTP upload failed: {} -> {}: {}", f.string(), dstDir, err); okAll = false; }
    }
    return okAll;
}

} // namespace transfer


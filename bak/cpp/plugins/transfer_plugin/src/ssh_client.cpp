#include "../include/ssh_client.hpp"
#include "zexuan/logger.hpp"

#include <sys/types.h>
#include <sys/socket.h>
#include <netdb.h>
#include <unistd.h>
#include <fcntl.h>
#include <arpa/inet.h>
#include <sys/stat.h>
#include <errno.h>
#include <cstring>
#include <filesystem>
#include <fstream>

namespace fs = std::filesystem;

namespace transfer {

static int connect_tcp(const std::string& host, int port, std::string& err) {
    struct addrinfo hints{}; hints.ai_socktype = SOCK_STREAM; hints.ai_family = AF_UNSPEC;
    struct addrinfo* res = nullptr;
    std::string portStr = std::to_string(port);
    int rc = getaddrinfo(host.c_str(), portStr.c_str(), &hints, &res);
    if (rc != 0) { err = gai_strerror(rc); return -1; }
    int sock = -1;
    for (auto p = res; p; p = p->ai_next) {
        sock = ::socket(p->ai_family, p->ai_socktype, p->ai_protocol);
        if (sock < 0) continue;
        if (::connect(sock, p->ai_addr, p->ai_addrlen) == 0) break;
        ::close(sock); sock = -1;
    }
    freeaddrinfo(res);
    if (sock < 0) err = "connect() failed";
    return sock;
}

SSHClient::SSHClient() : sockfd_(-1), session_(nullptr), sftp_(nullptr) {}

SSHClient::~SSHClient() {
    if (sftp_) { libssh2_sftp_shutdown(sftp_); sftp_ = nullptr; }
    if (session_) { libssh2_session_disconnect(session_, "Normal Shutdown"); libssh2_session_free(session_); session_ = nullptr; }
    if (sockfd_ >= 0) { ::close(sockfd_); sockfd_ = -1; }
    libssh2_exit();
}

bool SSHClient::connectAndAuth(const std::string& host, int port,
                               const std::string& user, const std::string& password,
                               std::string& errMsg) {
    auto logger = zexuan::Logger::getFileLogger("plugin/transfer_plugin");
    // 初始化全局（幂等）
    libssh2_init(0);

    sockfd_ = connect_tcp(host, port, errMsg);
    if (sockfd_ < 0) { logger->error("TCP connect failed: {}", errMsg); return false; }

    session_ = libssh2_session_init();
    if (!session_) { errMsg = "libssh2_session_init failed"; return false; }

    if (libssh2_session_handshake(session_, sockfd_)) {
        errMsg = "SSH handshake failed"; return false;
    }

    // 密码认证
    if (libssh2_userauth_password(session_, user.c_str(), password.c_str())) {
        errMsg = "SSH password authentication failed"; return false;
    }

    logger->info("SSH connected and authenticated to {}:{} as {}", host, port, user);
    return true;
}

bool SSHClient::ensureRemoteDir(const std::string& remoteDir, std::string& errMsg) {
    // 用 exec channel 执行 mkdir -p
    LIBSSH2_CHANNEL* channel = libssh2_channel_open_session(session_);
    if (!channel) { errMsg = "open channel failed"; return false; }
    std::string cmd = "mkdir -p '" + remoteDir + "'";
    if (libssh2_channel_exec(channel, cmd.c_str())) {
        errMsg = "exec mkdir failed"; libssh2_channel_free(channel); return false;
    }
    libssh2_channel_close(channel);
    libssh2_channel_free(channel);
    return true;
}

bool SSHClient::scpUploadFile(const std::string& localFile, const std::string& remoteDir, std::string& errMsg) {
    if (!fs::exists(localFile) || !fs::is_regular_file(localFile)) { errMsg = "local file not exists"; return false; }

    // 确保目录
    if (!ensureRemoteDir(remoteDir, errMsg)) return false;

    struct stat st{};
    if (stat(localFile.c_str(), &st) != 0) { errMsg = std::strerror(errno); return false; }

    std::string remotePath = remoteDir + "/" + fs::path(localFile).filename().string();

    LIBSSH2_CHANNEL* channel = libssh2_scp_send64(session_, remotePath.c_str(), 0644, st.st_size, 0, 0);
    if (!channel) { errMsg = "libssh2_scp_send64 failed"; return false; }

    std::ifstream ifs(localFile, std::ios::binary);
    std::vector<char> buf(32768);
    while (ifs) {
        ifs.read(buf.data(), buf.size());
        std::streamsize n = ifs.gcount();
        if (n <= 0) break;
        const char* p = buf.data();
        long toWrite = static_cast<long>(n);
        while (toWrite > 0) {
            ssize_t wrote = libssh2_channel_write(channel, p, toWrite);
            if (wrote < 0) { errMsg = "scp write failed"; libssh2_channel_free(channel); return false; }
            toWrite -= wrote; p += wrote;
        }
    }

    libssh2_channel_send_eof(channel);
    libssh2_channel_wait_eof(channel);
    libssh2_channel_wait_closed(channel);
    libssh2_channel_free(channel);
    return true;
}

bool SSHClient::openSftp(std::string& errMsg) {
    if (sftp_) return true;
    sftp_ = libssh2_sftp_init(session_);
    if (!sftp_) { errMsg = "libssh2_sftp_init failed"; return false; }
    return true;
}

bool SSHClient::sftpUploadFile(const std::string& localFile, const std::string& remoteDir, std::string& errMsg) {
    if (!fs::exists(localFile) || !fs::is_regular_file(localFile)) { errMsg = "local file not exists"; return false; }

    if (!openSftp(errMsg)) return false;

    // 逐级创建目录
    std::string path = remoteDir;
    if (!path.empty() && path[0] == '/') {
        // ok
    }
    size_t pos = 1;
    while (pos < path.size()) {
        pos = path.find('/', pos);
        std::string sub = (pos == std::string::npos) ? path : path.substr(0, pos);
        libssh2_sftp_mkdir(sftp_, sub.c_str(), 0755); // 已存在忽略
        if (pos == std::string::npos) break;
        ++pos;
    }

    std::string remotePath = remoteDir + "/" + fs::path(localFile).filename().string();

    LIBSSH2_SFTP_HANDLE* handle = libssh2_sftp_open(sftp_, remotePath.c_str(),
        LIBSSH2_FXF_WRITE | LIBSSH2_FXF_CREAT | LIBSSH2_FXF_TRUNC, 0644);
    if (!handle) { errMsg = "libssh2_sftp_open failed"; return false; }

    std::ifstream ifs(localFile, std::ios::binary);
    std::vector<char> buf(32768);
    while (ifs) {
        ifs.read(buf.data(), buf.size());
        std::streamsize n = ifs.gcount();
        if (n <= 0) break;
        const char* p = buf.data();
        long toWrite = static_cast<long>(n);
        while (toWrite > 0) {
            ssize_t wrote = libssh2_sftp_write(handle, p, toWrite);
            if (wrote < 0) { errMsg = "sftp write failed"; libssh2_sftp_close(handle); return false; }
            toWrite -= wrote; p += wrote;
        }
    }

    libssh2_sftp_close(handle);
    return true;
}

} // namespace transfer


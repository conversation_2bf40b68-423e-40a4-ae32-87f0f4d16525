#include "../include/scp_transfer.hpp"
#include "../include/ssh_client.hpp"
#include "zexuan/logger.hpp"
#include <filesystem>
#include <cstdlib>

namespace fs = std::filesystem;

namespace transfer {

// 使用 libssh2 执行真正的 SCP 上传
bool ScpTransfer::transfer(const std::string& src, const std::string& dstDir) {
    auto logger = zexuan::Logger::getFileLogger("plugin/transfer_plugin");
    fs::path srcPath(src);

    if (!fs::exists(srcPath)) {
        logger->error("SCP: source not exists: {}", src);
        return false;
    }

    // 连接参数（可用环境变量覆盖）
    const char* host = std::getenv("ZEXUAN_SSH_HOST");
    const char* portEnv = std::getenv("ZEXUAN_SSH_PORT");
    const char* user = std::getenv("ZEXUAN_SSH_USER");
    const char* pass = std::getenv("ZEXUAN_SSH_PASS");
    std::string hostStr = host ? host : "zx.zexuan.online";
    int port = portEnv ? std::atoi(portEnv) : 3422;
    std::string userStr = user ? user : "root";
    std::string passStr = pass ? pass : "!furryzexuan0";

    SSHClient client;
    std::string err;
    if (!client.connectAndAuth(hostStr, port, userStr, passStr, err)) {
        logger->error("SCP connect/auth failed: {}", err);
        return false;
    }

    // 文件或目录
    if (fs::is_regular_file(srcPath)) {
        bool ok = client.scpUploadFile(srcPath.string(), dstDir, err);
        if (!ok) logger->error("SCP upload failed: {} -> {}: {}", srcPath.string(), dstDir, err);
        return ok;
    }

    // 目录：非递归上传目录下的所有文件
    auto files = listFiles(srcPath);
    if (files.empty()) {
        logger->warn("SCP: no files in directory: {}", src);
    }
    bool okAll = true;
    for (auto& f : files) {
        bool ok = client.scpUploadFile(f.string(), dstDir, err);
        if (!ok) { logger->error("SCP upload failed: {} -> {}: {}", f.string(), dstDir, err); okAll = false; }
    }
    return okAll;
}

} // namespace transfer


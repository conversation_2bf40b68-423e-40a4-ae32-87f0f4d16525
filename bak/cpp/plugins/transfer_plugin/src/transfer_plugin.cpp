#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/plugin/plugin_export.hpp"
#include "zexuan/base/mediator.hpp"
#include "../include/message_transfer.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/base/message_types.hpp"
#include "zexuan/logger.hpp"
#include "../include/transfer_factory.hpp"
#include <filesystem>
#include <mutex>

namespace fs = std::filesystem;
using namespace zexuan;

class TransferPlugin : public zexuan::plugin::PluginBase {
private:
    std::mutex opMutex_;

public:
    TransferPlugin(std::shared_ptr<zexuan::base::Mediator> mediator, int pluginId, const std::string& name)
        : PluginBase(mediator, pluginId, name) {
        transfer::TransferRegistrar::registerAll();
    }

    bool initialize() override {
        auto logger = Logger::getFileLogger("plugin/transfer_plugin");
        logger->info("TransferPlugin {} initializing...", getPluginId());
        return true;
    }

    void shutdown() override {
        auto logger = Logger::getFileLogger("plugin/transfer_plugin");
        logger->info("TransferPlugin {} shutting down...", getPluginId());
    }

protected:
    void processMessage(const zexuan::base::Message& message) override {
        auto logger = Logger::getFileLogger("plugin/transfer_plugin");
        logger->debug("TransferPlugin {} received message: TYP={}, COT={}, Source={}, Target={}, FUN={}",
                      getPluginId(),
                      static_cast<int>(message.getTyp()),
                      static_cast<int>(message.getCot()),
                      static_cast<int>(message.getSource()),
                      static_cast<int>(message.getTarget()),
                      static_cast<int>(message.getFun()));

        // 处理插件信息请求
        if (message.getCot() == static_cast<uint8_t>(zexuan::base::CauseOfTransmission::INFO_REQUEST)) {
            logger->info("Plugin Name: {}, ID: {}, Description: 文件传输插件 - 支持SCP/SFTP文件传输", 
                        getPluginName(), getPluginId());
            
            // 创建回复消息，交换source和target
            zexuan::base::Message responseMessage = message;
            responseMessage.swapSourceTarget();
            responseMessage.setCot(static_cast<uint8_t>(zexuan::base::CauseOfTransmission::INFO_RESPONSE));
            
            // 设置JSON响应内容
            std::string jsonResponse = zexuan::plugin::transfer::getAvailableMessagesJson();
            responseMessage.setTextContent(jsonResponse);
            
            // 发送回复给服务器
            try {
                int result = sendPluginMessageToTarget(responseMessage, "server");
                if (result == 0) {
                    logger->info("Successfully sent INFO_RESPONSE to server");
                } else {
                    logger->error("Failed to send INFO_RESPONSE to server: error code {}", result);
                }
            } catch (const std::exception& e) {
                logger->error("Error sending INFO_RESPONSE to server: {}", e.what());
            }
            return;
        }

        // 处理传输消息: SCP或SFTP
        if (message.getCot() == static_cast<uint8_t>(zexuan::base::CauseOfTransmission::ACTIVATION) && 
            (message.getFun() == zexuan::base::FunctionType::TRANSFER_SCP || 
             message.getFun() == zexuan::base::FunctionType::TRANSFER_SFTP)) {
            handleTransfer(message);
        }
    }

private:
    void handleTransfer(const zexuan::base::Message& message) {
        auto logger = Logger::getFileLogger("plugin/transfer_plugin");
        std::lock_guard<std::mutex> lk(opMutex_);

        const uint8_t func = message.getFun();
        std::string proto = (func == zexuan::base::FunctionType::TRANSFER_SCP) ? "scp" : "sftp";

        // 本地目录从消息可变结构提取（与 filename 插件一致），远端目录固定为 /tmp
        std::string src = message.getTextContent();
        std::string dst = "/tmp";

        try {
            if (src.empty()) {
                logger->error("No local path provided in message variable structure");
                return;
            }
            if (!fs::exists(src)) {
                logger->error("Local path not found: {}", src);
                return;
            }

            auto transfer = transfer::TransferFactory::create(proto);
            if (!transfer) {
                logger->error("No transfer implementation registered for: {}", proto);
                return;
            }

            logger->info("Start {} transfer: {} -> {}", proto, src, dst);
            bool ok = transfer->transfer(src, dst);
            if (ok) {
                logger->info("{} transfer completed: {} -> {}", proto, src, dst);
            } else {
                logger->error("{} transfer failed: {} -> {}", proto, src, dst);
            }
        } catch (const std::exception& e) {
            logger->error("Transfer error: {}", e.what());
        }
    }
};

ZEXUAN_PLUGIN_EXPORT(TransferPlugin)

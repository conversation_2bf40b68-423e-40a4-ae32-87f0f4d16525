#include "../include/transfer_base.hpp"
#include "zexuan/logger.hpp"
#include <fstream>

namespace fs = std::filesystem;

namespace transfer {

std::vector<fs::path> TransferBase::listFiles(const fs::path& dir) {
    std::vector<fs::path> files;
    if (!fs::exists(dir) || !fs::is_directory(dir)) {
        return files;
    }
    for (auto& p : fs::directory_iterator(dir)) {
        if (fs::is_regular_file(p.path())) {
            files.emplace_back(p.path());
        }
    }
    return files;
}

bool TransferBase::ensureDir(const fs::path& dir) {
    try {
        if (!fs::exists(dir)) {
            fs::create_directories(dir);
        }
        return true;
    } catch (const std::exception& e) {
        auto logger = zexuan::Logger::getFileLogger("plugin/transfer_plugin");
        logger->error("Failed to create directory {}: {}", dir.string(), e.what());
        return false;
    }
}

bool TransferBase::copyLocal(const fs::path& src, const fs::path& dstDir) {
    auto logger = zexuan::Logger::getFileLogger("plugin/transfer_plugin");
    try {
        if (!ensureDir(dstDir)) return false;
        fs::path dst = dstDir / src.filename();
        fs::copy_file(src, dst, fs::copy_options::overwrite_existing);
        logger->info("Local copy: {} -> {}", src.string(), dst.string());
        return true;
    } catch (const std::exception& e) {
        logger->error("Local copy failed: {} -> {}: {}", src.string(), (dstDir / src.filename()).string(), e.what());
        return false;
    }
}

} // namespace transfer


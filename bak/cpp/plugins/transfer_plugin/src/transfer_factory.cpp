#include "../include/transfer_factory.hpp"
#include "../include/scp_transfer.hpp"
#include "../include/sftp_transfer.hpp"

namespace transfer {

void TransferRegistrar::registerAll() {
    // 注册两个传输类型，名称小写
    ZEXUAN_REGISTER_AS(TransferFactory, ScpTransfer, "scp");
    ZEXUAN_REGISTER_AS(TransferFactory, SftpTransfer, "sftp");
}

std::vector<std::string> TransferRegistrar::getRegistered() {
    return TransferFactory::getRegisteredNames();
}

bool TransferRegistrar::isRegistered(const std::string& name) {
    return TransferFactory::isRegistered(name);
}

} // namespace transfer


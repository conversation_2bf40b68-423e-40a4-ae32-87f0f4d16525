# transfer Plugin CMakeLists.txt

# 创建 transfer_plugin 动态库
add_library(transfer_plugin SHARED
    src/transfer_plugin.cpp
    src/transfer_base.cpp
    src/transfer_factory.cpp
    src/scp_transfer.cpp
    src/sftp_transfer.cpp
    src/ssh_client.cpp
)

# 链接必要的库
find_package(Libssh2 REQUIRED)

target_link_libraries(transfer_plugin
    PRIVATE
        plugin_interface
        Libssh2::libssh2
)

# 设置包含目录
target_include_directories(transfer_plugin PRIVATE
    ${CMAKE_SOURCE_DIR}/core/include
    ${CMAKE_SOURCE_DIR}/interface/include
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 设置输出目录
set_target_properties(transfer_plugin PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/libs/plugins"
    VERSION 1.0.0
    SOVERSION 1
)

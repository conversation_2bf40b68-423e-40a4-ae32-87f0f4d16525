
策略模式：不同算法/行为切换（例如日志输出格式、序列化方式）。
    何时用/不用
    用：算法经常互换、希望解耦“选择”和“实现”、遵循开闭原则
    慎用：策略太多且差异很小（可能用参数化更简单）、对象创建/切换成本敏感（可考虑对象池或模板策略）


observer和subject为了性能考虑主线程应该直接发送消息传入队列，每个都有任务进行线程进行处理


已使用的设计模式
    观察者 + 中介者
    Reactor（反应堆，网络事件驱动）
    工厂 + 自动注册 + 单例（Factory + AutoRegister + Singleton）
    单例（Singleton）
    适配器（Adapter）   router 转换信息
    外观（Facade）      systemmanager
    模板方法（Template Method，弱形式） pluginbase
    命令风格（Command-like）    message

可加强的
    责任链/中间件（Chain of Responsibility / Middleware）
        场景：网络入站/出站管线（鉴权、限流、解压/解密、校验、解析、路由）
        做法：在 MessageRouter 增加可插拔中间件数组 Middlewares，约定签名 middleware(ctx, next)。节点可短路（不调 next）
        收益：把“可选前置/后置逻辑”从核心解析/路由里抽离，易于按需开关与测试

    构建者（Builder）
        场景：构造 Message 时字段较多、容易出错
        做法：提供 MessageBuilder 连缀 API（setTyp/vsq/cot/source/target/fun/inf/setTextContent/build）
        收益：显著提升可读性与正确性，减少“长度/字段漏填”类错误

    策略（Strategy）
        场景：编解码/压缩/加密算法、重试/退避、负载均衡、路由规则
        做法：抽象 ICodec/ICompressor/IRetryPolicy 等接口，MessageRouter 使用注入的策略实例；NetworkManager 的重试/LB 也可策略化
        收益：新增算法/规则只需增加策略实现，符合开闭原则



精简网络库：
    timestamp.h可以使用chrono替代
    stringpiece.h可以用string_view替代
    
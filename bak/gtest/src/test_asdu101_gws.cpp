#include "test_asdu_gws.h"
#include <memory>

// ASDU101测试夹具类
class TNXEcProAsdu101GWSTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        // 创建Mock对象
        mockModelSeek = std::make_unique<MockModelSeek>();
        mockLogRecord = std::make_unique<MockLogRecord>();
        
        // 创建测试对象
        testAsdu101 = std::make_unique<TestTNXEcProAsdu101GWS>(
            mockModelSeek.get(), mockLogRecord.get());
        
        // 设置默认的Mock行为
        SetupDefaultMockBehavior();
        
        // 创建测试文件结构
        TestUtils::CreateTestFileStructure("/tmp/test_files");
    }
    
    void TearDown() override
    {
        // 清理测试文件
        TestUtils::CleanupTestFiles("/tmp/test_files");
        
        // 重置对象
        testAsdu101.reset();
        mockLogRecord.reset();
        mockModelSeek.reset();
    }
    
    void SetupDefaultMockBehavior()
    {
        // 设置GetBasicCfg的默认行为
        ON_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
            .WillByDefault(::testing::Invoke([](BASIC_CFG_TB& cfg) {
                cfg.str_file_path = "/tmp/test_files";
                cfg.str_logroot_path = "/tmp/test_logs";
                return true;
            }));
        
        // 设置GetSubStationBasicCfg的默认行为
        static SUBSTATION_TB stationCfg;
        stationCfg.n_outaddr103 = 1;
        stationCfg.str_aliasname = "TestStation";
        
        ON_CALL(*mockModelSeek, GetSubStationBasicCfg())
            .WillByDefault(::testing::Return(&stationCfg));
        
        // 设置基类方法的默认行为
        ON_CALL(*testAsdu101, __GetTimeFormProFrameBody(::testing::_, ::testing::_))
            .WillByDefault(::testing::Invoke([](PRO_FRAME_BODY* pBody, ASDU_TIME& asduTime) {
                // 模拟从报文中提取时间
                if (pBody && pBody->vVarData.size() >= 16) {
                    memcpy(&asduTime.nInfoHappenUtc, &pBody->vVarData[0], 4);
                    memcpy(&asduTime.nInfoRcvUtc, &pBody->vVarData[8], 4);
                    asduTime.nInfoHappenMs = 0;
                    asduTime.nInfoRcvMs = 0;
                }
                return 0;
            }));
        
        ON_CALL(*testAsdu101, FormatGeneralFilesListBodyofAsdu102(::testing::_, ::testing::_, ::testing::_, ::testing::_))
            .WillByDefault(::testing::Invoke([](ASDU_TIME asduTime, PRO_FRAME_BODY* pBody, 
                                               FILE_PROPERTY_INF_LIST& fileList, PRO_FRAME_BODY_LIST& result) {
                // 模拟格式化文件列表为ASDU102报文
                PRO_FRAME_BODY body;
                body.nType = 0x66; // ASDU102类型
                body.nAddr = pBody->nAddr;
                body.nRii = pBody->nRii;
                
                // 模拟文件列表数据
                body.vVarData.resize(50 + fileList.size() * 20, 0x55);
                result.push_back(body);
                return 0;
            }));
        
        // 设置日志记录的默认行为（允许任意调用）
        EXPECT_CALL(*mockLogRecord, RecordTraceLog(::testing::_, ::testing::_))
            .Times(::testing::AnyNumber());
        EXPECT_CALL(*mockLogRecord, RecordErrorLog(::testing::_, ::testing::_))
            .Times(::testing::AnyNumber());
    }

protected:
    std::unique_ptr<MockModelSeek> mockModelSeek;
    std::unique_ptr<MockLogRecord> mockLogRecord;
    std::unique_ptr<TestTNXEcProAsdu101GWS> testAsdu101;
};

// 测试DirectResFromLocal到__QueryGeneralFilesList_SettingUp的完整调用链 - 成功场景
TEST_F(TNXEcProAsdu101GWSTest, DirectResFromLocal_SettingUp_Success)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateAsdu101SettingUpFrameBody(1, "/SettingUp/test.txt");
    PRO_FRAME_BODY_LIST result;
    
    // 设置期望的Mock调用
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .Times(1)
        .WillOnce(::testing::Invoke([](BASIC_CFG_TB& cfg) {
            cfg.str_file_path = "/tmp/test_files";
            return true;
        }));
    
    EXPECT_CALL(*testAsdu101, __GetTimeFormProFrameBody(::testing::_, ::testing::_))
        .Times(1)
        .WillOnce(::testing::Invoke([](PRO_FRAME_BODY* pBody, ASDU_TIME& asduTime) {
            asduTime.nInfoHappenUtc = 1640995200;
            asduTime.nInfoRcvUtc = 1672531200;
            asduTime.nInfoHappenMs = 0;
            asduTime.nInfoRcvMs = 0;
            return 0;
        }));
    
    EXPECT_CALL(*testAsdu101, FormatGeneralFilesListBodyofAsdu102(::testing::_, ::testing::_, ::testing::_, ::testing::_))
        .Times(1)
        .WillOnce(::testing::Invoke([](ASDU_TIME asduTime, PRO_FRAME_BODY* pBody, 
                                       FILE_PROPERTY_INF_LIST& fileList, PRO_FRAME_BODY_LIST& result) {
            PRO_FRAME_BODY body;
            body.nType = 0x66;
            body.nAddr = 1;
            body.vVarData.resize(100, 0xAA);
            result.push_back(body);
            return 0;
        }));
    
    // 期望的日志调用
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("通用文件列表召唤处理"), 
        ::testing::StrEq("TNXEcProAsdu101GWS")))
        .Times(1);
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(
        ::testing::HasSubstr("发现文件名包含SettingUp"), 
        ::testing::StrEq("TNXEcProAsdu101GWS")))
        .Times(1);
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("搜索路径"), 
        ::testing::StrEq("TNXEcProAsdu101GWS")))
        .Times(1);
    
    // 执行测试
    int ret = testAsdu101->DirectResFromLocal(&testBody, result);
    
    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_SUCCESS);
    EXPECT_EQ(result.size(), 1);
    EXPECT_EQ(result[0].nType, 0x66); // ASDU102类型
    EXPECT_EQ(result[0].nAddr, 1);
    EXPECT_EQ(result[0].vVarData.size(), 100);
}

// 测试DirectResFromLocal - 报文长度不够的情况
TEST_F(TNXEcProAsdu101GWSTest, DirectResFromLocal_InsufficientLength)
{
    // 准备测试数据 - 长度不够115
    PRO_FRAME_BODY testBody = TestUtils::CreateAsdu101SettingUpFrameBody(1, "test.txt");
    testBody.vVarData.resize(50); // 长度不够115
    PRO_FRAME_BODY_LIST result;
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("通用文件列表召唤处理"), 
        ::testing::StrEq("TNXEcProAsdu101GWS")))
        .Times(1);
    
    // 执行测试
    int ret = testAsdu101->DirectResFromLocal(&testBody, result);
    
    // 验证结果 - 应该跳过SettingUp处理，返回成功
    EXPECT_EQ(ret, EC_PRO_CVT_SUCCESS);
}

// 测试DirectResFromLocal - 空指针情况
TEST_F(TNXEcProAsdu101GWSTest, DirectResFromLocal_NullPointer)
{
    PRO_FRAME_BODY_LIST result;
    
    // 执行测试
    int ret = testAsdu101->DirectResFromLocal(nullptr, result);
    
    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_FAIL);
    EXPECT_EQ(result.size(), 0);
}

// 测试DirectResFromLocal - 非SettingUp文件名的处理
TEST_F(TNXEcProAsdu101GWSTest, DirectResFromLocal_NonSettingUp)
{
    // 准备测试数据 - 不包含SettingUp关键字
    PRO_FRAME_BODY testBody = TestUtils::CreateAsdu101NormalFrameBody(1, "normal_file.txt");
    PRO_FRAME_BODY_LIST result;
    
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("通用文件列表召唤处理"), 
        ::testing::StrEq("TNXEcProAsdu101GWS")))
        .Times(1);
    
    // 执行测试
    int ret = testAsdu101->DirectResFromLocal(&testBody, result);
    
    // 验证结果 - 应该跳过SettingUp处理，返回成功
    EXPECT_EQ(ret, EC_PRO_CVT_SUCCESS);
}

// 测试__QueryGeneralFilesList_SettingUp - 获取配置失败
TEST_F(TNXEcProAsdu101GWSTest, QueryGeneralFilesList_SettingUp_GetConfigFailed)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateAsdu101SettingUpFrameBody(1, "/SettingUp/test.txt");
    FILE_PROPERTY_INF_LIST fileList;
    ASDU_TIME asduTime;
    asduTime.nInfoHappenUtc = 1640995200;
    asduTime.nInfoRcvUtc = 1672531200;
    
    // 设置Mock行为 - 获取配置失败
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .Times(1)
        .WillOnce(::testing::Return(false));
    
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(
        ::testing::HasSubstr("获取系统基本配置表信息失败"), 
        ::testing::StrEq("TNXEcProAsdu101GWS")))
        .Times(1);
    
    // 执行测试
    int ret = testAsdu101->__QueryGeneralFilesList_SettingUp(asduTime, "/test.txt", &testBody, fileList);
    
    // 验证结果
    EXPECT_EQ(ret, -1);
    EXPECT_EQ(fileList.size(), 0);
}

// 测试__QueryGeneralFilesList_SettingUp - 成功查询文件列表
TEST_F(TNXEcProAsdu101GWSTest, QueryGeneralFilesList_SettingUp_Success)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateAsdu101SettingUpFrameBody(2, "/SettingUp/config.xml");
    FILE_PROPERTY_INF_LIST fileList;
    ASDU_TIME asduTime;
    asduTime.nInfoHappenUtc = 1640995200;
    asduTime.nInfoRcvUtc = 1672531200;

    // 设置期望的Mock调用
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .Times(1)
        .WillOnce(::testing::Invoke([](BASIC_CFG_TB& cfg) {
            cfg.str_file_path = "/tmp/test_files";
            return true;
        }));

    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("搜索路径"),
        ::testing::StrEq("TNXEcProAsdu101GWS")))
        .Times(1);

    // 执行测试
    int ret = testAsdu101->__QueryGeneralFilesList_SettingUp(asduTime, "/config.xml", &testBody, fileList);

    // 验证结果
    EXPECT_EQ(ret, 0);
    EXPECT_EQ(fileList.size(), 1);
    EXPECT_EQ(fileList[0].strFileName, "setting1.txt");
    EXPECT_EQ(fileList[0].nFileSize, 1024);
    EXPECT_EQ(fileList[0].nCreateTime, asduTime.nInfoHappenUtc);
}

// 测试DirectResFromLocal - __GetTimeFormProFrameBody失败
TEST_F(TNXEcProAsdu101GWSTest, DirectResFromLocal_SettingUp_GetTimeFailed)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateAsdu101SettingUpFrameBody(1, "/SettingUp/test.txt");
    PRO_FRAME_BODY_LIST result;

    // 设置Mock行为 - __GetTimeFormProFrameBody返回失败
    EXPECT_CALL(*testAsdu101, __GetTimeFormProFrameBody(::testing::_, ::testing::_))
        .Times(1)
        .WillOnce(::testing::Return(-1));

    EXPECT_CALL(*mockLogRecord, RecordErrorLog(
        ::testing::HasSubstr("发现文件名包含SettingUp"),
        ::testing::StrEq("TNXEcProAsdu101GWS")))
        .Times(1);

    // 执行测试
    int ret = testAsdu101->DirectResFromLocal(&testBody, result);

    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_FAIL);
}

// 测试DirectResFromLocal - FormatGeneralFilesListBodyofAsdu102失败
TEST_F(TNXEcProAsdu101GWSTest, DirectResFromLocal_SettingUp_FormatFailed)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateAsdu101SettingUpFrameBody(1, "/SettingUp/test.txt");
    PRO_FRAME_BODY_LIST result;

    // 设置Mock行为
    EXPECT_CALL(*testAsdu101, __GetTimeFormProFrameBody(::testing::_, ::testing::_))
        .Times(1)
        .WillOnce(::testing::Return(0));

    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .Times(1)
        .WillOnce(::testing::Return(true));

    EXPECT_CALL(*testAsdu101, FormatGeneralFilesListBodyofAsdu102(::testing::_, ::testing::_, ::testing::_, ::testing::_))
        .Times(1)
        .WillOnce(::testing::Return(-1)); // 返回失败

    // 执行测试
    int ret = testAsdu101->DirectResFromLocal(&testBody, result);

    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_FAIL);
}

// 测试_strstr_nocase函数 - 不区分大小写匹配（ASDU101版本）
TEST_F(TNXEcProAsdu101GWSTest, StrStr_NoCase_Function)
{
    // 测试不区分大小写匹配
    char* result1 = testAsdu101->_strstr_nocase("This is a SETTINGUP test", "settingup");
    EXPECT_NE(result1, nullptr);
    EXPECT_STREQ(result1, "SETTINGUP test");

    char* result2 = testAsdu101->_strstr_nocase("No match here", "settingup");
    EXPECT_EQ(result2, nullptr);

    char* result3 = testAsdu101->_strstr_nocase("SettingUp", "SETTINGUP");
    EXPECT_NE(result3, nullptr);
    EXPECT_STREQ(result3, "SettingUp");

    // 测试空字符串
    char* result4 = testAsdu101->_strstr_nocase("test", "");
    EXPECT_NE(result4, nullptr);
    EXPECT_STREQ(result4, "test");
}

// 测试DirectResFromLocal - 完整的调用链验证（集成测试）
TEST_F(TNXEcProAsdu101GWSTest, DirectResFromLocal_SettingUp_FullCallChain)
{
    // 准备测试数据
    PRO_FRAME_BODY testBody = TestUtils::CreateAsdu101SettingUpFrameBody(1, "/SettingUp/integration_test.txt");
    PRO_FRAME_BODY_LIST result;

    // 设置详细的Mock期望，验证完整调用链
    ::testing::InSequence seq;

    // 1. DirectResFromLocal应该首先记录通用文件列表召唤处理的日志
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("通用文件列表召唤处理"),
        ::testing::StrEq("TNXEcProAsdu101GWS")));

    // 2. 然后记录发现SettingUp的日志
    EXPECT_CALL(*mockLogRecord, RecordErrorLog(
        ::testing::HasSubstr("发现文件名包含SettingUp"),
        ::testing::StrEq("TNXEcProAsdu101GWS")));

    // 3. 调用__GetTimeFormProFrameBody提取时间
    EXPECT_CALL(*testAsdu101, __GetTimeFormProFrameBody(::testing::_, ::testing::_))
        .WillOnce(::testing::Invoke([](PRO_FRAME_BODY* pBody, ASDU_TIME& asduTime) {
            asduTime.nInfoHappenUtc = 1640995200;
            asduTime.nInfoRcvUtc = 1672531200;
            asduTime.nInfoHappenMs = 0;
            asduTime.nInfoRcvMs = 0;
            return 0;
        }));

    // 4. __QueryGeneralFilesList_SettingUp应该获取基本配置
    EXPECT_CALL(*mockModelSeek, GetBasicCfg(::testing::_))
        .WillOnce(::testing::Invoke([](BASIC_CFG_TB& cfg) {
            cfg.str_file_path = "/tmp/test_files";
            return true;
        }));

    // 5. 记录搜索路径的日志
    EXPECT_CALL(*mockLogRecord, RecordTraceLog(
        ::testing::HasSubstr("搜索路径"),
        ::testing::StrEq("TNXEcProAsdu101GWS")));

    // 6. 调用FormatGeneralFilesListBodyofAsdu102格式化结果
    EXPECT_CALL(*testAsdu101, FormatGeneralFilesListBodyofAsdu102(::testing::_, ::testing::_, ::testing::_, ::testing::_))
        .WillOnce(::testing::Invoke([](ASDU_TIME asduTime, PRO_FRAME_BODY* pBody,
                                       FILE_PROPERTY_INF_LIST& fileList, PRO_FRAME_BODY_LIST& result) {
            // 模拟成功格式化
            PRO_FRAME_BODY body1, body2;
            body1.nType = 0x66;
            body1.nAddr = 1;
            body1.vVarData.resize(64, 0xCC);

            body2.nType = 0x66;
            body2.nAddr = 1;
            body2.vVarData.resize(32, 0xDD);

            result.push_back(body1);
            result.push_back(body2);
            return 0;
        }));

    // 执行测试
    int ret = testAsdu101->DirectResFromLocal(&testBody, result);

    // 验证结果
    EXPECT_EQ(ret, EC_PRO_CVT_SUCCESS);
    EXPECT_EQ(result.size(), 2);
    EXPECT_EQ(result[0].vVarData.size(), 64);
    EXPECT_EQ(result[1].vVarData.size(), 32);

    // 验证数据内容
    for (auto& byte : result[0].vVarData) {
        EXPECT_EQ(byte, 0xCC);
    }
    for (auto& byte : result[1].vVarData) {
        EXPECT_EQ(byte, 0xDD);
    }
}

// 注意：main函数在test_asdu103_gws.cpp中定义，这里不需要重复定义

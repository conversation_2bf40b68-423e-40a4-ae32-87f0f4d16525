#include "HotReloadServer.h"
#include <signal.h>
#include <chrono>
#include <sstream>

namespace zexuan {
namespace server {

HotReloadServer::HotReloadServer(const ServerConfig& config)
    : config_(config)
    , running_(false)
    , initialized_(false)
    , logger_(Logger::getFileLogger("HotReloadServer"))
    , totalConnections_(0)
    , totalMessages_(0)
    , startTime_(std::chrono::system_clock::now()) {

    logger_->info("HotReloadServer created with config: {}:{}", config_.host, config_.port);
}

HotReloadServer::~HotReloadServer() {
    stop();
}

bool HotReloadServer::start() {
    if (running_.load()) {
        logger_->warn("Server is already running");
        return false;
    }

    logger_->info("Starting HotReloadServer...");

    if (!initialize()) {
        logger_->error("Failed to initialize server");
        return false;
    }

    if (!startNetworkThread()) {
        logger_->error("Failed to start network thread");
        cleanup();
        return false;
    }

    if (!startHotReloadThread()) {
        logger_->error("Failed to start hot reload thread");
        cleanup();
        return false;
    }

    running_.store(true);
    logger_->info("HotReloadServer started successfully on {}:{}", config_.host, config_.port);
    return true;
}

void HotReloadServer::stop() {
    if (!running_.load()) {
        return;
    }

    logger_->info("Stopping HotReloadServer...");
    running_.store(false);

    // 关闭所有插件
    if (pluginManager_) {
        pluginManager_->shutdownAllPlugins();
    }

    // 停止文件监控
    if (fileWatcher_) {
        fileWatcher_->stopWatching();
    }

    // 停止网络服务
    if (networkLoop_) {
        networkLoop_->quit();
    }

    // 等待线程结束
    if (hotReloadThread_.joinable()) {
        hotReloadThread_.join();
    }

    if (networkThread_.joinable()) {
        networkThread_.join();
    }

    cleanup();
    logger_->info("HotReloadServer stopped");
}

void HotReloadServer::waitForStop() {
    std::unique_lock<std::mutex> lock(stateMutex_);
    stateCondition_.wait(lock, [this] { return !running_.load(); });
}

std::string HotReloadServer::getStatistics() const {
    std::ostringstream oss;

    auto now = std::chrono::system_clock::now();
    auto uptime = std::chrono::duration_cast<std::chrono::seconds>(now - startTime_);

    oss << "{\n";
    oss << "  \"status\": \"" << (running_.load() ? "running" : "stopped") << "\",\n";
    oss << "  \"uptime_seconds\": " << uptime.count() << ",\n";
    oss << "  \"total_connections\": " << totalConnections_.load() << ",\n";
    oss << "  \"current_connections\": " << getConnectionCount() << ",\n";
    oss << "  \"total_messages\": " << totalMessages_.load() << ",\n";
    oss << "  \"plugin_manager\": " << pluginManager_->getStatistics() << "\n";
    oss << "}";

    return oss.str();
}

bool HotReloadServer::manualReloadPlugin(const std::string& pluginName) {
    if (!pluginManager_) {
        logger_->error("Plugin manager not available");
        return false;
    }

    // 构建插件路径
    std::string libPath = config_.pluginPath + "/lib" + pluginName + ".so";

    logger_->info("Manual reload requested for plugin: {}", pluginName);
    return pluginManager_->hotReloadPlugin(libPath);
}

size_t HotReloadServer::getConnectionCount() const {
    std::lock_guard<std::mutex> lock(connectionsMutex_);
    return connections_.size();
}

bool HotReloadServer::initializePluginSystem(const std::string& configPath) {
    // 如果服务器组件还没有初始化，先初始化
    if (!pluginManager_) {
        if (!initialize()) {
            logger_->error("Failed to initialize server components");
            return false;
        }
    }

    try {
        logger_->info("Initializing plugin system from config: {}", configPath);

        // 1. 从配置文件加载插件
        if (!pluginManager_->loadPluginsFromConfig(configPath)) {
            logger_->error("Failed to load plugins from config");
            return false;
        }

        // 2. 初始化所有插件
        if (!pluginManager_->initializeAllPlugins()) {
            logger_->warn("Some plugins failed to initialize");
            // 不返回 false，因为部分插件失败不应该阻止服务器启动
        }

        logger_->info("Plugin system initialized successfully");
        return true;

    } catch (const std::exception& e) {
        logger_->error("Exception during plugin system initialization: {}", e.what());
        return false;
    }
}

bool HotReloadServer::initialize() {
    if (initialized_.load()) {
        return true;
    }

    try {
        // 创建消息中介器
        mediator_ = std::make_shared<base::BaseMediator>();
        if (!mediator_->initialize()) {
            throw std::runtime_error("Failed to initialize mediator");
        }

        // 创建插件管理器
        pluginManager_ = std::make_shared<ThreadSafePluginManager>(mediator_);
        pluginManager_->setPluginChangeCallback(
            [this](const std::string& pluginName, const std::string& action) {
                onPluginChange(pluginName, action);
            });

        // 创建消息处理器
        messageProcessor_ = std::make_unique<MessageProcessor>(pluginManager_, mediator_);

        // 创建文件系统监控器
        fileWatcher_ = std::make_unique<FileSystemWatcher>(config_.pluginPath, config_.debounceMs);

        // 创建网络事件循环
        networkLoop_ = std::make_unique<net::EventLoop>();

        // 创建 TCP 服务器
        net::InetAddress serverAddr(config_.port);
        tcpServer_ = std::make_unique<net::TcpServer>(
            networkLoop_.get(),
            serverAddr,
            "HotReloadServer"
        );

        // 设置网络回调
        tcpServer_->setConnectionCallback(
            [this](const net::TcpConnectionPtr& conn) {
                onConnection(conn);
            });

        tcpServer_->setMessageCallback(
            [this](const net::TcpConnectionPtr& conn, net::Buffer* buf, Timestamp receiveTime) {
                onMessage(conn, buf, receiveTime);
            });

        initialized_.store(true);
        logger_->info("Server components initialized successfully");
        return true;

    } catch (const std::exception& e) {
        logger_->error("Exception during initialization: {}", e.what());
        return false;
    }
}

bool HotReloadServer::startNetworkThread() {
    try {
        networkThread_ = std::thread(&HotReloadServer::networkThreadFunc, this);

        logger_->info("Network thread started");
        return true;

    } catch (const std::exception& e) {
        logger_->error("Failed to start network thread: {}", e.what());
        return false;
    }
}

bool HotReloadServer::startHotReloadThread() {
    try {
        hotReloadThread_ = std::thread(&HotReloadServer::hotReloadThreadFunc, this);

        logger_->info("Hot reload thread started");
        return true;

    } catch (const std::exception& e) {
        logger_->error("Failed to start hot reload thread: {}", e.what());
        return false;
    }
}

void HotReloadServer::networkThreadFunc() {
    logger_->info("Network thread running");

    try {
        // 启动 TCP 服务器
        tcpServer_->start();

        // 运行事件循环
        networkLoop_->loop();

    } catch (const std::exception& e) {
        logger_->error("Exception in network thread: {}", e.what());
    }

    logger_->info("Network thread stopped");
}

void HotReloadServer::hotReloadThreadFunc() {
    logger_->info("Hot reload thread running");

    try {
        // 启动文件系统监控
        bool watchStarted = fileWatcher_->startWatching(
            [this](const std::string& filePath, const std::string& eventType) {
                onFileChange(filePath, eventType);
            });

        if (!watchStarted) {
            logger_->error("Failed to start file system watching");
            return;
        }

        // 保持线程运行直到停止信号
        while (running_.load()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }

    } catch (const std::exception& e) {
        logger_->error("Exception in hot reload thread: {}", e.what());
    }

    logger_->info("Hot reload thread stopped");
}

void HotReloadServer::onConnection(const net::TcpConnectionPtr& conn) {
    if (conn->connected()) {
        logger_->info("Client connected: {}", conn->peerAddress().toIpPort());
        totalConnections_.fetch_add(1);

        {
            std::lock_guard<std::mutex> lock(connectionsMutex_);
            connections_.insert(conn);
        }

        // 发送欢迎消息和插件列表
        sendPluginList(conn);

    } else {
        logger_->info("Client disconnected: {}", conn->peerAddress().toIpPort());

        {
            std::lock_guard<std::mutex> lock(connectionsMutex_);
            connections_.erase(conn);
        }
    }
}

void HotReloadServer::onMessage(const net::TcpConnectionPtr& conn,
                               net::Buffer* buf,
                               Timestamp receiveTime) {
    totalMessages_.fetch_add(1);

    try {
        // 读取所有可用数据
        std::string data = buf->retrieveAllAsString();

        if (data.empty()) {
            logger_->warn("Received empty message from {}", conn->peerAddress().toIpPort());
            return;
        }

        logger_->debug("Received message from {}: {} bytes",
                      conn->peerAddress().toIpPort(), data.size());

        // 将字符串数据转换为字节向量
        std::vector<uint8_t> buffer(data.begin(), data.end());

        // 处理消息
        messageProcessor_->processRawMessage(buffer,
            [this, conn](const base::Message& response) {
                // 序列化响应消息
                std::vector<uint8_t> responseBuffer;
                response.serialize(responseBuffer);

                // 发送响应
                std::string responseStr(responseBuffer.begin(), responseBuffer.end());
                conn->send(responseStr);

                logger_->debug("Sent response to {}: {} bytes",
                              conn->peerAddress().toIpPort(), responseStr.size());
            });

    } catch (const std::exception& e) {
        logger_->error("Exception processing message from {}: {}",
                      conn->peerAddress().toIpPort(), e.what());
        sendErrorResponse(conn, "Message processing failed");
    }
}

void HotReloadServer::onFileChange(const std::string& filePath, const std::string& eventType) {
    logger_->info("File change detected: {} - {}", filePath, eventType);

    if (eventType == "modified" || eventType == "created") {
        // 尝试热加载插件
        bool success = pluginManager_->hotReloadPlugin(filePath);

        if (success) {
            logger_->info("Successfully hot reloaded plugin from: {}", filePath);
        } else {
            logger_->error("Failed to hot reload plugin from: {}", filePath);
        }
    } else if (eventType == "deleted") {
        // 提取插件名并卸载
        std::filesystem::path path(filePath);
        std::string filename = path.filename().string();

        // 移除 lib 前缀和 .so 后缀
        std::regex pattern(R"(^lib(.+?)\.so.*$)");
        std::smatch matches;

        if (std::regex_match(filename, matches, pattern)) {
            std::string pluginName = matches[1].str();

            bool success = pluginManager_->unloadPlugin(pluginName);
            if (success) {
                logger_->info("Successfully unloaded deleted plugin: {}", pluginName);
            } else {
                logger_->error("Failed to unload deleted plugin: {}", pluginName);
            }
        }
    }
}

void HotReloadServer::onPluginChange(const std::string& pluginName, const std::string& action) {
    logger_->info("Plugin change notification: {} - {}", pluginName, action);

    // 创建系统通知消息
    auto notification = messageProcessor_->createSystemNotification("plugin_" + action, pluginName);

    // 广播给所有连接的客户端
    std::vector<uint8_t> notificationBuffer;
    notification.serialize(notificationBuffer);
    std::string notificationStr(notificationBuffer.begin(), notificationBuffer.end());

    broadcastSystemMessage(notificationStr);
}

void HotReloadServer::broadcastSystemMessage(const std::string& message) {
    std::lock_guard<std::mutex> lock(connectionsMutex_);

    for (auto& conn : connections_) {
        if (conn->connected()) {
            try {
                conn->send(message);
                logger_->debug("Broadcasted system message to {}", conn->peerAddress().toIpPort());
            } catch (const std::exception& e) {
                logger_->error("Failed to broadcast to {}: {}",
                              conn->peerAddress().toIpPort(), e.what());
            }
        }
    }
}

void HotReloadServer::sendPluginList(const net::TcpConnectionPtr& conn) {
    try {
        auto pluginListMessage = messageProcessor_->createPluginListResponse();

        std::vector<uint8_t> buffer;
        pluginListMessage.serialize(buffer);
        std::string messageStr(buffer.begin(), buffer.end());

        conn->send(messageStr);
        logger_->debug("Sent plugin list to {}", conn->peerAddress().toIpPort());

    } catch (const std::exception& e) {
        logger_->error("Failed to send plugin list to {}: {}",
                      conn->peerAddress().toIpPort(), e.what());
    }
}

void HotReloadServer::sendErrorResponse(const net::TcpConnectionPtr& conn, const std::string& error) {
    try {
        // 创建一个简单的错误消息
        base::Message errorMessage;
        errorMessage.setTyp(base::TypeIdentification::STANDARD);
        errorMessage.setVsq(base::VariableStructureQualifier::SINGLE_INFO);
        errorMessage.setCot(static_cast<uint8_t>(base::CauseOfTransmission::TERMINATION));
        errorMessage.setSource(0);
        errorMessage.setTarget(0);
        errorMessage.setFun(0);
        errorMessage.setInf(0);
        errorMessage.setTextContent("ERROR:" + error);

        std::vector<uint8_t> buffer;
        errorMessage.serialize(buffer);
        std::string messageStr(buffer.begin(), buffer.end());

        conn->send(messageStr);
        logger_->debug("Sent error response to {}: {}", conn->peerAddress().toIpPort(), error);

    } catch (const std::exception& e) {
        logger_->error("Failed to send error response to {}: {}",
                      conn->peerAddress().toIpPort(), e.what());
    }
}
void HotReloadServer::cleanup() {
    // 清理网络组件
    tcpServer_.reset();
    networkLoop_.reset();

    // 清理热加载组件
    fileWatcher_.reset();

    // 清理插件管理器
    pluginManager_.reset();

    // 清理消息处理器
    messageProcessor_.reset();

    // 清理中介器
    mediator_.reset();

    // 清理连接列表
    {
        std::lock_guard<std::mutex> lock(connectionsMutex_);
        connections_.clear();
    }

    initialized_.store(false);
    logger_->info("Server cleanup completed");
}

} // namespace server
} // namespace zexuan
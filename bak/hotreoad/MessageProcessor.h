#ifndef MESSAGE_PROCESSOR_H
#define MESSAGE_PROCESSOR_H

#include <string>
#include <memory>
#include <functional>
#include <vector>
#include "zexuan/net/TcpConnection.h"
#include "zexuan/base/message.hpp"
#include "zexuan/base/message_types.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/logger.hpp"
#include "ThreadSafePluginManager.h"

namespace zexuan {
namespace server {

/**
 * @brief 消息处理器
 *
 * 负责处理客户端发送的 Message 消息，包括：
 * 1. 插件调用请求
 * 2. 系统查询请求
 * 3. 插件信息请求
 */
class MessageProcessor {
public:
    using ResponseCallback = std::function<void(const base::Message& response)>;

    /**
     * @brief 构造函数
     * @param pluginManager 插件管理器
     * @param mediator 消息中介器
     */
    explicit MessageProcessor(std::shared_ptr<ThreadSafePluginManager> pluginManager,
                             std::shared_ptr<base::Mediator> mediator);

    /**
     * @brief 析构函数
     */
    ~MessageProcessor() = default;

    // 禁用拷贝构造和赋值
    MessageProcessor(const MessageProcessor&) = delete;
    MessageProcessor& operator=(const MessageProcessor&) = delete;

    /**
     * @brief 处理客户端消息
     * @param message Message 对象
     * @param callback 响应回调函数
     * @return 成功返回 true，失败返回 false
     */
    bool processMessage(const base::Message& message, ResponseCallback callback);

    /**
     * @brief 处理原始字节消息
     * @param buffer 消息字节缓冲区
     * @param callback 响应回调函数
     * @return 成功返回 true，失败返回 false
     */
    bool processRawMessage(const std::vector<uint8_t>& buffer, ResponseCallback callback);

    /**
     * @brief 创建系统通知消息
     * @param event 事件类型
     * @param pluginName 插件名称
     * @param version 插件版本
     * @return Message 格式的系统通知消息
     */
    base::Message createSystemNotification(const std::string& event,
                                          const std::string& pluginName = "",
                                          const std::string& version = "");

    /**
     * @brief 创建插件列表响应
     * @return Message 格式的插件列表
     */
    base::Message createPluginListResponse();

    /**
     * @brief 创建服务器状态响应
     * @return Message 格式的服务器状态
     */
    base::Message createServerStatusResponse();

private:
    /**
     * @brief 处理插件调用请求
     * @param request 请求 Message 对象
     * @return 响应 Message 对象
     */
    base::Message processPluginCall(const base::Message& request);

    /**
     * @brief 处理系统查询请求
     * @param request 请求 Message 对象
     * @return 响应 Message 对象
     */
    base::Message processSystemQuery(const base::Message& request);

    /**
     * @brief 处理插件信息请求
     * @param request 请求 Message 对象
     * @return 响应 Message 对象
     */
    base::Message processPluginInfoRequest(const base::Message& request);

    /**
     * @brief 创建错误响应
     * @param originalMessage 原始请求消息
     * @param error 错误信息
     * @return 错误响应 Message 对象
     */
    base::Message createErrorResponse(const base::Message& originalMessage, const std::string& error);

    /**
     * @brief 创建成功响应
     * @param originalMessage 原始请求消息
     * @param result 结果数据
     * @return 成功响应 Message 对象
     */
    base::Message createSuccessResponse(const base::Message& originalMessage, const std::string& result);

    /**
     * @brief 验证请求消息格式
     * @param message 请求 Message 对象
     * @return 有效返回 true
     */
    bool validateMessage(const base::Message& message);

    /**
     * @brief 根据消息类型路由到相应的处理函数
     * @param message 请求消息
     * @return 响应消息
     */
    base::Message routeMessage(const base::Message& message);

    /**
     * @brief 将插件列表转换为文本格式
     * @param plugins 插件名称列表
     * @return 格式化的插件列表文本
     */
    std::string formatPluginList(const std::vector<std::string>& plugins);

private:
    std::shared_ptr<ThreadSafePluginManager> pluginManager_; ///< 插件管理器
    std::shared_ptr<base::Mediator> mediator_;               ///< 消息中介器
    std::shared_ptr<spdlog::logger> logger_;                 ///< 日志记录器

    // 统计信息
    std::atomic<uint64_t> processedMessages_;                ///< 已处理消息数
    std::atomic<uint64_t> errorMessages_;                    ///< 错误消息数
};

} // namespace server
} // namespace zexuan

#endif // MESSAGE_PROCESSOR_H

#include "FileSystemWatcher.h"
#include <sys/inotify.h>
#include <unistd.h>
#include <cstring>
#include <chrono>
#include <thread>

namespace zexuan {
namespace server {

FileSystemWatcher::FileSystemWatcher(const std::string& watchPath, int debounceMs)
    : watchPath_(watchPath)
    , debounceMs_(debounceMs)
    , inotifyFd_(-1)
    , watchDescriptor_(-1)
    , running_(false)
    , logger_(Logger::getFileLogger("FileSystemWatcher")) {
    
    // 默认只监控 .so 文件
    setFileFilter([](const std::string& fileName) {
        return fileName.find(".so") != std::string::npos;
    });
}

FileSystemWatcher::~FileSystemWatcher() {
    stopWatching();
}

bool FileSystemWatcher::startWatching(FileChangeCallback callback) {
    if (running_.load()) {
        logger_->warn("FileSystemWatcher is already running");
        return false;
    }

    callback_ = callback;
    
    // 初始化 inotify
    inotifyFd_ = inotify_init1(IN_NONBLOCK | IN_CLOEXEC);
    if (inotifyFd_ == -1) {
        logger_->error("Failed to initialize inotify: {}", strerror(errno));
        return false;
    }

    // 添加监控目录
    uint32_t mask = IN_CLOSE_WRITE | IN_MOVED_TO | IN_DELETE;
    watchDescriptor_ = inotify_add_watch(inotifyFd_, watchPath_.c_str(), mask);
    if (watchDescriptor_ == -1) {
        logger_->error("Failed to add watch for {}: {}", watchPath_, strerror(errno));
        close(inotifyFd_);
        inotifyFd_ = -1;
        return false;
    }

    running_.store(true);
    watcherThread_ = std::thread(&FileSystemWatcher::watcherThreadFunc, this);
    
    logger_->info("Started watching directory: {}", watchPath_);
    return true;
}

void FileSystemWatcher::stopWatching() {
    if (!running_.load()) {
        return;
    }

    running_.store(false);
    
    if (watcherThread_.joinable()) {
        watcherThread_.join();
    }

    if (watchDescriptor_ != -1) {
        inotify_rm_watch(inotifyFd_, watchDescriptor_);
        watchDescriptor_ = -1;
    }

    if (inotifyFd_ != -1) {
        close(inotifyFd_);
        inotifyFd_ = -1;
    }

    logger_->info("Stopped watching directory: {}", watchPath_);
}

void FileSystemWatcher::setFileFilter(std::function<bool(const std::string&)> filter) {
    fileFilter_ = filter;
}

void FileSystemWatcher::watcherThreadFunc() {
    constexpr size_t BUFFER_SIZE = 4096;
    char buffer[BUFFER_SIZE];
    
    logger_->info("FileSystemWatcher thread started");
    
    while (running_.load()) {
        ssize_t length = read(inotifyFd_, buffer, BUFFER_SIZE);
        
        if (length == -1) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                // 没有事件，短暂休眠
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                continue;
            } else {
                logger_->error("Error reading inotify events: {}", strerror(errno));
                break;
            }
        }
        
        if (length > 0) {
            processEvents(buffer, length);
        }
    }
    
    logger_->info("FileSystemWatcher thread stopped");
}

void FileSystemWatcher::processEvents(const char* buffer, ssize_t length) {
    ssize_t offset = 0;
    
    while (offset < length) {
        const struct inotify_event* event = 
            reinterpret_cast<const struct inotify_event*>(buffer + offset);
        
        if (event->len > 0) {
            std::string fileName(event->name);
            std::string filePath = watchPath_ + "/" + fileName;
            
            // 检查文件过滤器
            if (!shouldWatchFile(fileName)) {
                offset += sizeof(struct inotify_event) + event->len;
                continue;
            }
            
            std::string eventType;
            if (event->mask & IN_CLOSE_WRITE) {
                eventType = "modified";
            } else if (event->mask & IN_MOVED_TO) {
                eventType = "created";
            } else if (event->mask & IN_DELETE) {
                eventType = "deleted";
            }
            
            if (!eventType.empty()) {
                logger_->debug("File event: {} - {}", filePath, eventType);
                debounceCallback(filePath, eventType);
            }
        }
        
        offset += sizeof(struct inotify_event) + event->len;
    }
}

bool FileSystemWatcher::shouldWatchFile(const std::string& fileName) const {
    if (!fileFilter_) {
        return true;
    }
    return fileFilter_(fileName);
}

void FileSystemWatcher::debounceCallback(const std::string& filePath, const std::string& eventType) {
    std::lock_guard<std::mutex> lock(debounceMutex_);
    
    auto now = std::chrono::steady_clock::now();
    auto& lastTime = lastEventTime_[filePath];
    
    // 检查是否在防抖时间内
    if (lastTime.time_since_epoch().count() > 0) {
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastTime);
        if (elapsed.count() < debounceMs_) {
            logger_->debug("Debouncing file event: {}", filePath);
            lastTime = now;
            return;
        }
    }
    
    lastTime = now;
    
    // 触发回调
    if (callback_) {
        try {
            callback_(filePath, eventType);
        } catch (const std::exception& e) {
            logger_->error("Exception in file change callback: {}", e.what());
        }
    }
}

} // namespace server
} // namespace zexuan

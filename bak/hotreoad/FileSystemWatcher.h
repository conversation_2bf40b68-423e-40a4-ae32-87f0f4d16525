#ifndef FILESYSTEM_WATCHER_H
#define FILESYSTEM_WATCHER_H

#include <string>
#include <functional>
#include <thread>
#include <atomic>
#include <memory>
#include <map>
#include <chrono>
#include <mutex>
#include "zexuan/logger.hpp"

namespace zexuan {
namespace server {

/**
 * @brief 文件系统监控器，基于 inotify 监控文件变化
 * 
 * 监控指定目录下的文件变化事件，支持过滤特定文件类型
 * 提供防抖机制避免频繁触发回调
 */
class FileSystemWatcher {
public:
    using FileChangeCallback = std::function<void(const std::string& filePath, const std::string& eventType)>;

    /**
     * @brief 构造函数
     * @param watchPath 要监控的目录路径
     * @param debounceMs 防抖延迟时间（毫秒）
     */
    explicit FileSystemWatcher(const std::string& watchPath, int debounceMs = 1000);
    
    /**
     * @brief 析构函数，自动停止监控
     */
    ~FileSystemWatcher();

    // 禁用拷贝构造和赋值
    FileSystemWatcher(const FileSystemWatcher&) = delete;
    FileSystemWatcher& operator=(const FileSystemWatcher&) = delete;

    /**
     * @brief 开始监控文件系统变化
     * @param callback 文件变化时的回调函数
     * @return 成功返回 true，失败返回 false
     */
    bool startWatching(FileChangeCallback callback);

    /**
     * @brief 停止监控
     */
    void stopWatching();

    /**
     * @brief 检查是否正在监控
     * @return 正在监控返回 true
     */
    bool isWatching() const { return running_.load(); }

    /**
     * @brief 设置文件过滤器（只监控匹配的文件）
     * @param filter 文件名过滤函数，返回 true 表示需要监控该文件
     */
    void setFileFilter(std::function<bool(const std::string&)> filter);

private:
    /**
     * @brief 监控线程主函数
     */
    void watcherThreadFunc();

    /**
     * @brief 处理 inotify 事件
     * @param buffer 事件缓冲区
     * @param length 缓冲区长度
     */
    void processEvents(const char* buffer, ssize_t length);

    /**
     * @brief 检查文件是否应该被监控
     * @param fileName 文件名
     * @return 需要监控返回 true
     */
    bool shouldWatchFile(const std::string& fileName) const;

    /**
     * @brief 防抖处理，避免频繁触发回调
     * @param filePath 文件路径
     * @param eventType 事件类型
     */
    void debounceCallback(const std::string& filePath, const std::string& eventType);

private:
    std::string watchPath_;                    ///< 监控路径
    int debounceMs_;                          ///< 防抖延迟时间
    int inotifyFd_;                           ///< inotify 文件描述符
    int watchDescriptor_;                     ///< 监控描述符
    
    std::atomic<bool> running_;               ///< 运行状态
    std::thread watcherThread_;               ///< 监控线程
    
    FileChangeCallback callback_;             ///< 文件变化回调
    std::function<bool(const std::string&)> fileFilter_;  ///< 文件过滤器
    
    std::shared_ptr<spdlog::logger> logger_;  ///< 日志记录器
    
    // 防抖相关
    mutable std::mutex debounceMutex_;
    std::map<std::string, std::chrono::steady_clock::time_point> lastEventTime_;
};

} // namespace server
} // namespace zexuan

#endif // FILESYSTEM_WATCHER_H

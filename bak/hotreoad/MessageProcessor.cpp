#include "MessageProcessor.h"
#include <sstream>
#include <chrono>

namespace zexuan {
namespace server {

MessageProcessor::MessageProcessor(std::shared_ptr<ThreadSafePluginManager> pluginManager,
                                 std::shared_ptr<base::Mediator> mediator)
    : pluginManager_(pluginManager)
    , mediator_(mediator)
    , logger_(Logger::getFileLogger("MessageProcessor"))
    , processedMessages_(0)
    , errorMessages_(0) {
    
    logger_->info("MessageProcessor initialized");
}

bool MessageProcessor::processMessage(const base::Message& message, ResponseCallback callback) {
    processedMessages_.fetch_add(1);
    
    try {
        if (!validateMessage(message)) {
            logger_->warn("Invalid message received");
            auto errorResponse = createErrorResponse(message, "Invalid message format");
            callback(errorResponse);
            errorMessages_.fetch_add(1);
            return false;
        }
        
        auto response = routeMessage(message);
        callback(response);
        
        logger_->debug("Successfully processed message from source {}", message.getSource());
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Exception processing message: {}", e.what());
        auto errorResponse = createErrorResponse(message, "Internal processing error");
        callback(errorResponse);
        errorMessages_.fetch_add(1);
        return false;
    }
}

bool MessageProcessor::processRawMessage(const std::vector<uint8_t>& buffer, ResponseCallback callback) {
    try {
        base::Message message;
        size_t bytesRead = message.deserialize(buffer);
        
        if (bytesRead == 0) {
            logger_->warn("Failed to deserialize message from buffer");
            return false;
        }
        
        return processMessage(message, callback);
        
    } catch (const std::exception& e) {
        logger_->error("Exception processing raw message: {}", e.what());
        return false;
    }
}

base::Message MessageProcessor::createSystemNotification(const std::string& event, 
                                                        const std::string& pluginName,
                                                        const std::string& version) {
    base::Message notification;
    
    notification.setTyp(base::TypeIdentification::STANDARD);
    notification.setVsq(base::VariableStructureQualifier::SINGLE_INFO);
    notification.setCot(static_cast<uint8_t>(base::CauseOfTransmission::SPONTANEOUS));
    notification.setSource(0); // 服务器作为源
    notification.setTarget(0); // 广播
    notification.setFun(0);    // 系统消息
    notification.setInf(0);    // 通知类型
    
    // 构建通知文本
    std::ostringstream oss;
    oss << "SYSTEM_EVENT:" << event;
    if (!pluginName.empty()) {
        oss << "|PLUGIN:" << pluginName;
    }
    if (!version.empty()) {
        oss << "|VERSION:" << version;
    }
    
    notification.setTextContent(oss.str());
    
    return notification;
}

base::Message MessageProcessor::createPluginListResponse() {
    base::Message response;
    
    response.setTyp(base::TypeIdentification::STANDARD);
    response.setVsq(base::VariableStructureQualifier::SINGLE_INFO);
    response.setCot(static_cast<uint8_t>(base::CauseOfTransmission::CONFIRMATION));
    response.setSource(0); // 服务器作为源
    response.setTarget(0); // 响应给请求者
    response.setFun(0);    // 系统响应
    response.setInf(base::InformationNumber::STATUS_REQUEST);
    
    auto plugins = pluginManager_->getPluginList();
    std::string pluginListText = formatPluginList(plugins);
    response.setTextContent(pluginListText);
    
    return response;
}

base::Message MessageProcessor::createServerStatusResponse() {
    base::Message response;
    
    response.setTyp(base::TypeIdentification::STANDARD);
    response.setVsq(base::VariableStructureQualifier::SINGLE_INFO);
    response.setCot(static_cast<uint8_t>(base::CauseOfTransmission::CONFIRMATION));
    response.setSource(0);
    response.setTarget(0);
    response.setFun(0);
    response.setInf(base::InformationNumber::STATUS_REQUEST);
    
    // 获取服务器统计信息
    std::ostringstream oss;
    oss << "SERVER_STATUS:RUNNING";
    oss << "|PROCESSED_MESSAGES:" << processedMessages_.load();
    oss << "|ERROR_MESSAGES:" << errorMessages_.load();
    oss << "|PLUGIN_VERSION:" << pluginManager_->getVersion();
    
    auto plugins = pluginManager_->getPluginList();
    oss << "|ACTIVE_PLUGINS:" << plugins.size();
    
    response.setTextContent(oss.str());
    
    return response;
}

base::Message MessageProcessor::processPluginCall(const base::Message& request) {
    uint8_t targetPlugin = request.getTarget();
    uint8_t functionType = request.getFun();
    uint8_t infoNumber = request.getInf();
    std::string textContent = request.getTextContent();
    
    logger_->debug("Processing plugin call: target={}, fun={}, inf={}, content='{}'", 
                  targetPlugin, functionType, infoNumber, textContent);
    
    // 通过中介器转发消息到目标插件
    if (mediator_) {
        std::string description;
        int result = mediator_->sendMessage(request, description);
        
        if (result == 0) {
            return createSuccessResponse(request, "Plugin call executed successfully");
        } else {
            return createErrorResponse(request, "Plugin call failed: " + description);
        }
    } else {
        return createErrorResponse(request, "No mediator available");
    }
}

base::Message MessageProcessor::processSystemQuery(const base::Message& request) {
    uint8_t infoNumber = request.getInf();
    
    switch (infoNumber) {
        case base::InformationNumber::STATUS_REQUEST:
            return createServerStatusResponse();
        default:
            return createErrorResponse(request, "Unknown system query type");
    }
}

base::Message MessageProcessor::processPluginInfoRequest(const base::Message& request) {
    return createPluginListResponse();
}

base::Message MessageProcessor::createErrorResponse(const base::Message& originalMessage, const std::string& error) {
    base::Message response;
    
    response.setTyp(originalMessage.getTyp());
    response.setVsq(originalMessage.getVsq());
    response.setCot(static_cast<uint8_t>(base::CauseOfTransmission::TERMINATION)); // 表示错误终止
    response.setSource(0); // 服务器作为源
    response.setTarget(originalMessage.getSource()); // 响应给原始发送者
    response.setFun(originalMessage.getFun());
    response.setInf(originalMessage.getInf());
    
    response.setTextContent("ERROR:" + error);
    
    return response;
}

base::Message MessageProcessor::createSuccessResponse(const base::Message& originalMessage, const std::string& result) {
    base::Message response;
    
    response.setTyp(originalMessage.getTyp());
    response.setVsq(originalMessage.getVsq());
    response.setCot(static_cast<uint8_t>(base::CauseOfTransmission::CONFIRMATION));
    response.setSource(0); // 服务器作为源
    response.setTarget(originalMessage.getSource()); // 响应给原始发送者
    response.setFun(originalMessage.getFun());
    response.setInf(originalMessage.getInf());
    
    response.setTextContent("SUCCESS:" + result);
    
    return response;
}

bool MessageProcessor::validateMessage(const base::Message& message) {
    // 基本验证：检查消息的基本字段是否合理
    if (message.getTyp() == 0) {
        return false;
    }
    
    // 可以添加更多验证逻辑
    return true;
}

base::Message MessageProcessor::routeMessage(const base::Message& message) {
    uint8_t cot = message.getCot();
    uint8_t target = message.getTarget();
    
    // 根据传送原因路由消息
    switch (static_cast<base::CauseOfTransmission>(cot)) {
        case base::CauseOfTransmission::INFO_REQUEST:
            return processPluginInfoRequest(message);
            
        case base::CauseOfTransmission::ACTIVATION:
            if (target == 0) {
                // 系统查询
                return processSystemQuery(message);
            } else {
                // 插件调用
                return processPluginCall(message);
            }
            
        default:
            return createErrorResponse(message, "Unsupported message type");
    }
}

std::string MessageProcessor::formatPluginList(const std::vector<std::string>& plugins) {
    if (plugins.empty()) {
        return "PLUGINS:NONE";
    }
    
    std::ostringstream oss;
    oss << "PLUGINS:";
    for (size_t i = 0; i < plugins.size(); ++i) {
        if (i > 0) {
            oss << ",";
        }
        oss << plugins[i];
    }
    
    return oss.str();
}

} // namespace server
} // namespace zexuan

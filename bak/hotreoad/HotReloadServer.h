#ifndef HOT_RELOAD_SERVER_H
#define HOT_RELOAD_SERVER_H

#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <string>
#include <set>
#include <regex>
#include <filesystem>

#include "zexuan/net/EventLoop.h"
#include "zexuan/net/TcpServer.h"
#include "zexuan/net/TcpConnection.h"
#include "zexuan/net/InetAddress.h"
#include "zexuan/net/Buffer.h"
#include "zexuan/net/Timestamp.h"
#include "zexuan/logger.hpp"

#include "ThreadSafePluginManager.h"
#include "FileSystemWatcher.h"
#include "MessageProcessor.h"

namespace zexuan {
namespace server {

/**
 * @brief 热加载服务器配置
 */
struct ServerConfig {
    std::string host = "0.0.0.0";             ///< 监听地址
    uint16_t port = 3414;                     ///< 监听端口
    int maxConnections = 1000;                ///< 最大连接数
    int heartbeatInterval = 30;               ///< 心跳间隔（秒）
    std::string pluginPath = "/root/zexuan/cpp/libs/plugins/"; ///< 插件目录
    int debounceMs = 1000;                    ///< 文件变化防抖时间
    int backupCount = 3;                      ///< 插件备份数量
};

/**
 * @brief 热加载服务器主类
 * 
 * 实现双线程架构：
 * 1. 网络服务线程：处理客户端连接和消息
 * 2. 热加载监控线程：监控插件文件变化并执行热加载
 * 
 * 提供线程安全的插件管理和网络服务
 */
class HotReloadServer {
public:
    /**
     * @brief 构造函数
     * @param config 服务器配置
     */
    explicit HotReloadServer(const ServerConfig& config = ServerConfig{});
    
    /**
     * @brief 析构函数，自动停止服务器
     */
    ~HotReloadServer();

    // 禁用拷贝构造和赋值
    HotReloadServer(const HotReloadServer&) = delete;
    HotReloadServer& operator=(const HotReloadServer&) = delete;

    /**
     * @brief 启动服务器
     * @return 成功返回 true，失败返回 false
     */
    bool start();

    /**
     * @brief 停止服务器
     */
    void stop();

    /**
     * @brief 等待服务器停止
     */
    void waitForStop();

    /**
     * @brief 检查服务器是否正在运行
     * @return 正在运行返回 true
     */
    bool isRunning() const { return running_.load(); }

    /**
     * @brief 初始化插件系统
     * @param configPath 配置文件路径
     * @return 成功返回 true
     */
    bool initializePluginSystem(const std::string& configPath);

    /**
     * @brief 获取服务器统计信息
     * @return JSON 格式的统计信息
     */
    std::string getStatistics() const;

    /**
     * @brief 手动重载指定插件
     * @param pluginName 插件名称
     * @return 成功返回 true
     */
    bool manualReloadPlugin(const std::string& pluginName);

    /**
     * @brief 获取当前连接数
     * @return 连接数
     */
    size_t getConnectionCount() const;

private:
    /**
     * @brief 初始化服务器组件
     * @return 成功返回 true
     */
    bool initialize();

    /**
     * @brief 启动网络服务线程
     * @return 成功返回 true
     */
    bool startNetworkThread();

    /**
     * @brief 启动热加载监控线程
     * @return 成功返回 true
     */
    bool startHotReloadThread();

    /**
     * @brief 网络服务线程主函数
     */
    void networkThreadFunc();

    /**
     * @brief 热加载监控线程主函数
     */
    void hotReloadThreadFunc();

    /**
     * @brief TCP 连接回调
     * @param conn TCP 连接指针
     */
    void onConnection(const net::TcpConnectionPtr& conn);

    /**
     * @brief TCP 消息回调
     * @param conn TCP 连接指针
     * @param buf 消息缓冲区
     * @param receiveTime 接收时间
     */
    void onMessage(const net::TcpConnectionPtr& conn,
                   net::Buffer* buf,
                   Timestamp receiveTime);

    /**
     * @brief 文件系统变化回调
     * @param filePath 变化的文件路径
     * @param eventType 事件类型
     */
    void onFileChange(const std::string& filePath, const std::string& eventType);

    /**
     * @brief 插件状态变化回调
     * @param pluginName 插件名称
     * @param action 操作类型
     */
    void onPluginChange(const std::string& pluginName, const std::string& action);

    /**
     * @brief 向所有客户端广播系统消息
     * @param message 消息内容
     */
    void broadcastSystemMessage(const std::string& message);

    /**
     * @brief 发送插件列表给指定连接
     * @param conn TCP 连接指针
     */
    void sendPluginList(const net::TcpConnectionPtr& conn);

    /**
     * @brief 发送错误响应
     * @param conn TCP 连接指针
     * @param error 错误信息
     */
    void sendErrorResponse(const net::TcpConnectionPtr& conn, const std::string& error);

    /**
     * @brief 清理资源
     */
    void cleanup();

private:
    ServerConfig config_;                      ///< 服务器配置
    std::atomic<bool> running_;                ///< 运行状态
    std::atomic<bool> initialized_;            ///< 初始化状态
    
    // 网络服务组件
    std::unique_ptr<net::EventLoop> networkLoop_;     ///< 网络事件循环
    std::unique_ptr<net::TcpServer> tcpServer_;       ///< TCP 服务器
    std::thread networkThread_;                       ///< 网络服务线程
    
    // 热加载监控组件
    std::unique_ptr<FileSystemWatcher> fileWatcher_;  ///< 文件系统监控器
    std::thread hotReloadThread_;                     ///< 热加载监控线程
    
    // 插件管理
    std::shared_ptr<ThreadSafePluginManager> pluginManager_; ///< 插件管理器
    std::shared_ptr<base::Mediator> mediator_;               ///< 消息中介器
    
    // 消息处理
    std::unique_ptr<MessageProcessor> messageProcessor_;     ///< 消息处理器
    
    // 线程同步
    mutable std::mutex stateMutex_;            ///< 状态互斥锁
    std::condition_variable stateCondition_;   ///< 状态条件变量
    
    // 连接管理
    mutable std::mutex connectionsMutex_;      ///< 连接列表互斥锁
    std::set<net::TcpConnectionPtr> connections_; ///< 活跃连接列表
    
    std::shared_ptr<spdlog::logger> logger_;   ///< 日志记录器
    
    // 统计信息
    std::atomic<uint64_t> totalConnections_;   ///< 总连接数
    std::atomic<uint64_t> totalMessages_;      ///< 总消息数
    std::chrono::system_clock::time_point startTime_; ///< 启动时间
};

} // namespace server
} // namespace zexuan

#endif // HOT_RELOAD_SERVER_H

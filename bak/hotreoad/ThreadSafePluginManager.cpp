#include "ThreadSafePluginManager.h"
#include <dlfcn.h>
#include <filesystem>
#include <regex>
#include <nlohmann/json.hpp>

namespace zexuan {
namespace server {

ThreadSafePluginManager::ThreadSafePluginManager(std::shared_ptr<base::Mediator> mediator)
    : mediator_(mediator)
    , version_(0)
    , logger_(Logger::getFileLogger("ThreadSafePluginManager"))
    , loadSuccessCount_(0)
    , loadFailureCount_(0)
    , unloadCount_(0) {

    logger_->info("ThreadSafePluginManager initialized");
}

ThreadSafePluginManager::~ThreadSafePluginManager() {
    // 卸载所有插件
    std::unique_lock<std::shared_mutex> lock(pluginsMutex_);

    for (auto& [name, info] : plugins_) {
        if (info.instance) {
            try {
                info.instance->shutdown();
            } catch (const std::exception& e) {
                logger_->error("Exception during plugin {} shutdown: {}", name, e.what());
            }
        }

        if (info.libHandle) {
            dlclose(info.libHandle);
        }
    }

    plugins_.clear();
    logger_->info("ThreadSafePluginManager destroyed");
}

bool ThreadSafePluginManager::loadPluginsFromConfig(const std::string& configPath) {
    logger_->info("Loading plugins from configuration: {}", configPath);

    try {
        // 创建 ConfigLoader 实例
        ConfigLoader config;
        config.loadFromFile(configPath);

        // 获取插件配置数组
        auto pluginsJson = config.get<nlohmann::json>("plugins", nlohmann::json::array());

        if (pluginsJson.empty()) {
            logger_->warn("No plugins found in configuration file: {}", configPath);
            return true; // 空配置不算错误
        }

        logger_->info("Found {} plugins to load", pluginsJson.size());

        std::unique_lock<std::shared_mutex> lock(pluginsMutex_);
        bool allSuccess = true;

        // 遍历插件配置
        for (const auto& pluginConfig : pluginsJson) {
            try {
                // 检查插件是否启用
                bool enabled = pluginConfig.value("enabled", true);
                if (!enabled) {
                    logger_->info("Skipping disabled plugin");
                    continue;
                }

                // 获取插件配置
                int pluginId = pluginConfig.value("id", 0);
                std::string name = pluginConfig.value("name", "");
                std::string libraryPath = pluginConfig.value("library_path", "");

                // 验证必需字段
                if (pluginId == 0) {
                    logger_->error("Plugin configuration missing id");
                    allSuccess = false;
                    continue;
                }
                if (name.empty()) {
                    logger_->error("Plugin configuration missing name");
                    allSuccess = false;
                    continue;
                }
                if (libraryPath.empty()) {
                    logger_->error("Plugin configuration missing library_path");
                    allSuccess = false;
                    continue;
                }

                logger_->info("Loading plugin {} ({}) from {}", pluginId, name, libraryPath);

                if (loadPluginFromLibraryUnsafe(libraryPath, pluginId, name)) {
                    logger_->info("Successfully loaded plugin {} ({}) from {}", pluginId, name, libraryPath);
                    loadSuccessCount_.fetch_add(1);
                } else {
                    logger_->error("Failed to load plugin {} ({}) from {}", pluginId, name, libraryPath);
                    loadFailureCount_.fetch_add(1);
                    allSuccess = false;
                }

            } catch (const std::exception& e) {
                logger_->error("Failed to load plugin: {}", e.what());
                loadFailureCount_.fetch_add(1);
                allSuccess = false;
            }
        }

        if (!plugins_.empty()) {
            version_.fetch_add(1);
        }

        return allSuccess;

    } catch (const std::exception& e) {
        logger_->error("Failed to load plugins from config {}: {}", configPath, e.what());
        return false;
    }
}

bool ThreadSafePluginManager::initializeAllPlugins() {
    std::shared_lock<std::shared_mutex> lock(pluginsMutex_);

    logger_->info("Initializing all plugins...");
    bool allSuccess = true;

    for (auto& [name, info] : plugins_) {
        if (info.instance) {
            try {
                if (info.instance->initialize()) {
                    logger_->info("Plugin {} initialized successfully", name);
                    info.isActive = true;
                } else {
                    logger_->error("Plugin {} initialization failed", name);
                    info.isActive = false;
                    allSuccess = false;
                }
            } catch (const std::exception& e) {
                logger_->error("Plugin {} initialization error: {}", name, e.what());
                info.isActive = false;
                allSuccess = false;
            }
        }
    }

    return allSuccess;
}

void ThreadSafePluginManager::shutdownAllPlugins() {
    std::shared_lock<std::shared_mutex> lock(pluginsMutex_);

    logger_->info("Shutting down all plugins...");

    for (auto& [name, info] : plugins_) {
        if (info.instance) {
            try {
                info.instance->shutdown();
                info.isActive = false;
                logger_->info("Plugin {} shutdown successfully", name);
            } catch (const std::exception& e) {
                logger_->error("Plugin {} shutdown error: {}", name, e.what());
            }
        }
    }
}

bool ThreadSafePluginManager::hotReloadPlugin(const std::string& libPath) {
    std::unique_lock<std::shared_mutex> lock(pluginsMutex_);

    std::string pluginName = extractPluginName(libPath);
    logger_->info("Hot reloading plugin: {} from {}", pluginName, libPath);

    // 创建备份
    PluginBackup backup = createPluginBackup(pluginName);

    try {
        // 卸载旧插件（如果存在）
        if (plugins_.find(pluginName) != plugins_.end()) {
            if (!unloadPluginUnsafe(pluginName)) {
                throw std::runtime_error("Failed to unload old plugin");
            }
        }

        // 加载新插件
        if (!loadPluginUnsafe(libPath)) {
            throw std::runtime_error("Failed to load new plugin");
        }

        version_.fetch_add(1);
        loadSuccessCount_.fetch_add(1);

        // 通知插件变化
        lock.unlock();
        notifyPluginChange(pluginName, "reloaded");

        logger_->info("Successfully hot reloaded plugin: {}", pluginName);
        return true;

    } catch (const std::exception& e) {
        logger_->error("Plugin hot reload failed: {}", e.what());

        // 尝试恢复备份
        if (backup.wasLoaded) {
            if (restorePluginBackup(backup)) {
                logger_->info("Successfully restored plugin backup: {}", pluginName);
            } else {
                logger_->error("Failed to restore plugin backup: {}", pluginName);
            }
        }

        loadFailureCount_.fetch_add(1);
        return false;
    }
}

bool ThreadSafePluginManager::unloadPlugin(const std::string& pluginName) {
    std::unique_lock<std::shared_mutex> lock(pluginsMutex_);

    bool result = unloadPluginUnsafe(pluginName);
    if (result) {
        version_.fetch_add(1);
        unloadCount_.fetch_add(1);

        lock.unlock();
        notifyPluginChange(pluginName, "unloaded");
    }

    return result;
}

std::vector<std::string> ThreadSafePluginManager::getPluginList() const {
    std::shared_lock<std::shared_mutex> lock(pluginsMutex_);

    std::vector<std::string> pluginNames;
    pluginNames.reserve(plugins_.size());

    for (const auto& [name, info] : plugins_) {
        if (info.isActive) {
            pluginNames.push_back(name);
        }
    }

    return pluginNames;
}

PluginInfo ThreadSafePluginManager::getPluginInfo(const std::string& pluginName) const {
    std::shared_lock<std::shared_mutex> lock(pluginsMutex_);

    auto it = plugins_.find(pluginName);
    if (it != plugins_.end()) {
        return it->second;
    }

    return PluginInfo{}; // 返回空的插件信息
}

bool ThreadSafePluginManager::hasPlugin(const std::string& pluginName) const {
    std::shared_lock<std::shared_mutex> lock(pluginsMutex_);

    auto it = plugins_.find(pluginName);
    return it != plugins_.end() && it->second.isActive;
}

std::shared_ptr<plugin::PluginBase> ThreadSafePluginManager::getPluginInstance(const std::string& pluginName) const {
    std::shared_lock<std::shared_mutex> lock(pluginsMutex_);

    auto it = plugins_.find(pluginName);
    if (it != plugins_.end() && it->second.isActive) {
        return it->second.instance;
    }

    return nullptr;
}

void ThreadSafePluginManager::setPluginChangeCallback(PluginChangeCallback callback) {
    changeCallback_ = callback;
}

std::string ThreadSafePluginManager::getStatistics() const {
    nlohmann::json stats;

    {
        std::shared_lock<std::shared_mutex> lock(pluginsMutex_);
        stats["total_plugins"] = plugins_.size();
        stats["active_plugins"] = 0;

        for (const auto& [name, info] : plugins_) {
            if (info.isActive) {
                stats["active_plugins"] = stats["active_plugins"].get<int>() + 1;
            }
        }
    }

    stats["version"] = version_.load();
    stats["load_success_count"] = loadSuccessCount_.load();
    stats["load_failure_count"] = loadFailureCount_.load();
    stats["unload_count"] = unloadCount_.load();

    return stats.dump(2);
}

std::shared_ptr<plugin::PluginBase> ThreadSafePluginManager::getPluginById(int pluginId) const {
    std::shared_lock<std::shared_mutex> lock(pluginsMutex_);

    for (const auto& [name, info] : plugins_) {
        if (info.instance && info.instance->getPluginId() == pluginId) {
            return info.instance;
        }
    }

    return nullptr;
}

bool ThreadSafePluginManager::loadPluginFromLibraryUnsafe(const std::string& libPath, int pluginId, const std::string& pluginName) {
    try {
        // 1. 使用 DynamicLibraryLoader 加载库
        libraryLoader_.loadLibraryOrThrow(libPath);

        // 2. 获取必需的函数
        typedef plugin::PluginBase* (*create_plugin_func)(void*, int, const char*);
        auto create_plugin = libraryLoader_.getFunctionOrThrow<create_plugin_func>(libPath, "create_plugin");

        // 3. 获取可选的销毁函数
        typedef void (*destroy_plugin_func)(plugin::PluginBase*);
        auto destroy_plugin = libraryLoader_.getFunctionOptional<destroy_plugin_func>(libPath, "destroy_plugin");

        // 4. 创建插件实例
        // 显式转换为插件期望的类型
        std::shared_ptr<zexuan::base::Mediator> zexuan_mediator = mediator_;
        plugin::PluginBase* plugin_ptr = create_plugin(&zexuan_mediator, pluginId, pluginName.c_str());
        if (!plugin_ptr) {
            throw std::runtime_error("Plugin creation failed for: " + libPath);
        }

        // 5. 智能指针包装
        auto instance = std::shared_ptr<plugin::PluginBase>(plugin_ptr,
            [destroy_plugin, libPath, logger = logger_](plugin::PluginBase* p) {
                if (p) {
                    try {
                        if (destroy_plugin) {
                            destroy_plugin(p);
                            logger->debug("Plugin destroyed using custom destructor: {}", libPath);
                        } else {
                            delete p;
                            logger->debug("Plugin destroyed using default delete: {}", libPath);
                        }
                    } catch (const std::exception& e) {
                        logger->error("Error destroying plugin from {}: {}", libPath, e.what());
                    } catch (...) {
                        logger->error("Unknown error destroying plugin from: {}", libPath);
                    }
                }
            });

        // 6. 保存插件信息
        PluginInfo info;
        info.name = pluginName;
        info.libPath = libPath;
        info.version = "1.0.0"; // 可以从插件获取实际版本
        info.libHandle = nullptr; // DynamicLibraryLoader 管理句柄
        info.instance = instance;
        info.loadTime = std::chrono::system_clock::now();
        info.isActive = false; // 需要显式初始化

        plugins_[pluginName] = info;

        logger_->info("Successfully loaded plugin: {} (ID: {})", pluginName, pluginId);
        return true;

    } catch (const std::exception& e) {
        logger_->error("Failed to load plugin from {}: {}", libPath, e.what());
        return false;
    }
}

bool ThreadSafePluginManager::loadPluginUnsafe(const std::string& libPath) {
    if (!validateLibrary(libPath)) {
        logger_->error("Library validation failed: {}", libPath);
        return false;
    }

    std::string pluginName = extractPluginName(libPath);

    // 加载动态库
    void* handle = dlopen(libPath.c_str(), RTLD_LAZY);
    if (!handle) {
        logger_->error("Failed to load library {}: {}", libPath, dlerror());
        return false;
    }

    // 获取创建函数
    typedef plugin::PluginBase* (*CreatePluginFunc)(std::shared_ptr<base::Mediator>, int, const std::string&);
    CreatePluginFunc createFunc = reinterpret_cast<CreatePluginFunc>(dlsym(handle, "create_plugin"));

    if (!createFunc) {
        logger_->error("Failed to find create_plugin function in {}: {}", libPath, dlerror());
        dlclose(handle);
        return false;
    }

    try {
        // 创建插件实例
        int pluginId = static_cast<int>(plugins_.size() + 1); // 简单的 ID 分配策略
        auto instance = std::shared_ptr<plugin::PluginBase>(
            createFunc(mediator_, pluginId, pluginName)
        );

        if (!instance) {
            logger_->error("Failed to create plugin instance: {}", pluginName);
            dlclose(handle);
            return false;
        }

        // 初始化插件
        if (!instance->initialize()) {
            logger_->error("Failed to initialize plugin: {}", pluginName);
            dlclose(handle);
            return false;
        }

        // 保存插件信息
        PluginInfo info;
        info.name = pluginName;
        info.libPath = libPath;
        info.version = "1.0.0"; // 可以从插件获取实际版本
        info.libHandle = handle;
        info.instance = instance;
        info.loadTime = std::chrono::system_clock::now();
        info.isActive = true;

        plugins_[pluginName] = info;

        logger_->info("Successfully loaded plugin: {}", pluginName);
        return true;

    } catch (const std::exception& e) {
        logger_->error("Exception during plugin creation: {}", e.what());
        dlclose(handle);
        return false;
    }
}

bool ThreadSafePluginManager::unloadPluginUnsafe(const std::string& pluginName) {
    auto it = plugins_.find(pluginName);
    if (it == plugins_.end()) {
        logger_->warn("Plugin not found for unloading: {}", pluginName);
        return false;
    }

    PluginInfo& info = it->second;

    try {
        // 关闭插件
        if (info.instance) {
            info.instance->shutdown();
            info.instance.reset();
        }

        // 卸载动态库
        if (info.libHandle) {
            if (dlclose(info.libHandle) != 0) {
                logger_->warn("Failed to close library for {}: {}", pluginName, dlerror());
            }
        }

        // 从映射中移除
        plugins_.erase(it);

        logger_->info("Successfully unloaded plugin: {}", pluginName);
        return true;

    } catch (const std::exception& e) {
        logger_->error("Exception during plugin unloading: {}", e.what());
        return false;
    }
}

PluginBackup ThreadSafePluginManager::createPluginBackup(const std::string& pluginName) {
    PluginBackup backup;
    backup.pluginName = pluginName;

    auto it = plugins_.find(pluginName);
    if (it != plugins_.end()) {
        backup.originalInfo = it->second;
        backup.wasLoaded = true;
    } else {
        backup.wasLoaded = false;
    }

    return backup;
}

bool ThreadSafePluginManager::restorePluginBackup(const PluginBackup& backup) {
    if (!backup.wasLoaded) {
        // 原来没有加载，确保现在也没有
        auto it = plugins_.find(backup.pluginName);
        if (it != plugins_.end()) {
            unloadPluginUnsafe(backup.pluginName);
        }
        return true;
    }

    // 尝试恢复原来的插件
    try {
        plugins_[backup.pluginName] = backup.originalInfo;
        logger_->info("Restored plugin backup: {}", backup.pluginName);
        return true;
    } catch (const std::exception& e) {
        logger_->error("Failed to restore plugin backup {}: {}", backup.pluginName, e.what());
        return false;
    }
}

std::string ThreadSafePluginManager::extractPluginName(const std::string& libPath) const {
    std::filesystem::path path(libPath);
    std::string filename = path.filename().string();

    // 移除 lib 前缀和 .so 后缀
    std::regex pattern(R"(^lib(.+?)\.so.*$)");
    std::smatch matches;

    if (std::regex_match(filename, matches, pattern)) {
        return matches[1].str();
    }

    // 如果正则匹配失败，返回不带扩展名的文件名
    return path.stem().string();
}

bool ThreadSafePluginManager::validateLibrary(const std::string& libPath) const {
    // 检查文件是否存在
    if (!std::filesystem::exists(libPath)) {
        logger_->error("Library file does not exist: {}", libPath);
        return false;
    }

    // 检查文件是否可读
    if (!std::filesystem::is_regular_file(libPath)) {
        logger_->error("Library path is not a regular file: {}", libPath);
        return false;
    }

    // 尝试打开库文件检查基本有效性
    void* handle = dlopen(libPath.c_str(), RTLD_LAZY | RTLD_NOLOAD);
    if (handle) {
        dlclose(handle);
        return true;
    }

    // 如果库没有加载，尝试加载检查
    handle = dlopen(libPath.c_str(), RTLD_LAZY);
    if (!handle) {
        logger_->error("Failed to validate library {}: {}", libPath, dlerror());
        return false;
    }

    // 检查必需的符号
    bool hasCreateFunc = dlsym(handle, "create_plugin") != nullptr;
    dlclose(handle);

    if (!hasCreateFunc) {
        logger_->error("Library {} missing required create_plugin function", libPath);
        return false;
    }

    return true;
}

void ThreadSafePluginManager::notifyPluginChange(const std::string& pluginName, const std::string& action) {
    if (changeCallback_) {
        try {
            changeCallback_(pluginName, action);
        } catch (const std::exception& e) {
            logger_->error("Exception in plugin change callback: {}", e.what());
        }
    }
}
} // namespace server
} // namespace zexuan
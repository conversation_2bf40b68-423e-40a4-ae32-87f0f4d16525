#ifndef THREAD_SAFE_PLUGIN_MANAGER_H
#define THREAD_SAFE_PLUGIN_MANAGER_H

#include <string>
#include <map>
#include <vector>
#include <memory>
#include <shared_mutex>
#include <atomic>
#include <functional>
#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/dynamic_library_loader.hpp"
#include "zexuan/config_loader.hpp"
#include "zexuan/logger.hpp"

namespace zexuan {
namespace server {

/**
 * @brief 插件信息结构
 */
struct PluginInfo {
    std::string name;                           ///< 插件名称
    std::string libPath;                        ///< 动态库路径
    std::string version;                        ///< 插件版本
    void* libHandle;                           ///< 动态库句柄
    std::shared_ptr<plugin::PluginBase> instance; ///< 插件实例
    std::chrono::system_clock::time_point loadTime; ///< 加载时间
    bool isActive;                             ///< 是否激活状态
    
    PluginInfo() : libHandle(nullptr), isActive(false) {}
};

/**
 * @brief 插件备份信息，用于回滚
 */
struct PluginBackup {
    std::string pluginName;
    PluginInfo originalInfo;
    bool wasLoaded;
    
    PluginBackup() : wasLoaded(false) {}
};

/**
 * @brief 线程安全的插件管理器
 * 
 * 提供插件的热加载、卸载功能，支持多线程并发访问
 * 使用读写锁优化性能，支持插件状态备份和回滚
 */
class ThreadSafePluginManager {
public:
    using PluginChangeCallback = std::function<void(const std::string& pluginName, const std::string& action)>;

    /**
     * @brief 构造函数
     * @param mediator 消息中介器，用于插件间通信
     */
    explicit ThreadSafePluginManager(std::shared_ptr<base::Mediator> mediator);
    
    /**
     * @brief 析构函数，自动卸载所有插件
     */
    ~ThreadSafePluginManager();

    // 禁用拷贝构造和赋值
    ThreadSafePluginManager(const ThreadSafePluginManager&) = delete;
    ThreadSafePluginManager& operator=(const ThreadSafePluginManager&) = delete;

    /**
     * @brief 从配置文件加载所有插件
     * @param configPath 配置文件路径
     * @return 成功返回 true，失败返回 false
     */
    bool loadPluginsFromConfig(const std::string& configPath);

    /**
     * @brief 初始化所有已加载的插件
     * @return 成功返回 true，失败返回 false
     */
    bool initializeAllPlugins();

    /**
     * @brief 关闭所有插件
     */
    void shutdownAllPlugins();

    /**
     * @brief 热加载插件
     * @param libPath 动态库文件路径
     * @return 成功返回 true，失败返回 false
     */
    bool hotReloadPlugin(const std::string& libPath);

    /**
     * @brief 卸载插件
     * @param pluginName 插件名称
     * @return 成功返回 true，失败返回 false
     */
    bool unloadPlugin(const std::string& pluginName);

    /**
     * @brief 获取插件列表（线程安全）
     * @return 插件名称列表
     */
    std::vector<std::string> getPluginList() const;

    /**
     * @brief 获取插件信息（线程安全）
     * @param pluginName 插件名称
     * @return 插件信息，如果插件不存在返回空的 PluginInfo
     */
    PluginInfo getPluginInfo(const std::string& pluginName) const;

    /**
     * @brief 检查插件是否存在
     * @param pluginName 插件名称
     * @return 存在返回 true
     */
    bool hasPlugin(const std::string& pluginName) const;

    /**
     * @brief 获取插件实例（线程安全）
     * @param pluginName 插件名称
     * @return 插件实例，如果不存在返回 nullptr
     */
    std::shared_ptr<plugin::PluginBase> getPluginInstance(const std::string& pluginName) const;

    /**
     * @brief 获取插件实例（按ID）
     * @param pluginId 插件ID
     * @return 插件实例，未找到返回 nullptr
     */
    std::shared_ptr<plugin::PluginBase> getPluginById(int pluginId) const;

    /**
     * @brief 设置插件变化回调
     * @param callback 插件状态变化时的回调函数
     */
    void setPluginChangeCallback(PluginChangeCallback callback);

    /**
     * @brief 获取当前插件版本号
     * @return 版本号（每次插件变化时递增）
     */
    uint64_t getVersion() const { return version_.load(); }

    /**
     * @brief 获取插件统计信息
     * @return 包含插件数量、加载成功/失败次数等统计信息的 JSON 字符串
     */
    std::string getStatistics() const;

private:
    /**
     * @brief 加载单个插件（非线程安全，需要外部加锁）
     * @param libPath 动态库路径
     * @return 成功返回 true
     */
    bool loadPluginUnsafe(const std::string& libPath);

    /**
     * @brief 卸载单个插件（非线程安全，需要外部加锁）
     * @param pluginName 插件名称
     * @return 成功返回 true
     */
    bool unloadPluginUnsafe(const std::string& pluginName);

    /**
     * @brief 创建插件备份
     * @param pluginName 插件名称
     * @return 插件备份信息
     */
    PluginBackup createPluginBackup(const std::string& pluginName);

    /**
     * @brief 恢复插件备份
     * @param backup 备份信息
     * @return 成功返回 true
     */
    bool restorePluginBackup(const PluginBackup& backup);

    /**
     * @brief 从动态库路径提取插件名称
     * @param libPath 动态库路径
     * @return 插件名称
     */
    std::string extractPluginName(const std::string& libPath) const;

    /**
     * @brief 验证动态库文件
     * @param libPath 动态库路径
     * @return 有效返回 true
     */
    bool validateLibrary(const std::string& libPath) const;

    /**
     * @brief 从动态库加载插件（不加锁版本）
     * @param libPath 动态库路径
     * @param pluginId 插件ID
     * @param pluginName 插件名称
     * @return 成功返回 true
     */
    bool loadPluginFromLibraryUnsafe(const std::string& libPath, int pluginId, const std::string& pluginName);

    /**
     * @brief 通知插件状态变化
     * @param pluginName 插件名称
     * @param action 操作类型（loaded, unloaded, reloaded）
     */
    void notifyPluginChange(const std::string& pluginName, const std::string& action);

private:
    mutable std::shared_mutex pluginsMutex_;   ///< 插件映射的读写锁
    std::map<std::string, PluginInfo> plugins_; ///< 插件映射表
    
    std::shared_ptr<base::Mediator> mediator_; ///< 消息中介器
    std::atomic<uint64_t> version_;            ///< 插件版本号

    base::DynamicLibraryLoader libraryLoader_; ///< 动态库加载器
    PluginChangeCallback changeCallback_;      ///< 插件变化回调
    std::shared_ptr<spdlog::logger> logger_;   ///< 日志记录器
    
    // 统计信息
    mutable std::mutex statsMutex_;
    std::atomic<uint64_t> loadSuccessCount_;   ///< 加载成功次数
    std::atomic<uint64_t> loadFailureCount_;   ///< 加载失败次数
    std::atomic<uint64_t> unloadCount_;        ///< 卸载次数
};

} // namespace server
} // namespace zexuan

#endif // THREAD_SAFE_PLUGIN_MANAGER_H

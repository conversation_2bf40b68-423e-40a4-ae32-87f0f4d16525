alias tarz='tar --zstd -cf'              # 打包: tarz archive.tar.zst folder/
alias untarz='tar --zstd -xf'            # 解包: untarz archive.tar.zst
alias listz='tar --zstd -tf'             # 列出: listz archive.tar.zst
alias proxy='export http_proxy=http://192.168.2.10:10809 ;export https_proxy=http://192.168.2.10:10809 ;'
alias unproxy='export http_proxy= ;export https_proxy= ;'
# 裸机

FIQ 是快速中断模式，FIQ 模式下中断处理程序可以使用 R8~R12寄存器，因为 FIQ 模式下的 R8~R12 是独立的，因此中断处理程序可以不用执行保存和恢复中断现场的指令，从而加速中断的执行过程。

每种处理器模式使用 R14(LR)来存放当前子程序的返回地址，如果使用 BL 或者 BLX来调用子函数的话，R14(LR)被设置成该子函数的返回地址

所有的处理器模式都共用一个 CPSR 物理寄存器，因此 CPSR 可以在任何模式下被访问。CPSR 是当前程序状态寄存器，该寄存器包含了条件标志位、中断禁止位、当前处理器模式标志等一些状态位以及一些控制位。spsr是cpsr的备份，user和sys不要访问spsr。

因为 Cortex-A 芯片一上电 SP 指针还没初始化，C 环境还没准备好，所以肯定不能运行 C 代码，必须先用汇编语言设置好 C 环境，比如初始化 DDR、设置 SP指针等等，当汇编把 C 环境设置好了以后才可以运行 C 代码。


## 汇编
定义好的段： 
.text 表示代码段。
.data 初始化的数据段。
.bss 未初始化的数据段。
.rodata 只读数据段。

```
指令 目的 源 描述
MOV R0 R1 将 R1 里面的数据复制到 R0 中。
MRS R0 CPSR 将特殊寄存器 CPSR 里面的数据复制到 R0 中。
MSR CPSR R1 将 R1 里面的数据复制到特殊寄存器 CPSR 里中。

指令 描述
LDR Rd, [Rn , #offset] 从存储器 Rn+offset 的位置读取数据存放到 Rd 中。
STR Rd, [Rn, #offset] 将 Rd 中的数据写入到存储器中的 Rn+offset 位置。


PUSH <reg list> 将寄存器列表存入栈中。
POP <reg list> 从栈中恢复寄存器列表。

B <label>
跳转到 label，如果跳转范围超过了+/-2KB，可以指定 B.W <label>使用 32 位版本的跳转指令， 这样可以得到较大范围的跳转
BX <Rm> 间接跳转，跳转到存放于 Rm 中的地址处，并且切换指令集
BL <label> 跳转到标号地址，并将返回地址保存在 LR 中。
BLX <Rm>结合 BX 和 BL 的特点，跳转到 Rm 指定的地址，并将返回地址保存在 LR 中，切换指令集。
```

## 寄存器
### gpio
当IO配置为GPIO功能后，需要通过GPIO控制器的8个寄存器来控制其逻辑功能。每组GPIO（GPIO1~GPIO5）都有这8个寄存器。
DR (Data Register) - 数据寄存器
GDIR (GPIO Direction Register) - 方向寄存器
PSR (Pad Status Register) - 状态寄存器
ICR1/ICR2 (Interrupt Configuration Register) - 中断配置寄存器
IMR (Interrupt Mask Register) - 中断屏蔽寄存器
ISR (Interrupt Status Register) - 中断状态寄存器
EDGE_SEL (Edge Select Register) - 边沿选择寄存器

## 汇编编译

led.bin:led.s
    arm-linux-gnueabihf-gcc -g -c led.s -o led.o
    arm-linux-gnueabihf-ld -Ttext 0X87800000 led.o -o led.elf
    arm-linux-gnueabihf-objcopy -O binary -S -g led.elf led.bin
    arm-linux-gnueabihf-objdump -D led.elf > led.dis
clean:
    rm -rf *.o led.bin led.elf led.dis

## 启动方式
| BOOT_MODE | BOOT类型 |
|:-:|:-|
| 00 | 从 FUSE 启动 |
| 01 | 串行下载 |
| 10 | 内部 BOOT 模式 |
| 11 | 保留 |

|启动设备|	1	2	3	4	5	6	7	8	|说明|
|:-:|:-|:-|
|串行下载|	0	1	x	x	x	x	x	x	|USB烧写模式|
|SD卡启动|	1	0	0	0	0	0	1	0	|BOOT_CFG1[7:4]=0010|
|EMMC启动|	1	0	1	0	0	1	1	0	|BOOT_CFG1[7:4]=0011|
|NAND启动|	1	0	0	0	1	0	0	1	|BOOT_CFG1[7:4]=1000|

imx头部文件会初始化ddr，所以不用设置ddr初始化

## ld,start
```
SECTIONS{
	. = 0X87800000;
	.text :
	{
		obj/start.o 
		*(.text)
	}
	.rodata ALIGN(4) : {*(.rodata*)}     
	.data ALIGN(4)   : { *(.data) }    
	__bss_start = .;    
	.bss ALIGN(4)  : { *(.bss)  *(COMMON) }    
	__bss_end = .;
}

你需要 __bss_start 和 __bss_end 这两个符号来手动清零 BSS 段。
在裸机程序中（没有操作系统），BSS 段不会自动清零，需要在程序启动时手动清零。如果不清零，这些变量的值是随机的，会导致程序行为不可预测。

.global _start  				/* 全局标号 */

/*
 * 描述：	_start函数，首先是中断向量表的创建
 * 参考文档:ARM Cortex-A(armV7)编程手册V4.0.pdf P42，3 ARM Processor Modes and Registers（ARM处理器模型和寄存器）
 * 		 	ARM Cortex-A(armV7)编程手册V4.0.pdf P165 11.1.1 Exception priorities(异常)
 */
_start:
	ldr pc, =Reset_Handler		/* 复位中断 					*/	
	ldr pc, =Undefined_Handler	/* 未定义中断 					*/
	ldr pc, =SVC_Handler		/* SVC(Supervisor)中断 		*/
	ldr pc, =PrefAbort_Handler	/* 预取终止中断 					*/
	ldr pc, =DataAbort_Handler	/* 数据终止中断 					*/
	ldr	pc, =NotUsed_Handler	/* 未使用中断					*/
	ldr pc, =IRQ_Handler		/* IRQ中断 					*/
	ldr pc, =FIQ_Handler		/* FIQ(快速中断)未定义中断 			*/

/* 复位中断 */	
Reset_Handler:

	cpsid i						/* 关闭全局中断 */

	/* 关闭I,DCache和MMU 
	 * 采取读-改-写的方式。
	 */
	mrc     p15, 0, r0, c1, c0, 0     /* 读取CP15的C1寄存器到R0中       		        	*/
    bic     r0,  r0, #(0x1 << 12)     /* 清除C1寄存器的bit12位(I位)，关闭I Cache            	*/
    bic     r0,  r0, #(0x1 <<  2)     /* 清除C1寄存器的bit2(C位)，关闭D Cache    				*/
    bic     r0,  r0, #0x2             /* 清除C1寄存器的bit1(A位)，关闭对齐						*/
    bic     r0,  r0, #(0x1 << 11)     /* 清除C1寄存器的bit11(Z位)，关闭分支预测					*/
    bic     r0,  r0, #0x1             /* 清除C1寄存器的bit0(M位)，关闭MMU				       	*/
    mcr     p15, 0, r0, c1, c0, 0     /* 将r0寄存器中的值写入到CP15的C1寄存器中	 				*/

	
#if 0
	/* 汇编版本设置中断向量表偏移 */
	ldr r0, =0X87800000

	dsb
	isb
	mcr p15, 0, r0, c12, c0, 0
	dsb
	isb
#endif
    
	/* 设置各个模式下的栈指针，
	 * 注意：IMX6UL的堆栈是向下增长的！
	 * 堆栈指针地址一定要是4字节地址对齐的！！！
	 * DDR范围:0X80000000~0X9FFFFFFF
	 */
	/* 进入IRQ模式 */
	mrs r0, cpsr
	bic r0, r0, #0x1f 	/* 将r0寄存器中的低5位清零，也就是cpsr的M0~M4 	*/
	orr r0, r0, #0x12 	/* r0或上0x13,表示使用IRQ模式					*/
	msr cpsr, r0		/* 将r0 的数据写入到cpsr_c中 					*/
	ldr sp, =0x80600000	/* 设置IRQ模式下的栈首地址为0X80600000,大小为2MB */

	/* 进入SYS模式 */
	mrs r0, cpsr
	bic r0, r0, #0x1f 	/* 将r0寄存器中的低5位清零，也就是cpsr的M0~M4 	*/
	orr r0, r0, #0x1f 	/* r0或上0x13,表示使用SYS模式					*/
	msr cpsr, r0		/* 将r0 的数据写入到cpsr_c中 					*/
	ldr sp, =0x80400000	/* 设置SYS模式下的栈首地址为0X80400000,大小为2MB */

	/* 进入SVC模式 */
	mrs r0, cpsr
	bic r0, r0, #0x1f 	/* 将r0寄存器中的低5位清零，也就是cpsr的M0~M4 	*/
	orr r0, r0, #0x13 	/* r0或上0x13,表示使用SVC模式					*/
	msr cpsr, r0		/* 将r0 的数据写入到cpsr_c中 					*/
	ldr sp, =0X80200000	/* 设置SVC模式下的栈首地址为0X80200000,大小为2MB */

	cpsie i				/* 打开全局中断 */
#if 0
	/* 使能IRQ中断 */
	mrs r0, cpsr		/* 读取cpsr寄存器值到r0中 			*/
	bic r0, r0, #0x80	/* 将r0寄存器中bit7清零，也就是CPSR中的I位清零，表示允许IRQ中断 */
	msr cpsr, r0		/* 将r0重新写入到cpsr中 			*/
#endif

	b main				/* 跳转到main函数 			 	*/

/* 未定义中断 */
Undefined_Handler:
	ldr r0, =Undefined_Handler
	bx r0

/* SVC中断 */
SVC_Handler:
	ldr r0, =SVC_Handler
	bx r0

/* 预取终止中断 */
PrefAbort_Handler:
	ldr r0, =PrefAbort_Handler	
	bx r0

/* 数据终止中断 */
DataAbort_Handler:
	ldr r0, =DataAbort_Handler
	bx r0

/* 未使用的中断 */
NotUsed_Handler:

	ldr r0, =NotUsed_Handler
	bx r0

/* IRQ中断！重点！！！！！ */
IRQ_Handler:
	push {lr}					/* 保存lr地址 */
	push {r0-r3, r12}			/* 保存r0-r3，r12寄存器 */

	mrs r0, spsr				/* 读取spsr寄存器 */
	push {r0}					/* 保存spsr寄存器 */

	mrc p15, 4, r1, c15, c0, 0 /* 从CP15的C0寄存器内的值到R1寄存器中
								* 参考文档ARM Cortex-A(armV7)编程手册V4.0.pdf P49
								* Cortex-A7 Technical ReferenceManua.pdf P68 P138
								*/							
	add r1, r1, #0X2000			/* GIC基地址加0X2000，也就是GIC的CPU接口端基地址 */
	ldr r0, [r1, #0XC]			/* GIC的CPU接口端基地址加0X0C就是GICC_IAR寄存器，
								 * GICC_IAR寄存器保存这当前发生中断的中断号，我们要根据
								 * 这个中断号来绝对调用哪个中断服务函数
								 */
	push {r0, r1}				/* 保存r0,r1 */
	
	cps #0x13					/* 进入SVC模式，允许其他中断再次进去 */
	
<!-- 为什么切换到SVC模式？

IRQ模式默认禁用IRQ中断（防止中断嵌套）
SVC模式允许IRQ中断，实现中断嵌套
允许高优先级中断打断当前中断处理 -->

	push {lr}					/* 保存SVC模式的lr寄存器 */
	ldr r2, =system_irqhandler	/* 加载C语言中断处理函数到r2寄存器中*/
	blx r2						/* 运行C语言中断处理函数，带有一个参数，保存在R0寄存器中 */

	pop {lr}					/* 执行完C语言中断服务函数，lr出栈 */
	cps #0x12					/* 进入IRQ模式 */
	pop {r0, r1}				
	str r0, [r1, #0X10]			/* 中断执行完成，写EOIR */

	pop {r0}						
	msr spsr_cxsf, r0			/* 恢复spsr */

	pop {r0-r3, r12}			/* r0-r3,r12出栈 */
	pop {lr}					/* lr出栈 */
	subs pc, lr, #4				/* 将lr-4赋给pc */
	
	

/* FIQ中断 */
FIQ_Handler:

	ldr r0, =FIQ_Handler	
	bx r0									


用户程序运行 → 硬件中断发生 → 自动跳转到IRQ_Handler
    ↓
保存现场(lr, r0-r3, r12, spsr)
    ↓
读取GIC获取中断号
    ↓
切换到SVC模式(允许中断嵌套)
    ↓
调用C语言中断处理函数
    ↓
通知GIC中断处理完成
    ↓
恢复现场并返回用户程序
```

# uboot

https://github.com/Freescale/u-boot-fslc.git
sudo apt install make git gcc-arm-none-eabi gcc bison flex libssl-dev dpkg-dev lzop libncurses5-dev
sudo make ARCH=arm CROSS_COMPILE=arm-none-eabi- mx6ull_14x14_evk_defconfig
sudo make ARCH=arm CROSS_COMPILE=arm-none-eabi- -j16

dd iflag=dsync oflag=dsync if=u-boot-dtb.imx of=/dev/mmcblk1 seek=2

/root/workspace/u-boot-fslc/board/freescale/mx6ullevk/mx6ullevk.c

## 变量设置
setenv bootcmd 'mmc dev 0;fatload mmc 0:1 80800000 zImage;fatload mmc 0:1 83000000 imx6ull-14x14-zexuan.dtb;bootz 80800000 - 83000000'
setenv bootargs 'console=ttymxc0,115200 root=/dev/mmcblk0p2 rootwait rw'
saveenv
## 修改lcd
/root/workspace/u-boot-fslc/include/configs/mx6ullevk.h
```
"videomode=video=ctfb:x:1024,y:600,depth:24,pclk:19531,le:140,ri:160,up:20,lo:12,hs:20,vs:3,sync:0,vmode:0\0"
```
CONFIG_PHY_MICREL=y
CONFIG_PHY_MICREL_KSZ8XXX=y
改为
CONFIG_PHY_REALTEK=y

这一块待完成，解决不了
## 网络驱动
```

&clks {
	assigned-clocks = <&clks IMX6UL_CLK_PLL3_PFD2>;
	assigned-clock-rates = <320000000>;
};

&iomuxc_snvs {
	pinctrl-names = "default";
	pinctrl_enet1_reset: enet1resetgrp {
		fsl,pins = <
			MX6ULL_PAD_SNVS_TAMPER7__GPIO5_IO07 0x10B0
		>;
	};

	pinctrl_enet2_reset: enet2resetgrp {
		fsl,pins = <
			MX6ULL_PAD_SNVS_TAMPER8__GPIO5_IO08 0x10B0
		>;
	};
};

&fec1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_enet1
		&pinctrl_enet1_reset>;
	phy-reset-gpios = <&gpio5 7 GPIO_ACTIVE_LOW>;
	phy-reset-duration = <200>;
	phy-mode = "rmii";
	phy-handle = <&ethphy0>;
	phy-supply = <&reg_peri_3v3>;
	status = "okay";
};

&fec2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_enet2
		&pinctrl_enet2_reset>;
	phy-reset-gpios = <&gpio5 8 GPIO_ACTIVE_LOW>;
	phy-reset-duration = <200>;
	phy-mode = "rmii";
	phy-handle = <&ethphy1>;
	phy-supply = <&reg_peri_3v3>;
	status = "okay";

	mdio {
		#address-cells = <1>;
		#size-cells = <0>;

		ethphy0: ethernet-phy@2 {
			compatible = "ethernet-phy-id0022.1560";
			reg = <2>;
			micrel,led-mode = <1>;
			clocks = <&clks IMX6UL_CLK_ENET_REF>;
			clock-names = "rmii-ref";
		};

		ethphy1: ethernet-phy@1 {
			compatible = "ethernet-phy-id0022.1560";
			reg = <1>;
			micrel,led-mode = <1>;
			clocks = <&clks IMX6UL_CLK_ENET2_REF>;
			clock-names = "rmii-ref";
		};
	};
};

```
改了会卡死，暂时没有解决。

imx6ull-14x14-evk.dts
├── imx6ull.dtsi
│   ├── imx6ul.dtsi
│   └── imx6ull-pinfunc.h
└── imx6ul-14x14-evk.dtsi
# kernel

https://github.com/Freescale/linux-fslc.git
sudo apt install make gcc-arm-linux-gnueabihf gcc bison flex libssl-dev dpkg-dev lzop vim
ls arch/arm/configs/
make clean
make ARCH=arm imx_zexuan_v6_v7_defconfig
make ARCH=arm CROSS_COMPILE=arm-linux-gnueabihf- -j16
make ARCH=arm CROSS_COMPILE=arm-linux-gnueabihf- dtbs
make ARCH=arm CROSS_COMPILE=arm-linux-gnueabihf- INSTALL_MOD_PATH=./mod_output modules_install
make ARCH=arm CROSS_COMPILE=arm-linux-gnueabihf- modules -j16
## 设备树修改
### 结构
imx6ull-14x14-evk.dts
├── imx6ull.dtsi
│   ├── imx6ul.dtsi
│   └── imx6ull-pinfunc.h
└── imx6ul-14x14-evk.dtsi

#### imx6ull-14x14-evk.dts
定义板子型号和兼容性
配置时钟参数
##### 具体修改
```
#include <dt-bindings/clock/imx6ul-clock.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/media/video-interfaces.h>
#include "imx6ul-pinfunc.h"
#include "imx6ull-pinfunc.h"
#include "imx6ull-pinfunc-snvs.h"

/dts-v1/;

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	/*
	 * The decompressor and also some bootloaders rely on a
	 * pre-existing /chosen node to be available to insert the
	 * command line and merge other ATAGS info.
	 */
    model = "Freescale i.MX6 UltraLiteLite 14x14 EVK Board";
	compatible = "fsl,imx6ull-14x14-evk", "fsl,imx6ull";
	chosen {
        stdout-path = &uart1;
    };

	aliases {
		ethernet0 = &fec1;
		ethernet1 = &fec2;
		gpio0 = &gpio1;
		gpio1 = &gpio2;
		gpio2 = &gpio3;
		gpio3 = &gpio4;
		gpio4 = &gpio5;
		i2c0 = &i2c1;
		i2c1 = &i2c2;
		i2c2 = &i2c3;
		i2c3 = &i2c4;
		mmc0 = &usdhc1;
		mmc1 = &usdhc2;
		serial0 = &uart1;
		serial2 = &uart3;
		serial3 = &uart4;
		serial4 = &uart5;
		serial5 = &uart6;
		serial6 = &uart7;
		serial7 = &uart8;
		sai1 = &sai1;
		sai2 = &sai2;
		sai3 = &sai3;
		spi0 = &ecspi1;
		spi1 = &ecspi2;
		spi2 = &ecspi3;
		spi3 = &ecspi4;
		usb0 = &usbotg1;
		usb1 = &usbotg2;
		usbphy0 = &usbphy1;
		usbphy1 = &usbphy2;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0>;
			clock-frequency = <900000000>;
			clock-latency = <61036>; /* two CLK32 periods */
			#cooling-cells = <2>;
			operating-points = <
                /* kHz	uV */
                900000	1275000
                792000	1225000
                528000	1175000
                396000	1025000
                198000	950000
            >;
			fsl,soc-operating-points = <
                /* KHz	uV */
                900000	1250000
                792000	1175000
                528000	1175000
                396000	1175000
                198000	1175000
            >;
			clocks = <&clks IMX6UL_CLK_ARM>,
				 <&clks IMX6UL_CLK_PLL2_BUS>,
				 <&clks IMX6UL_CLK_PLL2_PFD2>,
				 <&clks IMX6UL_CA7_SECONDARY_SEL>,
				 <&clks IMX6UL_CLK_STEP>,
				 <&clks IMX6UL_CLK_PLL1_SW>,
				 <&clks IMX6UL_CLK_PLL1_SYS>;
			clock-names = "arm", "pll2_bus",  "pll2_pfd2_396m",
				      "secondary_sel", "step", "pll1_sw",
				      "pll1_sys";
			arm-supply = <&reg_arm>;
			soc-supply = <&reg_soc>;
			nvmem-cells = <&cpu_speed_grade>;
			nvmem-cell-names = "speed_grade";
		};
	};

    memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x20000000>;
	};

    beep {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "atkalpha-beep";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_beep>;
		beep-gpio = <&gpio5 1 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

    leds {
        compatible = "gpio-leds";
        led0 {
            label = "red";
            gpios = <&gpio1 3 GPIO_ACTIVE_LOW>;
            linux,default-trigger = "heartbeat";
            default-state = "on";
            };
	};
	
	keys {
		compatible = "gpio-keys";
		#address-cells = <1>;
		#size-cells = <0>;
		autorepeat;
		key0 {
			label = "GPIO Key Enter";
			linux,code = <KEY_ENTER>;
			gpios = <&gpio1 18 GPIO_ACTIVE_LOW>;
		};
	};


    backlight_display: backlight-display {
		compatible = "pwm-backlight";
		pwms = <&pwm1 0 5000000 0>;
		brightness-levels = <0 4 8 16 32 64 128 255>;
		default-brightness-level = <6>;
		status = "okay";
	};

    reg_sd1_vmmc: regulator-sd1-vmmc {
		compatible = "regulator-fixed";
		regulator-name = "VSD_3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		/*gpio = <&gpio1 9 GPIO_ACTIVE_HIGH>;*/
		enable-active-high;
	};

    reg_peri_3v3: regulator-peri-3v3 {
		compatible = "regulator-fixed";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_peri_3v3>;
		regulator-name = "VPERI_3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio5 2 GPIO_ACTIVE_LOW>;
		/*
		 * If you want to want to make this dynamic please
		 * check schematics and test all affected peripherals:
		 *
		 * - sensors
		 * - ethernet phy
		 * - can
		 * - bluetooth
		 * - wm8960 audio codec
		 * - ov5640 camera
		 */
		regulator-always-on;
	};

    panel {
		compatible = "panel-dpi";
		backlight = <&backlight_display>;
		
		width-mm = <154>;    /* 屏幕物理宽度 */
		height-mm = <86>;    /* 屏幕物理高度 */
		
		panel-timing {
			clock-frequency = <51200000>;
			hactive = <1024>;
			vactive = <600>;
			hfront-porch = <160>;
			hback-porch = <140>;
			hsync-len = <20>;
			vback-porch = <20>;
			vfront-porch = <12>;
			vsync-len = <3>;
			hsync-active = <0>;
			vsync-active = <0>;
			de-active = <1>;
			pixelclk-active = <0>;
		};

		port {
			panel_in: endpoint {
				remote-endpoint = <&display_out>;
			};
		};
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>;
		interrupt-parent = <&intc>;
		status = "disabled";
	};

	sound-wm8960 {
		compatible = "fsl,imx-audio-wm8960";
		model = "wm8960-audio";
		audio-cpu = <&sai2>;
		audio-codec = <&codec>;
		audio-asrc = <&asrc>;
		hp-det-gpio = <&gpio5 4 0>;
		audio-routing =
			"Headphone Jack", "HP_L",
			"Headphone Jack", "HP_R",
			"Ext Spk", "SPK_LP",
			"Ext Spk", "SPK_LN",
			"Ext Spk", "SPK_RP",
			"Ext Spk", "SPK_RN",
			"LINPUT2", "Mic Jack",
			"LINPUT3", "Mic Jack",
			"RINPUT1", "AMIC",
			"RINPUT2", "AMIC",
			"Mic Jack", "MICB",
			"AMIC", "MICB";
	};

	ckil: clock-cli {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <32768>;
		clock-output-names = "ckil";
	};

	osc: clock-osc {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <********>;
		clock-output-names = "osc";
	};

	ipp_di0: clock-di0 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <0>;
		clock-output-names = "ipp_di0";
	};

	ipp_di1: clock-di1 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <0>;
		clock-output-names = "ipp_di1";
	};

	pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupt-parent = <&gpc>;
		interrupts = <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>;
	};

	soc: soc {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "simple-bus";
		interrupt-parent = <&gpc>;
		ranges;

		ocram: sram@900000 {
			compatible = "mmio-sram";
			reg = <0x00900000 0x20000>;
			ranges = <0 0x00900000 0x20000>;
			#address-cells = <1>;
			#size-cells = <1>;
		};

		intc: interrupt-controller@a01000 {
			compatible = "arm,gic-400", "arm,cortex-a7-gic";
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_HIGH)>;
			#interrupt-cells = <3>;
			interrupt-controller;
			interrupt-parent = <&intc>;
			reg = <0x00a01000 0x1000>,
			      <0x00a02000 0x2000>,
			      <0x00a04000 0x2000>,
			      <0x00a06000 0x2000>;
		};

		dma_apbh: dma-controller@1804000 {
			compatible = "fsl,imx6q-dma-apbh", "fsl,imx28-dma-apbh";
			reg = <0x01804000 0x2000>;
			interrupts = <0 13 IRQ_TYPE_LEVEL_HIGH>,
				     <0 13 IRQ_TYPE_LEVEL_HIGH>,
				     <0 13 IRQ_TYPE_LEVEL_HIGH>,
				     <0 13 IRQ_TYPE_LEVEL_HIGH>;
			#dma-cells = <1>;
			dma-channels = <4>;
			clocks = <&clks IMX6UL_CLK_APBHDMA>;
		};

		gpmi: nand-controller@1806000 {
			compatible = "fsl,imx6q-gpmi-nand";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x01806000 0x2000>, <0x01808000 0x2000>;
			reg-names = "gpmi-nand", "bch";
			interrupts = <0 15 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "bch";
			clocks = <&clks IMX6UL_CLK_GPMI_IO>,
				 <&clks IMX6UL_CLK_GPMI_APB>,
				 <&clks IMX6UL_CLK_GPMI_BCH>,
				 <&clks IMX6UL_CLK_GPMI_BCH_APB>,
				 <&clks IMX6UL_CLK_PER_BCH>;
			clock-names = "gpmi_io", "gpmi_apb", "gpmi_bch",
				      "gpmi_bch_apb", "per1_bch";
			dmas = <&dma_apbh 0>;
			dma-names = "rx-tx";
			status = "disabled";
		};

		aips1: bus@2000000 {
			compatible = "fsl,aips-bus", "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x02000000 0x100000>;
			ranges;

			spba-bus@2000000 {
				compatible = "fsl,spba-bus", "simple-bus";
				#address-cells = <1>;
				#size-cells = <1>;
				reg = <0x02000000 0x40000>;
				ranges;

				ecspi1: spi@2008000 {
					#address-cells = <1>;
					#size-cells = <0>;
					compatible = "fsl,imx6ul-ecspi", "fsl,imx51-ecspi";
					reg = <0x02008000 0x4000>;
					interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_ECSPI1>,
						 <&clks IMX6UL_CLK_ECSPI1>;
					clock-names = "ipg", "per";
					dmas = <&sdma 3 7 1>, <&sdma 4 7 2>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				ecspi2: spi@200c000 {
					#address-cells = <1>;
					#size-cells = <0>;
					compatible = "fsl,imx6ul-ecspi", "fsl,imx51-ecspi";
					reg = <0x0200c000 0x4000>;
					interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_ECSPI2>,
						 <&clks IMX6UL_CLK_ECSPI2>;
					clock-names = "ipg", "per";
					dmas = <&sdma 5 7 1>, <&sdma 6 7 2>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				ecspi3: spi@2010000 {
					#address-cells = <1>;
					#size-cells = <0>;
					compatible = "fsl,imx6ul-ecspi", "fsl,imx51-ecspi";
					reg = <0x02010000 0x4000>;
					interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_ECSPI3>,
						 <&clks IMX6UL_CLK_ECSPI3>;
					clock-names = "ipg", "per";
					dmas = <&sdma 7 7 1>, <&sdma 8 7 2>;
					dma-names = "rx", "tx";
					fsl,spi-num-chipselects = <1>;
                    cs-gpios = <&gpio1 20 GPIO_ACTIVE_LOW>;
                    pinctrl-names = "default";
                    pinctrl-0 = <&pinctrl_ecspi3>;
                    status = "okay";

                    spidev: icm20608@0 {
                        compatible = "invensense,icm20608";
                        spi-max-frequency = <8000000>;
                        reg = <0>;
                    };
				};

				ecspi4: spi@2014000 {
					#address-cells = <1>;
					#size-cells = <0>;
					compatible = "fsl,imx6ul-ecspi", "fsl,imx51-ecspi";
					reg = <0x02014000 0x4000>;
					interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_ECSPI4>,
						 <&clks IMX6UL_CLK_ECSPI4>;
					clock-names = "ipg", "per";
					dmas = <&sdma 9 7 1>, <&sdma 10 7 2>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				uart7: serial@2018000 {
					compatible = "fsl,imx6ul-uart",
						     "fsl,imx6q-uart";
					reg = <0x02018000 0x4000>;
					interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_UART7_IPG>,
						 <&clks IMX6UL_CLK_UART7_SERIAL>;
					clock-names = "ipg", "per";
					status = "disabled";
				};

				uart1: serial@2020000 {
					compatible = "fsl,imx6ul-uart",
						     "fsl,imx6q-uart";
					reg = <0x02020000 0x4000>;
					interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_UART1_IPG>,
						 <&clks IMX6UL_CLK_UART1_SERIAL>;
					clock-names = "ipg", "per";
					pinctrl-names = "default";
                    pinctrl-0 = <&pinctrl_uart1>;
                    status = "okay";
				};

				sai1: sai@2028000 {
					#sound-dai-cells = <0>;
					compatible = "fsl,imx6ul-sai", "fsl,imx6sx-sai";
					reg = <0x02028000 0x4000>;
					interrupts = <GIC_SPI 97 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_SAI1_IPG>,
						 <&clks IMX6UL_CLK_SAI1>,
						 <&clks IMX6UL_CLK_DUMMY>, <&clks IMX6UL_CLK_DUMMY>;
					clock-names = "bus", "mclk1", "mclk2", "mclk3";
					dmas = <&sdma 35 24 0>,
					       <&sdma 36 24 0>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				sai2: sai@202c000 {
					#sound-dai-cells = <0>;
					compatible = "fsl,imx6ul-sai", "fsl,imx6sx-sai";
					reg = <0x0202c000 0x4000>;
					interrupts = <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_SAI2_IPG>,
						 <&clks IMX6UL_CLK_SAI2>,
						 <&clks IMX6UL_CLK_DUMMY>, <&clks IMX6UL_CLK_DUMMY>;
					clock-names = "bus", "mclk1", "mclk2", "mclk3";
					dmas = <&sdma 37 24 0>,
					       <&sdma 38 24 0>;
					dma-names = "rx", "tx";
					pinctrl-names = "default";
                    pinctrl-0 = <&pinctrl_sai2
								&pinctrl_sai2_hp_det_b>;
                    assigned-clocks = <&clks IMX6UL_CLK_SAI2_SEL>,
                            <&clks IMX6UL_CLK_SAI2>;
                    assigned-clock-parents = <&clks IMX6UL_CLK_PLL4_AUDIO_DIV>;
                    assigned-clock-rates = <0>, <12288000>;
                    status = "okay";
				};

				sai3: sai@2030000 {
					#sound-dai-cells = <0>;
					compatible = "fsl,imx6ul-sai", "fsl,imx6sx-sai";
					reg = <0x02030000 0x4000>;
					interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_SAI3_IPG>,
						 <&clks IMX6UL_CLK_SAI3>,
						 <&clks IMX6UL_CLK_DUMMY>, <&clks IMX6UL_CLK_DUMMY>;
					clock-names = "bus", "mclk1", "mclk2", "mclk3";
					dmas = <&sdma 39 24 0>,
					       <&sdma 40 24 0>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				asrc: asrc@2034000 {
					compatible = "fsl,imx6ul-asrc", "fsl,imx53-asrc";
					reg = <0x2034000 0x4000>;
					interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_ASRC_IPG>,
						<&clks IMX6UL_CLK_ASRC_MEM>, <&clks 0>,
						<&clks 0>, <&clks 0>, <&clks 0>, <&clks 0>,
						<&clks 0>, <&clks 0>, <&clks 0>, <&clks 0>,
						<&clks 0>, <&clks 0>, <&clks 0>, <&clks 0>,
						<&clks IMX6UL_CLK_SPDIF>, <&clks 0>, <&clks 0>,
						<&clks IMX6UL_CLK_SPBA>;
					clock-names = "mem", "ipg", "asrck_0",
						"asrck_1", "asrck_2", "asrck_3", "asrck_4",
						"asrck_5", "asrck_6", "asrck_7", "asrck_8",
						"asrck_9", "asrck_a", "asrck_b", "asrck_c",
						"asrck_d", "asrck_e", "asrck_f", "spba";
					dmas = <&sdma 17 23 1>, <&sdma 18 23 1>, <&sdma 19 23 1>,
						<&sdma 20 23 1>, <&sdma 21 23 1>, <&sdma 22 23 1>;
					dma-names = "rxa", "rxb", "rxc",
						    "txa", "txb", "txc";
					fsl,asrc-rate = <48000>;
					fsl,asrc-width = <16>;
					status = "okay";
				};
			};

			pwm1: pwm@2080000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x02080000 0x4000>;
				interrupts = <GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM1>,
					 <&clks IMX6UL_CLK_PWM1>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_pwm1>;
                status = "okay";
			};

			pwm2: pwm@2084000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x02084000 0x4000>;
				interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM2>,
					 <&clks IMX6UL_CLK_PWM2>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm3: pwm@2088000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x02088000 0x4000>;
				interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM3>,
					 <&clks IMX6UL_CLK_PWM3>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm4: pwm@208c000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x0208c000 0x4000>;
				interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM4>,
					 <&clks IMX6UL_CLK_PWM4>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			gpt1: timer@2098000 {
				compatible = "fsl,imx6ul-gpt", "fsl,imx6sx-gpt";
				reg = <0x02098000 0x4000>;
				interrupts = <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPT1_BUS>,
					 <&clks IMX6UL_CLK_GPT1_SERIAL>;
				clock-names = "ipg", "per";
			};

			gpio1: gpio@209c000 {
				compatible = "fsl,imx6ul-gpio", "fsl,imx35-gpio";
				reg = <0x0209c000 0x4000>;
				interrupts = <GIC_SPI 66 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 67 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPIO1>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc  0 23 10>, <&iomuxc 10 17 6>,
					      <&iomuxc 16 33 16>;
			};

			gpio2: gpio@20a0000 {
				compatible = "fsl,imx6ul-gpio", "fsl,imx35-gpio";
				reg = <0x020a0000 0x4000>;
				interrupts = <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPIO2>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 49 16>, <&iomuxc 16 111 6>;
			};

			gpio3: gpio@20a4000 {
				compatible = "fsl,imx6ul-gpio", "fsl,imx35-gpio";
				reg = <0x020a4000 0x4000>;
				interrupts = <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPIO3>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 65 29>;
			};

			gpio4: gpio@20a8000 {
				compatible = "fsl,imx6ul-gpio", "fsl,imx35-gpio";
				reg = <0x020a8000 0x4000>;
				interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPIO4>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 94 17>, <&iomuxc 17 117 12>;
			};

			gpio5: gpio@20ac000 {
				compatible = "fsl,imx6ul-gpio", "fsl,imx35-gpio";
				reg = <0x020ac000 0x4000>;
				interrupts = <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPIO5>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 7 10>, <&iomuxc 10 5 2>;
			};

			fec2: ethernet@20b4000 {
				compatible = "fsl,imx6ul-fec", "fsl,imx6q-fec";
				reg = <0x020b4000 0x4000>;
				interrupt-names = "int0", "pps";
				interrupts = <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_ENET>,
					 <&clks IMX6UL_CLK_ENET_AHB>,
					 <&clks IMX6UL_CLK_ENET_PTP>,
					 <&clks IMX6UL_CLK_ENET2_REF_SEL>;
				clock-names = "ipg", "ahb", "ptp",
					      "enet_clk_ref";
				fsl,num-tx-queues = <1>;
				fsl,num-rx-queues = <1>;
				fsl,stop-mode = <&gpr 0x10 4>;
				fsl,magic-packet;
				nvmem-cells = <&fec2_mac_addr>;
				nvmem-cell-names = "mac-address";
				pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_enet2
                                &pinctrl_enet2_reset>;
                phy-reset-gpios = <&gpio5 8 GPIO_ACTIVE_LOW>;
                phy-reset-duration = <200>;
                phy-mode = "rmii";
                phy-handle = <&ethphy1>;
                phy-supply = <&reg_peri_3v3>;
                status = "okay";

                mdio {
                    #address-cells = <1>;
                    #size-cells = <0>;

                    ethphy0: ethernet-phy@2 {
                        compatible = "ethernet-phy-id0022.1560";
                        reg = <2>;
                        micrel,led-mode = <1>;
                        clocks = <&clks IMX6UL_CLK_ENET_REF>;
                        clock-names = "rmii-ref";

                    };

                    ethphy1: ethernet-phy@1 {
                        compatible = "ethernet-phy-id0022.1560";
                        reg = <1>;
                        micrel,led-mode = <1>;
                        clocks = <&clks IMX6UL_CLK_ENET2_REF>;
                        clock-names = "rmii-ref";
                    };
                };
			};

			kpp: keypad@20b8000 {
				compatible = "fsl,imx6ul-kpp", "fsl,imx21-kpp";
				reg = <0x020b8000 0x4000>;
				interrupts = <GIC_SPI 82 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_KPP>;
				status = "disabled";
			};

			wdog1: watchdog@20bc000 {
				compatible = "fsl,imx6ul-wdt", "fsl,imx21-wdt";
				reg = <0x020bc000 0x4000>;
				interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_WDOG1>;
                pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_wdog>;
                fsl,ext-reset-output;
			};

			wdog2: watchdog@20c0000 {
				compatible = "fsl,imx6ul-wdt", "fsl,imx21-wdt";
				reg = <0x020c0000 0x4000>;
				interrupts = <GIC_SPI 81 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_WDOG2>;
				status = "disabled";
			};

			clks: clock-controller@20c4000 {
				compatible = "fsl,imx6ul-ccm";
				reg = <0x020c4000 0x4000>;
				interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>;
				#clock-cells = <1>;
				clocks = <&ckil>, <&osc>, <&ipp_di0>, <&ipp_di1>;
				clock-names = "ckil", "osc", "ipp_di0", "ipp_di1";
                assigned-clocks = <&clks IMX6UL_CLK_PLL3_PFD2>;
	            assigned-clock-rates = <320000000>;
			};

			anatop: anatop@20c8000 {
				compatible = "fsl,imx6ul-anatop", "fsl,imx6q-anatop",
					     "syscon", "simple-mfd";
				reg = <0x020c8000 0x1000>;
				interrupts = <GIC_SPI 49 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 127 IRQ_TYPE_LEVEL_HIGH>;

				reg_3p0: regulator-3p0 {
					compatible = "fsl,anatop-regulator";
					regulator-name = "vdd3p0";
					regulator-min-microvolt = <2625000>;
					regulator-max-microvolt = <3400000>;
					anatop-reg-offset = <0x120>;
					anatop-vol-bit-shift = <8>;
					anatop-vol-bit-width = <5>;
					anatop-min-bit-val = <0>;
					anatop-min-voltage = <2625000>;
					anatop-max-voltage = <3400000>;
					anatop-enable-bit = <0>;
				};

				reg_arm: regulator-vddcore {
					compatible = "fsl,anatop-regulator";
					regulator-name = "cpu";
					regulator-min-microvolt = <725000>;
					regulator-max-microvolt = <1450000>;
					regulator-always-on;
					anatop-reg-offset = <0x140>;
					anatop-vol-bit-shift = <0>;
					anatop-vol-bit-width = <5>;
					anatop-delay-reg-offset = <0x170>;
					anatop-delay-bit-shift = <24>;
					anatop-delay-bit-width = <2>;
					anatop-min-bit-val = <1>;
					anatop-min-voltage = <725000>;
					anatop-max-voltage = <1450000>;
				};

				reg_soc: regulator-vddsoc {
					compatible = "fsl,anatop-regulator";
					regulator-name = "vddsoc";
					regulator-min-microvolt = <725000>;
					regulator-max-microvolt = <1450000>;
					regulator-always-on;
					anatop-reg-offset = <0x140>;
					anatop-vol-bit-shift = <18>;
					anatop-vol-bit-width = <5>;
					anatop-delay-reg-offset = <0x170>;
					anatop-delay-bit-shift = <28>;
					anatop-delay-bit-width = <2>;
					anatop-min-bit-val = <1>;
					anatop-min-voltage = <725000>;
					anatop-max-voltage = <1450000>;
				};

				tempmon: tempmon {
					compatible = "fsl,imx6ul-tempmon", "fsl,imx6sx-tempmon";
					interrupt-parent = <&gpc>;
					interrupts = <GIC_SPI 49 IRQ_TYPE_LEVEL_HIGH>;
					fsl,tempmon = <&anatop>;
					nvmem-cells = <&tempmon_calib>, <&tempmon_temp_grade>;
					nvmem-cell-names = "calib", "temp_grade";
					clocks = <&clks IMX6UL_CLK_PLL3_USB_OTG>;
					#thermal-sensor-cells = <0>;
				};
			};

			usbphy1: usbphy@20c9000 {
				compatible = "fsl,imx6ul-usbphy", "fsl,imx23-usbphy";
				reg = <0x020c9000 0x1000>;
				interrupts = <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_USBPHY1>;
				phy-3p0-supply = <&reg_3p0>;
				fsl,anatop = <&anatop>;
                fsl,tx-d-cal = <106>;
			};

			usbphy2: usbphy@20ca000 {
				compatible = "fsl,imx6ul-usbphy", "fsl,imx23-usbphy";
				reg = <0x020ca000 0x1000>;
				interrupts = <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_USBPHY2>;
				phy-3p0-supply = <&reg_3p0>;
				fsl,anatop = <&anatop>;
                fsl,tx-d-cal = <106>;
			};

			snvs: snvs@20cc000 {
				compatible = "fsl,sec-v4.0-mon", "syscon", "simple-mfd";
				reg = <0x020cc000 0x4000>;

				snvs_rtc: snvs-rtc-lp {
					compatible = "fsl,sec-v4.0-mon-rtc-lp";
					regmap = <&snvs>;
					offset = <0x34>;
					interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>,
						     <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
				};

				snvs_poweroff: snvs-poweroff {
					compatible = "syscon-poweroff";
					regmap = <&snvs>;
					offset = <0x38>;
					value = <0x60>;
					mask = <0x60>;
					status = "okay";
				};

				snvs_pwrkey: snvs-powerkey {
					compatible = "fsl,sec-v4.0-pwrkey";
					regmap = <&snvs>;
					interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
					linux,keycode = <KEY_POWER>;
					wakeup-source;
					status = "okay";
				};

				snvs_lpgpr: snvs-lpgpr {
					compatible = "fsl,imx6ul-snvs-lpgpr";
				};
			};

			epit1: epit@20d0000 {
				reg = <0x020d0000 0x4000>;
				interrupts = <GIC_SPI 56 IRQ_TYPE_LEVEL_HIGH>;
			};

			epit2: epit@20d4000 {
				reg = <0x020d4000 0x4000>;
				interrupts = <GIC_SPI 57 IRQ_TYPE_LEVEL_HIGH>;
			};

			src: reset-controller@20d8000 {
				compatible = "fsl,imx6ul-src", "fsl,imx51-src";
				reg = <0x020d8000 0x4000>;
				interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
				#reset-cells = <1>;
			};

			gpc: gpc@20dc000 {
				compatible = "fsl,imx6ul-gpc", "fsl,imx6q-gpc";
				reg = <0x020dc000 0x4000>;
				interrupt-controller;
				#interrupt-cells = <3>;
				interrupts = <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-parent = <&intc>;
				clocks = <&clks IMX6UL_CLK_IPG>;
				clock-names = "ipg";

				pgc {
					#address-cells = <1>;
					#size-cells = <0>;

					power-domain@0 {
						reg = <0>;
						#power-domain-cells = <0>;
					};
				};
			};

			iomuxc: pinctrl@20e0000 {
				compatible = "fsl,imx6ul-iomuxc";
				reg = <0x020e0000 0x4000>;

                pinctrl-names = "default";

                pinctrl_camera_clock: cameraclockgrp {
                    fsl,pins = <
                        MX6UL_PAD_CSI_MCLK__CSI_MCLK		0x1b088
                    >;
                };

                pinctrl_led: ledgrp {
                    fsl,pins = <
                        MX6UL_PAD_GPIO1_IO03__GPIO1_IO03 0x10B0 /* LED0 */
                    >;
                };
                pinctrl_beep: beepgrp {
                    fsl,pins = <
                        MX6ULL_PAD_SNVS_TAMPER1__GPIO5_IO01 0x10B0 /* beep */ 
                    >;
                };
                pinctrl_key: keygrp {
                    fsl,pins = <
                        MX6UL_PAD_UART1_CTS_B__GPIO1_IO18 0xF080 /* KEY0 */
                    >;
                };
				pinctrl_uart3: uart3grp {
					fsl,pins = <
						MX6UL_PAD_UART3_TX_DATA__UART3_DCE_TX 0X1b0b1
						MX6UL_PAD_UART3_RX_DATA__UART3_DCE_RX 0X1b0b1
					>;
				};
				pinctrl_tsc: tscgrp {
					fsl,pins = <
						MX6UL_PAD_GPIO1_IO09__GPIO1_IO09 0x79 /* TSC_INT */
					>;
				};
                pinctrl_csi1: csi1grp {
                    fsl,pins = <
                        MX6UL_PAD_CSI_PIXCLK__CSI_PIXCLK	0x1b088
                        MX6UL_PAD_CSI_VSYNC__CSI_VSYNC		0x1b088
                        MX6UL_PAD_CSI_HSYNC__CSI_HSYNC		0x1b088
                        MX6UL_PAD_CSI_DATA00__CSI_DATA02	0x1b088
                        MX6UL_PAD_CSI_DATA01__CSI_DATA03	0x1b088
                        MX6UL_PAD_CSI_DATA02__CSI_DATA04	0x1b088
                        MX6UL_PAD_CSI_DATA03__CSI_DATA05	0x1b088
                        MX6UL_PAD_CSI_DATA04__CSI_DATA06	0x1b088
                        MX6UL_PAD_CSI_DATA05__CSI_DATA07	0x1b088
                        MX6UL_PAD_CSI_DATA06__CSI_DATA08	0x1b088
                        MX6UL_PAD_CSI_DATA07__CSI_DATA09	0x1b088
                    >;
                };

                pinctrl_enet1: enet1grp {
                    fsl,pins = <
                        MX6UL_PAD_ENET1_RX_EN__ENET1_RX_EN	0x1b0b0
                        MX6UL_PAD_ENET1_RX_ER__ENET1_RX_ER	0x1b0b0
                        MX6UL_PAD_ENET1_RX_DATA0__ENET1_RDATA00	0x1b0b0
                        MX6UL_PAD_ENET1_RX_DATA1__ENET1_RDATA01	0x1b0b0
                        MX6UL_PAD_ENET1_TX_EN__ENET1_TX_EN	0x1b0b0
                        MX6UL_PAD_ENET1_TX_DATA0__ENET1_TDATA00	0x1b0b0
                        MX6UL_PAD_ENET1_TX_DATA1__ENET1_TDATA01	0x1b0b0
                        MX6UL_PAD_ENET1_TX_CLK__ENET1_REF_CLK1	0x4001b031
                    >;
                };

                pinctrl_enet2: enet2grp {
                    fsl,pins = <
                        MX6UL_PAD_GPIO1_IO07__ENET2_MDC		0x1b0b0
                        MX6UL_PAD_GPIO1_IO06__ENET2_MDIO	0x1b0b0
                        MX6UL_PAD_ENET2_RX_EN__ENET2_RX_EN	0x1b0b0
                        MX6UL_PAD_ENET2_RX_ER__ENET2_RX_ER	0x1b0b0
                        MX6UL_PAD_ENET2_RX_DATA0__ENET2_RDATA00	0x1b0b0
                        MX6UL_PAD_ENET2_RX_DATA1__ENET2_RDATA01	0x1b0b0
                        MX6UL_PAD_ENET2_TX_EN__ENET2_TX_EN	0x1b0b0
                        MX6UL_PAD_ENET2_TX_DATA0__ENET2_TDATA00	0x1b0b0
                        MX6UL_PAD_ENET2_TX_DATA1__ENET2_TDATA01	0x1b0b0
                        MX6UL_PAD_ENET2_TX_CLK__ENET2_REF_CLK2	0x4001b031
                    >;
                };

                pinctrl_flexcan1: flexcan1grp {
                    fsl,pins = <
                        MX6UL_PAD_UART3_RTS_B__FLEXCAN1_RX	0x1b020
                        MX6UL_PAD_UART3_CTS_B__FLEXCAN1_TX	0x1b020
                    >;
                };

                pinctrl_i2c1: i2c1grp {
                    fsl,pins = <
                        MX6UL_PAD_UART4_TX_DATA__I2C1_SCL 0x4001b8b0
                        MX6UL_PAD_UART4_RX_DATA__I2C1_SDA 0x4001b8b0
                    >;
                };

                pinctrl_i2c2: i2c2grp {
                    fsl,pins = <
                        MX6UL_PAD_UART5_TX_DATA__I2C2_SCL 0x4001b8b0
                        MX6UL_PAD_UART5_RX_DATA__I2C2_SDA 0x4001b8b0
                    >;
                };

                pinctrl_ecspi3: icm20608 {
                    fsl,pins = <
                        MX6UL_PAD_UART2_TX_DATA__GPIO1_IO20 0x10b0 /* CS */
                        MX6UL_PAD_UART2_RX_DATA__ECSPI3_SCLK 0x10b1 /* SCLK */
                        MX6UL_PAD_UART2_RTS_B__ECSPI3_MISO 0x10b1 /* MISO */
                        MX6UL_PAD_UART2_CTS_B__ECSPI3_MOSI 0x10b1 /* MOSI */
                    >;
                };

                pinctrl_lcdif_dat: lcdifdatgrp {
                    fsl,pins = <
                        MX6UL_PAD_LCD_DATA00__LCDIF_DATA00  0x79
                        MX6UL_PAD_LCD_DATA01__LCDIF_DATA01  0x79
                        MX6UL_PAD_LCD_DATA02__LCDIF_DATA02  0x79
                        MX6UL_PAD_LCD_DATA03__LCDIF_DATA03  0x79
                        MX6UL_PAD_LCD_DATA04__LCDIF_DATA04  0x79
                        MX6UL_PAD_LCD_DATA05__LCDIF_DATA05  0x79
                        MX6UL_PAD_LCD_DATA06__LCDIF_DATA06  0x79
                        MX6UL_PAD_LCD_DATA07__LCDIF_DATA07  0x79
                        MX6UL_PAD_LCD_DATA08__LCDIF_DATA08  0x79
                        MX6UL_PAD_LCD_DATA09__LCDIF_DATA09  0x79
                        MX6UL_PAD_LCD_DATA10__LCDIF_DATA10  0x79
                        MX6UL_PAD_LCD_DATA11__LCDIF_DATA11  0x79
                        MX6UL_PAD_LCD_DATA12__LCDIF_DATA12  0x79
                        MX6UL_PAD_LCD_DATA13__LCDIF_DATA13  0x79
                        MX6UL_PAD_LCD_DATA14__LCDIF_DATA14  0x79
                        MX6UL_PAD_LCD_DATA15__LCDIF_DATA15  0x79
                        MX6UL_PAD_LCD_DATA16__LCDIF_DATA16  0x79
                        MX6UL_PAD_LCD_DATA17__LCDIF_DATA17  0x79
                        MX6UL_PAD_LCD_DATA18__LCDIF_DATA18  0x79
                        MX6UL_PAD_LCD_DATA19__LCDIF_DATA19  0x79
                        MX6UL_PAD_LCD_DATA20__LCDIF_DATA20  0x79
                        MX6UL_PAD_LCD_DATA21__LCDIF_DATA21  0x79
                        MX6UL_PAD_LCD_DATA22__LCDIF_DATA22  0x79
                        MX6UL_PAD_LCD_DATA23__LCDIF_DATA23  0x79
                    >;
                };

                pinctrl_lcdif_ctrl: lcdifctrlgrp {
                    fsl,pins = <
                        MX6UL_PAD_LCD_CLK__LCDIF_CLK	    0x79
                        MX6UL_PAD_LCD_ENABLE__LCDIF_ENABLE  0x79
                        MX6UL_PAD_LCD_HSYNC__LCDIF_HSYNC    0x79
                        MX6UL_PAD_LCD_VSYNC__LCDIF_VSYNC    0x79
                    >;
                };

                pinctrl_qspi: qspigrp {
                    fsl,pins = <
                        MX6UL_PAD_NAND_WP_B__QSPI_A_SCLK	0x70a1
                        MX6UL_PAD_NAND_READY_B__QSPI_A_DATA00	0x70a1
                        MX6UL_PAD_NAND_CE0_B__QSPI_A_DATA01	0x70a1
                        MX6UL_PAD_NAND_CE1_B__QSPI_A_DATA02	0x70a1
                        MX6UL_PAD_NAND_CLE__QSPI_A_DATA03	0x70a1
                        MX6UL_PAD_NAND_DQS__QSPI_A_SS0_B	0x70a1
                    >;
                };

                pinctrl_sai2: sai2grp {
                    fsl,pins = <
                        MX6UL_PAD_JTAG_TDI__SAI2_TX_BCLK	0x17088
                        MX6UL_PAD_JTAG_TDO__SAI2_TX_SYNC	0x17088
                        MX6UL_PAD_JTAG_TRST_B__SAI2_TX_DATA	0x11088
                        MX6UL_PAD_JTAG_TCK__SAI2_RX_DATA	0x11088
                        MX6UL_PAD_JTAG_TMS__SAI2_MCLK		0x17088
                    >;
                };
				pinctrl_sai2_hp_det_b: sai2_hp_det_grp {
					fsl,pins = <
						MX6ULL_PAD_SNVS_TAMPER4__GPIO5_IO04 0x17059
					>;
				};
                pinctrl_peri_3v3: peri3v3grp {
                    fsl,pins = <
                        MX6UL_PAD_SNVS_TAMPER2__GPIO5_IO02	0x1b0b0
                    >;
                };

                pinctrl_pwm1: pwm1grp {
                    fsl,pins = <
                        MX6UL_PAD_GPIO1_IO08__PWM1_OUT   0x110b0
                    >;
                };

                pinctrl_sim2: sim2grp {
                    fsl,pins = <
                        MX6UL_PAD_CSI_DATA03__SIM2_PORT1_PD		0xb808
                        MX6UL_PAD_CSI_DATA04__SIM2_PORT1_CLK		0x31
                        MX6UL_PAD_CSI_DATA05__SIM2_PORT1_RST_B		0xb808
                        MX6UL_PAD_CSI_DATA06__SIM2_PORT1_SVEN		0xb808
                        MX6UL_PAD_CSI_DATA07__SIM2_PORT1_TRXD		0xb809
                        MX6UL_PAD_CSI_DATA02__GPIO4_IO23		0x3008
                    >;
                };

                pinctrl_spi4: spi4grp {
                    fsl,pins = <
                        MX6UL_PAD_BOOT_MODE0__GPIO5_IO10	0x70a1
                        MX6UL_PAD_BOOT_MODE1__GPIO5_IO11	0x70a1
                        MX6UL_PAD_SNVS_TAMPER7__GPIO5_IO07	0x70a1
                        MX6UL_PAD_SNVS_TAMPER8__GPIO5_IO08	0x80000000
                    >;
                };

                pinctrl_uart1: uart1grp {
                    fsl,pins = <
                        MX6UL_PAD_UART1_TX_DATA__UART1_DCE_TX 0x1b0b1
                        MX6UL_PAD_UART1_RX_DATA__UART1_DCE_RX 0x1b0b1
                    >;
                };

                pinctrl_usb_otg1: usbotg1grp {
                    fsl,pins = <
                        MX6UL_PAD_GPIO1_IO00__ANATOP_OTG1_ID	0x17059
                    >;
                };

                pinctrl_usdhc1: usdhc1grp {
                    fsl,pins = <
                        MX6UL_PAD_SD1_CMD__USDHC1_CMD     	0x17059
                        MX6UL_PAD_SD1_CLK__USDHC1_CLK     	0x10059
                        MX6UL_PAD_SD1_DATA0__USDHC1_DATA0 	0x17059
                        MX6UL_PAD_SD1_DATA1__USDHC1_DATA1 	0x17059
                        MX6UL_PAD_SD1_DATA2__USDHC1_DATA2 	0x17059
                        MX6UL_PAD_SD1_DATA3__USDHC1_DATA3 	0x17059
                        MX6UL_PAD_UART1_RTS_B__GPIO1_IO19       0x17059 /* SD1 CD */
                        MX6UL_PAD_GPIO1_IO05__USDHC1_VSELECT    0x17059 /* SD1 VSELECT */
                    >;
                };

                pinctrl_usdhc1_100mhz: usdhc1-100mhz-grp {
                    fsl,pins = <
                        MX6UL_PAD_SD1_CMD__USDHC1_CMD     0x170b9
                        MX6UL_PAD_SD1_CLK__USDHC1_CLK     0x100b9
                        MX6UL_PAD_SD1_DATA0__USDHC1_DATA0 0x170b9
                        MX6UL_PAD_SD1_DATA1__USDHC1_DATA1 0x170b9
                        MX6UL_PAD_SD1_DATA2__USDHC1_DATA2 0x170b9
                        MX6UL_PAD_SD1_DATA3__USDHC1_DATA3 0x170b9

                    >;
                };

                pinctrl_usdhc1_200mhz: usdhc1-200mhz-grp {
                    fsl,pins = <
                        MX6UL_PAD_SD1_CMD__USDHC1_CMD     0x170f9
                        MX6UL_PAD_SD1_CLK__USDHC1_CLK     0x100f9
                        MX6UL_PAD_SD1_DATA0__USDHC1_DATA0 0x170f9
                        MX6UL_PAD_SD1_DATA1__USDHC1_DATA1 0x170f9
                        MX6UL_PAD_SD1_DATA2__USDHC1_DATA2 0x170f9
                        MX6UL_PAD_SD1_DATA3__USDHC1_DATA3 0x170f9
                    >;
                };

                pinctrl_usdhc2: usdhc2grp {
                    fsl,pins = <
                        MX6UL_PAD_NAND_RE_B__USDHC2_CLK     0x10079
                        MX6UL_PAD_NAND_WE_B__USDHC2_CMD     0x17079
                        MX6UL_PAD_NAND_DATA00__USDHC2_DATA0 0x17079
                        MX6UL_PAD_NAND_DATA01__USDHC2_DATA1 0x17079
                        MX6UL_PAD_NAND_DATA02__USDHC2_DATA2 0x17079
                        MX6UL_PAD_NAND_DATA03__USDHC2_DATA3 0x17079
                        MX6UL_PAD_NAND_DATA04__USDHC2_DATA4 0x17079
                        MX6UL_PAD_NAND_DATA05__USDHC2_DATA5 0x17079
                        MX6UL_PAD_NAND_DATA06__USDHC2_DATA6 0x17079
                        MX6UL_PAD_NAND_DATA07__USDHC2_DATA7 0x17079
                        MX6UL_PAD_NAND_ALE__USDHC2_RESET_B  0x17079  /* eMMC RESET */
                    >;
                };

                pinctrl_usdhc2_100mhz: usdhc2-100mhz-grp {
                    fsl,pins = <
                        MX6UL_PAD_NAND_RE_B__USDHC2_CLK     0x100b9
                        MX6UL_PAD_NAND_WE_B__USDHC2_CMD     0x170b9
                        MX6UL_PAD_NAND_DATA00__USDHC2_DATA0 0x170b9
                        MX6UL_PAD_NAND_DATA01__USDHC2_DATA1 0x170b9
                        MX6UL_PAD_NAND_DATA02__USDHC2_DATA2 0x170b9
                        MX6UL_PAD_NAND_DATA03__USDHC2_DATA3 0x170b9
                        MX6UL_PAD_NAND_DATA04__USDHC2_DATA4 0x170b9
                        MX6UL_PAD_NAND_DATA05__USDHC2_DATA5 0x170b9
                        MX6UL_PAD_NAND_DATA06__USDHC2_DATA6 0x170b9
                        MX6UL_PAD_NAND_DATA07__USDHC2_DATA7 0x170b9
                        MX6UL_PAD_NAND_ALE__USDHC2_RESET_B  0x170b9
                    >;
                };

                pinctrl_usdhc2_200mhz: usdhc2-200mhz-grp {
                    fsl,pins = <
                        MX6UL_PAD_NAND_RE_B__USDHC2_CLK     0x100f9
                        MX6UL_PAD_NAND_WE_B__USDHC2_CMD     0x170f9
                        MX6UL_PAD_NAND_DATA00__USDHC2_DATA0 0x170f9
                        MX6UL_PAD_NAND_DATA01__USDHC2_DATA1 0x170f9
                        MX6UL_PAD_NAND_DATA02__USDHC2_DATA2 0x170f9
                        MX6UL_PAD_NAND_DATA03__USDHC2_DATA3 0x170f9
                        MX6UL_PAD_NAND_DATA04__USDHC2_DATA4 0x170f9
                        MX6UL_PAD_NAND_DATA05__USDHC2_DATA5 0x170f9
                        MX6UL_PAD_NAND_DATA06__USDHC2_DATA6 0x170f9
                        MX6UL_PAD_NAND_DATA07__USDHC2_DATA7 0x170f9
                        MX6UL_PAD_NAND_ALE__USDHC2_RESET_B  0x170f9
                    >;
                };

                pinctrl_wdog: wdoggrp {
                    fsl,pins = <
                        MX6UL_PAD_LCD_RESET__WDOG1_WDOG_ANY    0x30b0
                    >;
                };
			};

			gpr: iomuxc-gpr@20e4000 {
				compatible = "fsl,imx6ul-iomuxc-gpr",
					     "fsl,imx6q-iomuxc-gpr", "syscon";
				reg = <0x020e4000 0x4000>;
			};

			gpt2: timer@20e8000 {
				compatible = "fsl,imx6ul-gpt", "fsl,imx6sx-gpt";
				reg = <0x020e8000 0x4000>;
				interrupts = <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPT2_BUS>,
					 <&clks IMX6UL_CLK_GPT2_SERIAL>;
				clock-names = "ipg", "per";
				status = "disabled";
			};

			sdma: dma-controller@20ec000 {
				compatible = "fsl,imx6ul-sdma", "fsl,imx6q-sdma",
					     "fsl,imx35-sdma";
				reg = <0x020ec000 0x4000>;
				interrupts = <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_IPG>,
					 <&clks IMX6UL_CLK_SDMA>;
				clock-names = "ipg", "ahb";
				#dma-cells = <3>;
				fsl,sdma-ram-script-name = "imx/sdma/sdma-imx6q.bin";
			};

			pwm5: pwm@20f0000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x020f0000 0x4000>;
				interrupts = <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM5>,
					 <&clks IMX6UL_CLK_PWM5>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm6: pwm@20f4000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x020f4000 0x4000>;
				interrupts = <GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM6>,
					 <&clks IMX6UL_CLK_PWM6>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm7: pwm@20f8000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x020f8000 0x4000>;
				interrupts = <GIC_SPI 116 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM7>,
					 <&clks IMX6UL_CLK_PWM7>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm8: pwm@20fc000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x020fc000 0x4000>;
				interrupts = <GIC_SPI 117 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM8>,
					 <&clks IMX6UL_CLK_PWM8>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};
		};

		aips2: bus@2100000 {
			compatible = "fsl,aips-bus", "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x02100000 0x100000>;
			ranges;

			usbotg1: usb@2184000 {
				compatible = "fsl,imx6ul-usb", "fsl,imx27-usb";
				reg = <0x02184000 0x200>;
				interrupts = <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_USBOH3>;
				fsl,usbphy = <&usbphy1>;
				fsl,usbmisc = <&usbmisc 0>;
				ahb-burst-config = <0x0>;
				tx-burst-size-dword = <0x10>;
				rx-burst-size-dword = <0x10>;
				dr_mode = "otg";
                pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_usb_otg1>;
                status = "okay";
			};

			usbotg2: usb@2184200 {
				compatible = "fsl,imx6ul-usb", "fsl,imx27-usb";
				reg = <0x02184200 0x200>;
				interrupts = <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_USBOH3>;
				fsl,usbphy = <&usbphy2>;
				fsl,usbmisc = <&usbmisc 1>;
				ahb-burst-config = <0x0>;
				tx-burst-size-dword = <0x10>;
				rx-burst-size-dword = <0x10>;
				dr_mode = "host";
                disable-over-current;
                status = "okay";
			};

			usbmisc: usbmisc@2184800 {
				#index-cells = <1>;
				compatible = "fsl,imx6ul-usbmisc", "fsl,imx6q-usbmisc";
				reg = <0x02184800 0x200>;
			};

			fec1: ethernet@2188000 {
				compatible = "fsl,imx6ul-fec", "fsl,imx6q-fec";
				reg = <0x02188000 0x4000>;
				interrupt-names = "int0", "pps";
				interrupts = <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_ENET>,
					 <&clks IMX6UL_CLK_ENET_AHB>,
					 <&clks IMX6UL_CLK_ENET_PTP>,
					 <&clks IMX6UL_CLK_ENET1_REF_SEL>;
				clock-names = "ipg", "ahb", "ptp",
					      "enet_clk_ref";
				fsl,num-tx-queues = <1>;
				fsl,num-rx-queues = <1>;
				fsl,stop-mode = <&gpr 0x10 3>;
				fsl,magic-packet;
				nvmem-cells = <&fec1_mac_addr>;
				nvmem-cell-names = "mac-address";
				pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_enet1
                                &pinctrl_enet1_reset>;
                phy-reset-gpios = <&gpio5 7 GPIO_ACTIVE_LOW>;
                phy-reset-duration = <200>;
                phy-mode = "rmii";
                phy-handle = <&ethphy0>;
                phy-supply = <&reg_peri_3v3>;
                status = "okay";
			};

			usdhc1: mmc@2190000 {
				compatible = "fsl,imx6ull-usdhc", "fsl,imx6sx-usdhc";
				reg = <0x02190000 0x4000>;
				interrupts = <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_USDHC1>,
					 <&clks IMX6UL_CLK_USDHC1>,
					 <&clks IMX6UL_CLK_USDHC1>;
				clock-names = "ipg", "ahb", "per";
				fsl,tuning-step = <2>;
				fsl,tuning-start-tap = <20>;
				bus-width = <4>;
				pinctrl-names = "default", "state_100mhz", "state_200mhz";
                pinctrl-0 = <&pinctrl_usdhc1>;
                pinctrl-1 = <&pinctrl_usdhc1_100mhz>;
                pinctrl-2 = <&pinctrl_usdhc1_200mhz>;
                cd-gpios = <&gpio1 19 GPIO_ACTIVE_LOW>;
                keep-power-in-suspend;
                wakeup-source;
                vmmc-supply = <&reg_sd1_vmmc>;
                status = "okay";
			};

			usdhc2: mmc@2194000 {
				compatible = "fsl,imx6ull-usdhc", "fsl,imx6sx-usdhc";
				reg = <0x02194000 0x4000>;
				interrupts = <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_USDHC2>,
					 <&clks IMX6UL_CLK_USDHC2>,
					 <&clks IMX6UL_CLK_USDHC2>;
				clock-names = "ipg", "ahb", "per";
				fsl,tuning-step = <2>;
				fsl,tuning-start-tap = <20>;
				pinctrl-names = "default", "state_100mhz", "state_200mhz";
                pinctrl-0 = <&pinctrl_usdhc2>;
                pinctrl-1 = <&pinctrl_usdhc2_100mhz>;
                pinctrl-2 = <&pinctrl_usdhc2_200mhz>;
                bus-width = <8>;
                no-1-8-v;
                broken-cd;
                keep-power-in-suspend;
                wakeup-source;
                status = "okay";
			};

			adc1: adc@2198000 {
				compatible = "fsl,imx6ul-adc", "fsl,vf610-adc";
				reg = <0x02198000 0x4000>;
				interrupts = <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_ADC1>;
				clock-names = "adc";
				fsl,adck-max-frequency = <30000000>, <40000000>,
							 <20000000>;
				status = "disabled";
			};

			i2c1: i2c@21a0000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx6ul-i2c", "fsl,imx21-i2c";
				reg = <0x021a0000 0x4000>;
				interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_I2C1>;
				clock-frequency = <100000>;
                pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_i2c1>;
                status = "okay";
                
				codec: wm8960@1a {
					compatible = "wlf,wm8960";
					reg = <0x1a>;
					clocks = <&clks IMX6UL_CLK_SAI2>;
					clock-names = "mclk";
					wlf,shared-lrclk;
				};

                ap3216c@1e {
                    compatible = "zexuan,ap3216c";
                    reg = <0x1e>;
                };
			};

			i2c2: i2c@21a4000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx6ul-i2c", "fsl,imx21-i2c";
				reg = <0x021a4000 0x4000>;
				interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_I2C2>;
                clock-frequency = <100000>;
                pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_i2c2>;
				status = "okay";

				gt9147:gt9147@14 {
					compatible = "goodix,gt9147", "goodix,gt9xx";
					reg = <0x14>;
					pinctrl-names = "default";
					pinctrl-0 = <&pinctrl_tsc
							&pinctrl_tsc_reset>;
					interrupt-parent = <&gpio1>;
					interrupts = <9 0>;
					reset-gpios = <&gpio5 9 GPIO_ACTIVE_LOW>;
					interrupt-gpios = <&gpio1 9 GPIO_ACTIVE_LOW>;
					status = "okay"; 
				};

			};

			i2c3: i2c@21a8000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx6ul-i2c", "fsl,imx21-i2c";
				reg = <0x021a8000 0x4000>;
				interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_I2C3>;
				status = "disabled";
			};

			memory-controller@21b0000 {
				compatible = "fsl,imx6ul-mmdc", "fsl,imx6q-mmdc";
				reg = <0x021b0000 0x4000>;
				clocks = <&clks IMX6UL_CLK_MMDC_P0_IPG>;
			};

			weim: memory-controller@21b8000 {
				#address-cells = <2>;
				#size-cells = <1>;
				compatible = "fsl,imx6ul-weim", "fsl,imx6q-weim";
				reg = <0x021b8000 0x4000>;
				interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_EIM>;
				fsl,weim-cs-gpr = <&gpr>;
				status = "disabled";
			};

			ocotp: efuse@21bc000 {
				#address-cells = <1>;
				#size-cells = <1>;
				compatible = "fsl,imx6ull-ocotp", "syscon";
				reg = <0x021bc000 0x4000>;
				clocks = <&clks IMX6UL_CLK_OCOTP>;

				tempmon_calib: calib@38 {
					reg = <0x38 4>;
				};

				tempmon_temp_grade: temp-grade@20 {
					reg = <0x20 4>;
				};

				cpu_speed_grade: speed-grade@10 {
					reg = <0x10 4>;
				};

				fec1_mac_addr: mac-addr@88 {
					reg = <0x88 6>;
				};

				fec2_mac_addr: mac-addr@8e {
					reg = <0x8e 6>;
				};
			};

			lcdif: lcdif@21c8000 {
				compatible = "fsl,imx6ul-lcdif", "fsl,imx6sx-lcdif";
				reg = <0x021c8000 0x4000>;
				interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_LCDIF_PIX>,
					 <&clks IMX6UL_CLK_LCDIF_APB>,
					 <&clks IMX6UL_CLK_DUMMY>;
				clock-names = "pix", "axi", "disp_axi";
				assigned-clocks = <&clks IMX6UL_CLK_LCDIF_PRE_SEL>;
                assigned-clock-parents = <&clks IMX6UL_CLK_PLL5_VIDEO_DIV>;
                pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_lcdif_dat
                        &pinctrl_lcdif_ctrl>;
                status = "okay";

                display-timings {
                    native-mode = <&timing0>;
                    timing0: timing0 {
                        bits-per-pixel = <24>;
                        bus-width = <24>;
                    };
                };

                port {
                    display_out: endpoint {
                        remote-endpoint = <&panel_in>;
                    };
                };
			};

			pxp: pxp@21cc000 {
				compatible = "fsl,imx6ull-pxp";
				reg = <0x021cc000 0x4000>;
				interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
		                    <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PXP>;
				clock-names = "axi";
			};

			qspi: spi@21e0000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx6ul-qspi";
				reg = <0x021e0000 0x4000>, <0x60000000 0x10000000>;
				reg-names = "QuadSPI", "QuadSPI-memory";
				interrupts = <GIC_SPI 107 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_QSPI>,
					 <&clks IMX6UL_CLK_QSPI>;
				clock-names = "qspi_en", "qspi";
				pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_qspi>;
                status = "okay";

                flash0: flash@0 {
                    #address-cells = <1>;
                    #size-cells = <1>;
                    compatible = "micron,n25q256a", "jedec,spi-nor";
                    spi-max-frequency = <29000000>;
                    spi-rx-bus-width = <4>;
                    spi-tx-bus-width = <1>;
                    reg = <0>;
                };
			};

			wdog3: watchdog@21e4000 {
				compatible = "fsl,imx6ul-wdt", "fsl,imx21-wdt";
				reg = <0x021e4000 0x4000>;
				interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_WDOG3>;
				status = "disabled";
			};

			uart3: serial@21ec000 {
				compatible = "fsl,imx6ul-uart",
					     "fsl,imx6q-uart";
				reg = <0x021ec000 0x4000>;
				interrupts = <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_UART3_IPG>,
					 <&clks IMX6UL_CLK_UART3_SERIAL>;
				clock-names = "ipg", "per";
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_uart3>;
				status = "okay";
			};

			uart4: serial@21f0000 {
				compatible = "fsl,imx6ul-uart",
					     "fsl,imx6q-uart";
				reg = <0x021f0000 0x4000>;
				interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_UART4_IPG>,
					 <&clks IMX6UL_CLK_UART4_SERIAL>;
				clock-names = "ipg", "per";
				status = "disabled";
			};

			uart5: serial@21f4000 {
				compatible = "fsl,imx6ul-uart",
					     "fsl,imx6q-uart";
				reg = <0x021f4000 0x4000>;
				interrupts = <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_UART5_IPG>,
					 <&clks IMX6UL_CLK_UART5_SERIAL>;
				clock-names = "ipg", "per";
				status = "disabled";
			};

			i2c4: i2c@21f8000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx6ul-i2c", "fsl,imx21-i2c";
				reg = <0x021f8000 0x4000>;
				interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_I2C4>;
				status = "disabled";
			};

			uart6: serial@21fc000 {
				compatible = "fsl,imx6ul-uart",
					     "fsl,imx6q-uart";
				reg = <0x021fc000 0x4000>;
				interrupts = <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_UART6_IPG>,
					 <&clks IMX6UL_CLK_UART6_SERIAL>;
				clock-names = "ipg", "per";
				status = "disabled";
			};
		};
        aips3: bus@2200000 {
			compatible = "fsl,aips-bus", "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x02200000 0x100000>;
			ranges;

			dcp: crypto@2280000 {
				compatible = "fsl,imx6ull-dcp", "fsl,imx28-dcp";
				reg = <0x02280000 0x4000>;
				interrupts = <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 47 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 48 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6ULL_CLK_DCP_CLK>;
				clock-names = "dcp";
			};

			rngb: rng@2284000 {
				compatible = "fsl,imx6ull-rngb", "fsl,imx25-rngb";
				reg = <0x02284000 0x4000>;
				interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_DUMMY>;
			};

			iomuxc_snvs: pinctrl@2290000 {
				compatible = "fsl,imx6ull-iomuxc-snvs";
				reg = <0x02290000 0x4000>;
                pinctrl_enet1_reset: enet1resetgrp {
                    fsl,pins = <
                        MX6ULL_PAD_SNVS_TAMPER7__GPIO5_IO07 0x10B0
                    >;
                };

                pinctrl_enet2_reset: enet2resetgrp {
                    fsl,pins = <
                        MX6ULL_PAD_SNVS_TAMPER8__GPIO5_IO08 0x10B0
                    >;
                };
				pinctrl_tsc_reset: tsc_reset {
					fsl,pins = <
						MX6ULL_PAD_SNVS_TAMPER9__GPIO5_IO09 0x10B0
					>;
				};
			};

			uart8: serial@2288000 {
				compatible = "fsl,imx6ul-uart",
					     "fsl,imx6q-uart";
				reg = <0x02288000 0x4000>;
				interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_UART8_IPG>,
					 <&clks IMX6UL_CLK_UART8_SERIAL>;
				clock-names = "ipg", "per";
				status = "disabled";
			};
		};
	};
};


```
#### imx6ull.dtsi
CPU配置（频率、电压等）
删除了i.MX6UL特有的UART8和CAAM节点
修改了一些外设的兼容性声明
添加了DCP（加密引擎）和RNGB（随机数生成器）等i.MX6ULL特有外设
#### imx6ul.dtsi
内存控制器（WEIM）,OCOTP（熔丝位）,CSI（摄像头接口）,LCDIF（LCD控制器）,PXP（像素处理引擎）,UART（串口）,I2C控制器,SPI控制器,USB控制器,SD/MMC控制器,GPIO控制器,以太网控制器,PWM控制器,ADC控制器,看门狗定时器
#### imx6ul-14x14-evk.dtsi
显示相关：
LCD接口配置,背光控制,PWM配置
通信接口：
以太网（FEC1/FEC2）配置,UART配置,I2C配置,USB配置
存储：
QSPI Flash配置,SD卡接口配置
其他外设：
音频接口（SAI2）,CAN总线,触摸屏（TSC）,引脚复用配置（pinctrl）,LCD引脚,UART引脚,以太网引脚,SD卡引脚,USB引脚,PWM引脚等



# rootfs
## ubuntu-base

### 安装基础开发工具
apt-get install -y build-essential git vim nano wget curl openssh-server net-tools iputils-ping iproute2 htop usbutils i2c-tools python3 python3-pip python3-dev sudo locales tzdata ca-certificates

sudo apt install libts-dev libts-bin minicom alsa-utils
### 设置时区
ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
### 设置语言环境
locale-gen en_US.UTF-8
update-locale LANG=en_US.UTF-8
### 配置SSH服务
sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config
### 设置root密码
passwd

## networkmanager
/etc/NetworkManager/NetworkManager.conf
  [ifupdown]
  managed=true

/usr/lib/NetworkManager/conf.d/10-globally-managed-devices.conf
  [keyfile]
  unmanaged-devices=*,except:type:wifi,except:type:gsm,except:type:cdma,except:type:ethernet
  
systemctl restart NetworkManager

nmcli device wifi connect "23-1506-2.4G" password "13815206023"

### wifi 
CONFIG_RTL8XXXU=m

需要下载linux-firmwire

cat >> "/etc/modules" << 'EOF'
rtl8xxxu
EOF
设置自动加载模块
# image

## 脚本
```

# 设置字体颜色
STEPS="[\033[95m STEPS \033[0m]"
INFO="[\033[94m INFO \033[0m]"
WARNING="[\033[93m WARNING \033[0m]"
SUCCESS="[\033[92m SUCCESS \033[0m]"
ERROR="[\033[91m ERROR \033[0m]"

# 设置路径
current_path="${PWD}"
tmp_dir="${current_path}/tmp"

# 设置默认参数
boot_mb="64"
root_mb="1024"

# 设置固定文件
uboot_file="ubuntu-base/u-boot-imx6ull-14x14-ddr512-emmc.imx"
kernel_file="ubuntu-base/zImage"
dtb_file="ubuntu-base/imx6ull-14x14-evk.dtb"

error_msg() {
    echo -e "${ERROR} ${1}"
    exit 1
}

process_msg() {
    echo -e "${INFO} ${1}"
}

mount_try() {
    local m_type="${1}"
    local m_dev="${2}"
    local m_target="${3}"
    
    [[ -n "${m_type}" && -n "${m_dev}" && -n "${m_target}" ]] || {
        error_msg "挂载参数缺失: [ ${m_type}, ${m_dev}, ${m_target} ]"
    }

    process_msg "尝试挂载 ${m_dev} 到 ${m_target}"

    local t="1"
    local max_try="5"
    while [[ "${t}" -le "${max_try}" ]]; do
        if mount -t "${m_type}" "${m_dev}" "${m_target}"; then
            process_msg "挂载成功: ${m_dev} -> ${m_target}"
            return 0
        else
            process_msg "第 ${t} 次挂载失败，重试..."
            sync && sleep 2
            umount -f "${m_target}" 2>/dev/null
            t="$((t + 1))"
        fi
    done
    error_msg "挂载失败，已尝试 ${t} 次"
}

check_files() {
    process_msg "检查必要文件..."
    
    # 检查目录结构
    [[ -d "${current_path}/boot" ]] || error_msg "boot 目录不存在"

    # 检查关键文件
    [[ -f "${current_path}/boot/${uboot_file}" ]] || error_msg "${uboot_file} 不存在"
    [[ -f "${current_path}/boot/${kernel_file}" ]] || error_msg "${kernel_file} 不存在"
    [[ -f "${current_path}/boot/${dtb_file}" ]] || error_msg "${dtb_file} 不存在"

    # 检查必要工具
    command -v mkfs.vfat >/dev/null || error_msg "请安装 dosfstools: apt-get install dosfstools"
    command -v parted >/dev/null || error_msg "请安装 parted: apt-get install parted"
}

make_image() {
    process_msg "准备对SD卡进行分区..."

    # SD卡设备
    local sd_dev="/dev/mmcblk1"

    # 检查设备是否存在
    [[ -b "${sd_dev}" ]] || error_msg "找不到SD卡设备 ${sd_dev}"

    # 确保设备未被挂载
    process_msg "确保SD卡未被挂载..."
    for part in $(mount | grep "${sd_dev}" | cut -d' ' -f1); do
        umount "${part}" 2>/dev/null
    done

    # 创建分区表
    process_msg "创建分区表..."
    parted -s "${sd_dev}" mklabel msdos
    parted -s "${sd_dev}" mkpart primary fat32 1MiB ${boot_mb}MiB
    parted -s "${sd_dev}" mkpart primary ext4 ${boot_mb}MiB 100%
    parted -s "${sd_dev}" set 1 boot on

    # 确保分区表更新
    process_msg "等待分区表更新..."
    partprobe "${sd_dev}"
    sleep 2

    # 强制内核重新读取分区表
    blockdev --rereadpt "${sd_dev}"
    
    # 等待udev完成设备节点创建
    process_msg "等待设备节点创建..."
    udevadm settle
    sleep 2

    # 验证分区设备是否存在
    for i in {1..10}; do
        if [[ -b "${sd_dev}p1" && -b "${sd_dev}p2" ]]; then
            process_msg "分区设备已就绪"
            break
        fi
        process_msg "等待分区设备就绪，尝试 ${i}/10..."
        sleep 1
    done

    # 最后检查
    [[ -b "${sd_dev}p1" ]] || error_msg "无法找到第一个分区设备"
    [[ -b "${sd_dev}p2" ]] || error_msg "无法找到第二个分区设备"
    
    # 格式化分区
    process_msg "格式化分区..."
    mkfs.vfat -n "boot" "${sd_dev}p1"
    mkfs.ext4 -F -L "rootfs" "${sd_dev}p2"

    # 写入u-boot
    process_msg "写入 u-boot..."
    dd if="${current_path}/boot/${uboot_file}" of="${sd_dev}" bs=1k seek=1 conv=fsync

    process_msg "复制文件到SD卡..."

    # 创建挂载点
    mkdir -p "${tmp_dir}/boot" "${tmp_dir}/rootfs"

    # 挂载分区
    mount_try vfat "${sd_dev}p1" "${tmp_dir}/boot"
    mount_try ext4 "${sd_dev}p2" "${tmp_dir}/rootfs"

    # 复制启动相关文件
    process_msg "复制启动文件..."
    cp "${current_path}/boot/${kernel_file}" "${tmp_dir}/boot/"
    cp "${current_path}/boot/${dtb_file}" "${tmp_dir}/boot/"

    # 解压根文件系统
    process_msg "解压根文件系统..."
    # tar xpf "${current_path}/filesystem/rootfs.tar.bz2" -C "${tmp_dir}/rootfs" --numeric-owner --warning=no-timestamp
    # tar jxfm "${current_path}/modules/modules.tar.bz2" -C "${tmp_dir}/rootfs/lib/modules/"
    cp -arf ${current_path}/rootfs/* "${tmp_dir}/rootfs/"
    sync
}

clean_tmp() {
    process_msg "清理临时文件..."
    
    # 卸载所有挂载
    umount "${tmp_dir}/boot" 2>/dev/null
    umount "${tmp_dir}/rootfs" 2>/dev/null
    
    # 删除临时目录
    rm -rf "${tmp_dir}"
    
    sync
}

main() {
    echo -e "${STEPS} 开始制作镜像..."
    
    # 检查必要文件
    check_files
    
    # 制作镜像
    make_image

    # 清理临时文件
    clean_tmp

    echo -e "${SUCCESS} 镜像制作完成！"
}

main 

```
## 版本号

 git tag -d v6.12.28 && git tag -a v6.12.28 -m "Release v6.12.28" && make kernelrelease
# driver

## 内存映射

/* 1. 普通硬件寄存器访问 - 使用 MT_DEVICE */
void __iomem *gpio_base = ioremap(GPIO_BASE_ADDR, GPIO_SIZE);
// 对应 MT_DEVICE：不可缓存，严格顺序访问

/* 2. 显存/帧缓冲区 - 使用 MT_DEVICE_WC */
void __iomem *fb_mem = ioremap_wc(FB_BASE_ADDR, FB_SIZE);
// 对应 MT_DEVICE_WC：允许写合并，提高显示性能

/* 3. 高频访问的设备内存 - 使用 MT_DEVICE_CACHED */
void __iomem *dma_buf = ioremap_cache(DMA_BUF_ADDR, DMA_BUF_SIZE);
// 对应 MT_DEVICE_CACHED：允许缓存，但需要手动同步

/* 4. CPU私有设备 - 使用 MT_DEVICE_NONSHARED */
void __iomem *cpu_local = ioremap_nocache(CPU_LOCAL_ADDR, CPU_LOCAL_SIZE);
// 对应 MT_DEVICE_NONSHARED：非共享，当前CPU专用

## 驱动结构
0. 物理内存映射到虚拟内存上 io_remap
    用设备树的话，就直接配合pinctrl和gpio子系统了，不需要内存映射了
1. 获取设备号 register_chrdev_region或者alloc_chrdev_region
2. 绑定驱动相关函数 cdev_init 和cdev_add
3. 联动udev自动创建设备节点

```
// 在驱动中
class_create(NEWCHRLED_NAME);  // 创建 /sys/class/newchrled/
device_create(...);            // 创建设备属性，触发 uevent

// udev 接收到事件后
// 1. 读取 /sys/class/newchrled/newchrled/dev 获取设备号
// 2. 在 /dev 下创建对应的设备节点
// 3. 设置正确的权限
```

### pinctrl和gpio
pinctrl用于初始化寄存器，设置某个 PIN 的复用功能、速度、上下拉，将其设置为gpio功能后，gpio用于设置他的输入输出
前面显示引脚名称然后是功能选择，后面用来设置电气属性 比如MX6UL_PAD_UART1_RTS_B__GPIO1_IO19 0x17059
前面的宏分别是 mux_reg寄存器偏移地址 conf_reg寄存器偏移地址 input_reg寄存器偏移地址 mux_reg寄存器值 input_reg寄存器值 0x17059就是conf_reg寄存器值了

cd-gpios = <&gpio1 19 GPIO_ACTIVE_LOW>;
表示低电平有效

#### gpio使用过程
1. 获取设备节点of_find_node_by_path
2. 获取设备树中的gpio属性，得到LED所使用的LED编号of_get_named_gpio
3. 设置GPIO1_IO03为输出gpio_direction_output

## 定时器
timer_setup(&timerdev.timer, timer_function, 0);
初始化定时器
主要是设置回调函数
mod_timer(&dev->timer, jiffies + msecs_to_jiffies(timerperiod)); 
修改定时器下次回调的时间

## 中断
软中断一般内核驱动使用，tasklet已废弃
先注册中断，回调中清除终端标记，然后执行工作队列

```
struct spi_device_data {
    struct spi_device *spi;
    struct work_struct tx_work;
    struct workqueue_struct *wq;
    u8 *tx_buffer;
    int tx_len;
};

static irqreturn_t spi_irq_handler(int irq, void *dev_id)
{
    struct spi_device_data *spi_data = dev_id;
    
    // 快速处理：清除中断标志
    clear_spi_interrupt();
    
    // 调度工作队列处理数据传输
    queue_work(spi_data->wq, &spi_data->tx_work);
    
    return IRQ_HANDLED;
}

static void spi_tx_work_handler(struct work_struct *work)
{
    struct spi_device_data *spi_data = 
        container_of(work, struct spi_device_data, tx_work);
    
    // 可以睡眠的SPI传输
    struct spi_transfer transfer = {
        .tx_buf = spi_data->tx_buffer,
        .len = spi_data->tx_len,
    };
    
    struct spi_message message;
    spi_message_init(&message);
    spi_message_add_tail(&transfer, &message);
    
    // 同步传输（可能睡眠）
    spi_sync(spi_data->spi, &message);
}

static int spi_device_probe(struct spi_device *spi)
{
    struct spi_device_data *spi_data;
    
    // ... 初始化代码 ...
    
    // 创建专用工作队列
    spi_data->wq = create_singlethread_workqueue("spi-tx");
    INIT_WORK(&spi_data->tx_work, spi_tx_work_handler);
    
    // 注册中断
    request_irq(spi->irq, spi_irq_handler, 0, "spi-device", spi_data);
    
    return 0;
}
```

## 阻塞io和非阻塞io
阻塞io搭配等待队列
使用等待队列实现阻塞访问重点注意两点：
①、将任务或者进程加入到等待队列头，
②、在合适的点唤醒等待队列，一般都是中断处理函数里面。

```
#include <linux/types.h>
#include <linux/kernel.h>
#include <linux/delay.h>
#include <linux/fs.h>
#include <linux/init.h>
#include <linux/module.h>
#include <linux/errno.h>
#include <linux/gpio.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/of.h>
#include <linux/of_address.h>
#include <linux/of_gpio.h>
#include <linux/semaphore.h>
#include <linux/timer.h>
#include <linux/of_irq.h>
#include <linux/irq.h>
#include <linux/interrupt.h>
#include <linux/wait.h>
#include <linux/poll.h>
#include <linux/platform_device.h>
#include <linux/uaccess.h>
#include <asm/io.h>
/***************************************************************
Copyright © ALIENTEK Co., Ltd. 1998-2029. All rights reserved.
文件名		: noblock.c
作者	  	: 左忠凯
版本	   	: V1.0
描述	   	: 非阻塞IO访问
其他	   	: 无
论坛 	   	: www.openedv.com
日志	   	: 初版V1.0 2019/7/26 左忠凯创建
***************************************************************/
#define IMX6UIRQ_CNT		1			/* 设备号个数 	*/
#define IMX6UIRQ_NAME		"noblockio"	/* 名字 		*/
#define KEY0VALUE			0X01		/* KEY0按键值 	*/
#define INVAKEY				0XFF		/* 无效的按键值 */
#define KEY_NUM				1			/* 按键数量 	*/

/* 中断IO描述结构体 */
struct irq_keydesc {
	int gpio;								/* gpio */
	int irqnum;								/* 中断号     */
	unsigned char value;					/* 按键对应的键值 */
	char name[10];							/* 名字 */
	irqreturn_t (*handler)(int, void *);	/* 中断服务函数 */
};

/* imx6uirq设备结构体 */
struct imx6uirq_dev{
	dev_t devid;			/* 设备号 	 */	
	struct cdev cdev;		/* cdev 	*/                 
	struct class *class;	/* 类 		*/
	struct device *device;	/* 设备 	 */
	int major;				/* 主设备号	  */
	int minor;				/* 次设备号   */
	struct device_node	*nd; /* 设备节点 */	
	atomic_t keyvalue;		/* 有效的按键键值 */
	atomic_t releasekey;	/* 标记是否完成一次完成的按键，包括按下和释放 */
	struct timer_list timer;/* 定义一个定时器*/
	struct irq_keydesc irqkeydesc[KEY_NUM];	/* 按键init述数组 */
	unsigned char curkeynum;				/* 当前init按键号 */

	wait_queue_head_t r_wait;	/* 读等待队列头 */
};

struct imx6uirq_dev imx6uirq;	/* irq设备 */

/* @description		: 中断服务函数，开启定时器		
 *				  	  定时器用于按键消抖。
 * @param - irq 	: 中断号 
 * @param - dev_id	: 设备结构。
 * @return 			: 中断执行结果
 */
static irqreturn_t key0_handler(int irq, void *dev_id)
{
	struct imx6uirq_dev *dev = (struct imx6uirq_dev*)dev_id;

	dev->curkeynum = 0;
	mod_timer(&dev->timer, jiffies + msecs_to_jiffies(10));	/* 10ms定时 */
	return IRQ_RETVAL(IRQ_HANDLED);
}

/* @description	: 定时器服务函数，用于按键消抖，定时器到了以后
 *				  再次读取按键值，如果按键还是处于按下状态就表示按键有效。
 * @param - t	: 定时器结构体指针
 * @return 		: 无
 */
static void timer_function(struct timer_list *t)
{
	unsigned char value;
	unsigned char num;
	struct irq_keydesc *keydesc;
	struct imx6uirq_dev *dev = from_timer(dev, t, timer);

	num = dev->curkeynum;
	keydesc = &dev->irqkeydesc[num];

	value = gpio_get_value(keydesc->gpio); 	/* 读取IO值 */
	if(value == 0){ 						/* 按下按键 */
		atomic_set(&dev->keyvalue, keydesc->value);
	}
	else{ 									/* 按键松开 */
		atomic_set(&dev->keyvalue, 0x80 | keydesc->value);
		atomic_set(&dev->releasekey, 1);	/* 标记松开按键，即完成一次完整的按键过程 */
	}               

	/* 唤醒进程 */
	if(atomic_read(&dev->releasekey)) {	/* 完成一次按键过程 */
		wake_up_interruptible(&dev->r_wait);
	}
}

/*
 * @description	: 按键IO初始化
 * @param 		: 无
 * @return 		: 无
 */
static int keyio_init(void)
{
	unsigned char i = 0;
	char name[10];
	int ret = 0;
	
	imx6uirq.nd = of_find_node_by_path("/key");
	if (imx6uirq.nd== NULL){
		printk("key node not find!\r\n");
		return -EINVAL;
	} 

	/* 提取GPIO */
	for (i = 0; i < KEY_NUM; i++) {
		imx6uirq.irqkeydesc[i].gpio = of_get_named_gpio(imx6uirq.nd ,"key-gpio", i);
		if (imx6uirq.irqkeydesc[i].gpio < 0) {
			printk("can't get key%d\r\n", i);
		}
	}
	
	/* 初始化key所使用的IO，并且设置成中断模式 */
	for (i = 0; i < KEY_NUM; i++) {
		memset(imx6uirq.irqkeydesc[i].name, 0, sizeof(name));	/* 缓冲区清零 */
		sprintf(imx6uirq.irqkeydesc[i].name, "KEY%d", i);		/* 组合名字 */
		gpio_request(imx6uirq.irqkeydesc[i].gpio, name);
		gpio_direction_input(imx6uirq.irqkeydesc[i].gpio);	
		imx6uirq.irqkeydesc[i].irqnum = irq_of_parse_and_map(imx6uirq.nd, i);
	}

	/* 申请中断 */
	imx6uirq.irqkeydesc[0].handler = key0_handler;
	imx6uirq.irqkeydesc[0].value = KEY0VALUE;
	
	for (i = 0; i < KEY_NUM; i++) {
		ret = request_irq(imx6uirq.irqkeydesc[i].irqnum, imx6uirq.irqkeydesc[i].handler, 
		                 IRQF_TRIGGER_FALLING|IRQF_TRIGGER_RISING, imx6uirq.irqkeydesc[i].name, &imx6uirq);
		if(ret < 0){
			printk("irq %d request failed!\r\n", imx6uirq.irqkeydesc[i].irqnum);
			return -EFAULT;
		}
	}

	/* 创建定时器 */
	timer_setup(&imx6uirq.timer, timer_function, 0);

	/* 初始化等待队列头 */
	init_waitqueue_head(&imx6uirq.r_wait);
	return 0;
}

/*
 * @description		: 打开设备
 * @param - inode 	: 传递给驱动的inode
 * @param - filp 	: 设备文件，file结构体有个叫做private_data的成员变量
 * 					  一般在open的时候将private_data指向设备结构体。
 * @return 			: 0 成功;其他 失败
 */
static int imx6uirq_open(struct inode *inode, struct file *filp)
{
	filp->private_data = &imx6uirq;	/* 设置私有数据 */
	return 0;
}

 /*
  * @description     : 从设备读取数据 
  * @param - filp    : 要打开的设备文件(文件描述符)
  * @param - buf     : 返回给用户空间的数据缓冲区
  * @param - cnt     : 要读取的数据长度
  * @param - offt    : 相对于文件首地址的偏移
  * @return          : 读取的字节数，如果为负值，表示读取失败
  */
static ssize_t imx6uirq_read(struct file *filp, char __user *buf, size_t cnt, loff_t *offt)
{
	int ret = 0;
	unsigned char keyvalue = 0;
	unsigned char releasekey = 0;
	struct imx6uirq_dev *dev = (struct imx6uirq_dev *)filp->private_data;

	if (filp->f_flags & O_NONBLOCK)	{ /* 非阻塞访问 */
		if(atomic_read(&dev->releasekey) == 0)	/* 没有按键按下，返回-EAGAIN */
			return -EAGAIN;
	} else {							/* 阻塞访问 */
		/* 加入等待队列，等待被唤醒,也就是有按键按下 */
 		ret = wait_event_interruptible(dev->r_wait, atomic_read(&dev->releasekey)); 
		if (ret) {
			goto wait_error;
		}
	}

	keyvalue = atomic_read(&dev->keyvalue);
	releasekey = atomic_read(&dev->releasekey);

	if (releasekey) { /* 有按键按下 */	
		if (keyvalue & 0x80) {
			keyvalue &= ~0x80;
			ret = copy_to_user(buf, &keyvalue, sizeof(keyvalue));
		} else {
			goto data_error;
		}
		atomic_set(&dev->releasekey, 0);/* 按下标志清零 */
	} else {
		goto data_error;
	}
	return 0;

wait_error:
	return ret;
data_error:
	return -EINVAL;
}

 /*
  * @description     : poll函数，用于处理非阻塞访问
  * @param - filp    : 要打开的设备文件(文件描述符)
  * @param - wait    : 等待列表(poll_table)
  * @return          : 设备或者资源状态，
  */
unsigned int imx6uirq_poll(struct file *filp, struct poll_table_struct *wait)
{
	unsigned int mask = 0;
	struct imx6uirq_dev *dev = (struct imx6uirq_dev *)filp->private_data;

	poll_wait(filp, &dev->r_wait, wait);	/* 将等待队列头添加到poll_table中 */
	
	if(atomic_read(&dev->releasekey)) {		/* 按键按下 */
		mask = POLLIN | POLLRDNORM;			/* 返回PLLIN */
	}
	return mask;
}

/* 设备操作函数 */
static struct file_operations imx6uirq_fops = {
	.owner = THIS_MODULE,
	.open = imx6uirq_open,
	.read = imx6uirq_read,
	.poll = imx6uirq_poll,
};

/*
 * @description	: 驱动入口函数
 * @param 		: 无
 * @return 		: 无
 */
static int __init imx6uirq_init(void)
{
	/* 1、构建设备号 */
	if (imx6uirq.major) {
		imx6uirq.devid = MKDEV(imx6uirq.major, 0);
		register_chrdev_region(imx6uirq.devid, IMX6UIRQ_CNT, IMX6UIRQ_NAME);
	} else {
		alloc_chrdev_region(&imx6uirq.devid, 0, IMX6UIRQ_CNT, IMX6UIRQ_NAME);
		imx6uirq.major = MAJOR(imx6uirq.devid);
		imx6uirq.minor = MINOR(imx6uirq.devid);
	}

	/* 2、注册字符设备 */
	cdev_init(&imx6uirq.cdev, &imx6uirq_fops);
	cdev_add(&imx6uirq.cdev, imx6uirq.devid, IMX6UIRQ_CNT);

	/* 3、创建类 */
	imx6uirq.class = class_create(IMX6UIRQ_NAME);
	if (IS_ERR(imx6uirq.class)) {	
		return PTR_ERR(imx6uirq.class);
	}

	/* 4、创建设备 */
	imx6uirq.device = device_create(imx6uirq.class, NULL, imx6uirq.devid, NULL, IMX6UIRQ_NAME);
	if (IS_ERR(imx6uirq.device)) {
		return PTR_ERR(imx6uirq.device);
	}
		
	/* 5、初始化按键 */
	atomic_set(&imx6uirq.keyvalue, INVAKEY);
	atomic_set(&imx6uirq.releasekey, 0);
	keyio_init();
	return 0;
}

/*
 * @description	: 驱动出口函数
 * @param 		: 无
 * @return 		: 无
 */
static void __exit imx6uirq_exit(void)
{
	unsigned i = 0;
	/* 删除定时器 */
	del_timer_sync(&imx6uirq.timer);	/* 删除定时器 */
		
	/* 释放中断 */	
	for (i = 0; i < KEY_NUM; i++) {
		free_irq(imx6uirq.irqkeydesc[i].irqnum, &imx6uirq);
		gpio_free(imx6uirq.irqkeydesc[i].gpio);
	}
	cdev_del(&imx6uirq.cdev);
	unregister_chrdev_region(imx6uirq.devid, IMX6UIRQ_CNT);
	device_destroy(imx6uirq.class, imx6uirq.devid);
	class_destroy(imx6uirq.class);
}	
	
module_init(imx6uirq_init);
module_exit(imx6uirq_exit);
MODULE_LICENSE("GPL");
	
	
```

阻塞IO：阻塞在read/write调用
非阻塞IO：read/write立即返回，可能返回EAGAIN
poll/select：阻塞在poll/select调用，而不是read/write调用
现代高性能程序通常使用poll/epoll + 非阻塞IO的组合。

## 异步通知
使用信号实现

```ko
#include <linux/types.h>
#include <linux/kernel.h>
#include <linux/delay.h>
#include <linux/ide.h>
#include <linux/init.h>
#include <linux/module.h>
#include <linux/errno.h>
#include <linux/gpio.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/of.h>
#include <linux/of_address.h>
#include <linux/of_gpio.h>
#include <linux/semaphore.h>
#include <linux/timer.h>
#include <linux/of_irq.h>
#include <linux/irq.h>
#include <linux/fcntl.h>
#include <linux/wait.h>
#include <linux/poll.h>
#include <asm/mach/map.h>
#include <asm/uaccess.h>
#include <asm/io.h>
/***************************************************************
Copyright © ALIENTEK Co., Ltd. 1998-2029. All rights reserved.
文件名		: asyncnoti.c
作者	  	: 左忠凯
版本	   	: V1.0
描述	   	: 非阻塞IO访问
其他	   	: 无
论坛 	   	: www.openedv.com
日志	   	: 初版V1.0 2019/8/13 左忠凯创建
***************************************************************/
#define IMX6UIRQ_CNT		1			/* 设备号个数 	*/
#define IMX6UIRQ_NAME		"asyncnoti"	/* 名字 		*/
#define KEY0VALUE			0X01		/* KEY0按键值 	*/
#define INVAKEY				0XFF		/* 无效的按键值 */
#define KEY_NUM				1			/* 按键数量 	*/

/* 中断IO描述结构体 */
struct irq_keydesc {
	int gpio;								/* gpio */
	int irqnum;								/* 中断号     */
	unsigned char value;					/* 按键对应的键值 */
	char name[10];							/* 名字 */
	irqreturn_t (*handler)(int, void *);	/* 中断服务函数 */
};

/* imx6uirq设备结构体 */
struct imx6uirq_dev{
	dev_t devid;			/* 设备号 	 */	
	struct cdev cdev;		/* cdev 	*/                 
	struct class *class;	/* 类 		*/
	struct device *device;	/* 设备 	 */
	int major;				/* 主设备号	  */
	int minor;				/* 次设备号   */
	struct device_node	*nd; /* 设备节点 */	
	atomic_t keyvalue;		/* 有效的按键键值 */
	atomic_t releasekey;	/* 标记是否完成一次完成的按键，包括按下和释放 */
	struct timer_list timer;/* 定义一个定时器*/
	struct irq_keydesc irqkeydesc[KEY_NUM];	/* 按键init述数组 */
	unsigned char curkeynum;				/* 当前init按键号 */
	wait_queue_head_t r_wait;				/* 读等待队列头 */
	struct fasync_struct *async_queue;		/* 异步相关结构体 */
};

struct imx6uirq_dev imx6uirq;	/* irq设备 */

/* @description		: 中断服务函数，开启定时器		
 *				  	  定时器用于按键消抖。
 * @param - irq 	: 中断号 
 * @param - dev_id	: 设备结构。
 * @return 			: 中断执行结果
 */
static irqreturn_t key0_handler(int irq, void *dev_id)
{
	struct imx6uirq_dev *dev = (struct imx6uirq_dev*)dev_id;

	dev->curkeynum = 0;
	dev->timer.data = (volatile long)dev_id;
	mod_timer(&dev->timer, jiffies + msecs_to_jiffies(10));	/* 10ms定时 */
	return IRQ_RETVAL(IRQ_HANDLED);
}

/* @description	: 定时器服务函数，用于按键消抖，定时器到了以后
 *				  再次读取按键值，如果按键还是处于按下状态就表示按键有效。
 * @param - arg	: 设备结构变量
 * @return 		: 无
 */
void timer_function(unsigned long arg)
{
	unsigned char value;
	unsigned char num;
	struct irq_keydesc *keydesc;
	struct imx6uirq_dev *dev = (struct imx6uirq_dev *)arg;

	num = dev->curkeynum;
	keydesc = &dev->irqkeydesc[num];

	value = gpio_get_value(keydesc->gpio); 	/* 读取IO值 */
	if(value == 0){ 						/* 按下按键 */
		atomic_set(&dev->keyvalue, keydesc->value);
	}
	else{ 									/* 按键松开 */
		atomic_set(&dev->keyvalue, 0x80 | keydesc->value);
		atomic_set(&dev->releasekey, 1);	/* 标记松开按键，即完成一次完整的按键过程 */
	}               

	if(atomic_read(&dev->releasekey)) {		/* 一次完整的按键过程 */
		if(dev->async_queue)
			kill_fasync(&dev->async_queue, SIGIO, POLL_IN);	/* 释放SIGIO信号 */
	}

#if 0
	/* 唤醒进程 */
	if(atomic_read(&dev->releasekey)) {	/* 完成一次按键过程 */
		/* wake_up(&dev->r_wait); */
		wake_up_interruptible(&dev->r_wait);
	}
#endif
}

/*
 * @description	: 按键IO初始化
 * @param 		: 无
 * @return 		: 无
 */
static int keyio_init(void)
{
	unsigned char i = 0;
	char name[10];
	int ret = 0;
	
	imx6uirq.nd = of_find_node_by_path("/key");
	if (imx6uirq.nd== NULL){
		printk("key node not find!\r\n");
		return -EINVAL;
	} 

	/* 提取GPIO */
	for (i = 0; i < KEY_NUM; i++) {
		imx6uirq.irqkeydesc[i].gpio = of_get_named_gpio(imx6uirq.nd ,"key-gpio", i);
		if (imx6uirq.irqkeydesc[i].gpio < 0) {
			printk("can't get key%d\r\n", i);
		}
	}
	
	/* 初始化key所使用的IO，并且设置成中断模式 */
	for (i = 0; i < KEY_NUM; i++) {
		memset(imx6uirq.irqkeydesc[i].name, 0, sizeof(name));	/* 缓冲区清零 */
		sprintf(imx6uirq.irqkeydesc[i].name, "KEY%d", i);		/* 组合名字 */
		gpio_request(imx6uirq.irqkeydesc[i].gpio, name);
		gpio_direction_input(imx6uirq.irqkeydesc[i].gpio);	
		imx6uirq.irqkeydesc[i].irqnum = irq_of_parse_and_map(imx6uirq.nd, i);
#if 0
		imx6uirq.irqkeydesc[i].irqnum = gpio_to_irq(imx6uirq.irqkeydesc[i].gpio);
#endif
	}

	/* 申请中断 */
	imx6uirq.irqkeydesc[0].handler = key0_handler;
	imx6uirq.irqkeydesc[0].value = KEY0VALUE;
	
	for (i = 0; i < KEY_NUM; i++) {
		ret = request_irq(imx6uirq.irqkeydesc[i].irqnum, imx6uirq.irqkeydesc[i].handler, 
		                 IRQF_TRIGGER_FALLING|IRQF_TRIGGER_RISING, imx6uirq.irqkeydesc[i].name, &imx6uirq);
		if(ret < 0){
			printk("irq %d request failed!\r\n", imx6uirq.irqkeydesc[i].irqnum);
			return -EFAULT;
		}
	}

	/* 创建定时器 */
    init_timer(&imx6uirq.timer);
    imx6uirq.timer.function = timer_function;

	/* 初始化等待队列头 */
	init_waitqueue_head(&imx6uirq.r_wait);
	return 0;
}

/*
 * @description		: 打开设备
 * @param - inode 	: 传递给驱动的inode
 * @param - filp 	: 设备文件，file结构体有个叫做private_data的成员变量
 * 					  一般在open的时候将private_data指向设备结构体。
 * @return 			: 0 成功;其他 失败
 */
static int imx6uirq_open(struct inode *inode, struct file *filp)
{
	filp->private_data = &imx6uirq;	/* 设置私有数据 */
	return 0;
}

 /*
  * @description     : 从设备读取数据 
  * @param - filp    : 要打开的设备文件(文件描述符)
  * @param - buf     : 返回给用户空间的数据缓冲区
  * @param - cnt     : 要读取的数据长度
  * @param - offt    : 相对于文件首地址的偏移
  * @return          : 读取的字节数，如果为负值，表示读取失败
  */
static ssize_t imx6uirq_read(struct file *filp, char __user *buf, size_t cnt, loff_t *offt)
{
	int ret = 0;
	unsigned char keyvalue = 0;
	unsigned char releasekey = 0;
	struct imx6uirq_dev *dev = (struct imx6uirq_dev *)filp->private_data;

	if (filp->f_flags & O_NONBLOCK)	{ /* 非阻塞访问 */
		if(atomic_read(&dev->releasekey) == 0)	/* 没有按键按下，返回-EAGAIN */
			return -EAGAIN;
	} else {							/* 阻塞访问 */
		/* 加入等待队列，等待被唤醒,也就是有按键按下 */
 		ret = wait_event_interruptible(dev->r_wait, atomic_read(&dev->releasekey)); 
		if (ret) {
			goto wait_error;
		}
	}

	keyvalue = atomic_read(&dev->keyvalue);
	releasekey = atomic_read(&dev->releasekey);

	if (releasekey) { /* 有按键按下 */	
		if (keyvalue & 0x80) {
			keyvalue &= ~0x80;
			ret = copy_to_user(buf, &keyvalue, sizeof(keyvalue));
		} else {
			goto data_error;
		}
		atomic_set(&dev->releasekey, 0);/* 按下标志清零 */
	} else {
		goto data_error;
	}
	return 0;

wait_error:
	return ret;
data_error:
	return -EINVAL;
}

 /*
  * @description     : poll函数，用于处理非阻塞访问
  * @param - filp    : 要打开的设备文件(文件描述符)
  * @param - wait    : 等待列表(poll_table)
  * @return          : 设备或者资源状态，
  */
unsigned int imx6uirq_poll(struct file *filp, struct poll_table_struct *wait)
{
	unsigned int mask = 0;
	struct imx6uirq_dev *dev = (struct imx6uirq_dev *)filp->private_data;

	poll_wait(filp, &dev->r_wait, wait);	/* 将等待队列头添加到poll_table中 */
	
	if(atomic_read(&dev->releasekey)) {		/* 按键按下 */
		mask = POLLIN | POLLRDNORM;			/* 返回PLLIN */
	}
	return mask;
}

/*
 * @description     : fasync函数，用于处理异步通知
 * @param - fd		: 文件描述符
 * @param - filp    : 要打开的设备文件(文件描述符)
 * @param - on      : 模式
 * @return          : 负数表示函数执行失败
 */
static int imx6uirq_fasync(int fd, struct file *filp, int on)
{
	struct imx6uirq_dev *dev = (struct imx6uirq_dev *)filp->private_data;
	return fasync_helper(fd, filp, on, &dev->async_queue);
}

/*
 * @description     : release函数，应用程序调用close关闭驱动文件的时候会执行
 * @param - inode	: inode节点
 * @param - filp    : 要打开的设备文件(文件描述符)
 * @return          : 负数表示函数执行失败
 */
static int imx6uirq_release(struct inode *inode, struct file *filp)
{
	return imx6uirq_fasync(-1, filp, 0);
}

/* 设备操作函数 */
static struct file_operations imx6uirq_fops = {
	.owner = THIS_MODULE,
	.open = imx6uirq_open,
	.read = imx6uirq_read,
	.poll = imx6uirq_poll,
	.fasync = imx6uirq_fasync,
	.release = imx6uirq_release,
};

/*
 * @description	: 驱动入口函数
 * @param 		: 无
 * @return 		: 无
 */
static int __init imx6uirq_init(void)
{
	/* 1、构建设备号 */
	if (imx6uirq.major) {
		imx6uirq.devid = MKDEV(imx6uirq.major, 0);
		register_chrdev_region(imx6uirq.devid, IMX6UIRQ_CNT, IMX6UIRQ_NAME);
	} else {
		alloc_chrdev_region(&imx6uirq.devid, 0, IMX6UIRQ_CNT, IMX6UIRQ_NAME);
		imx6uirq.major = MAJOR(imx6uirq.devid);
		imx6uirq.minor = MINOR(imx6uirq.devid);
	}

	/* 2、注册字符设备 */
	cdev_init(&imx6uirq.cdev, &imx6uirq_fops);
	cdev_add(&imx6uirq.cdev, imx6uirq.devid, IMX6UIRQ_CNT);

	/* 3、创建类 */
	imx6uirq.class = class_create(THIS_MODULE, IMX6UIRQ_NAME);
	if (IS_ERR(imx6uirq.class)) {	
		return PTR_ERR(imx6uirq.class);
	}

	/* 4、创建设备 */
	imx6uirq.device = device_create(imx6uirq.class, NULL, imx6uirq.devid, NULL, IMX6UIRQ_NAME);
	if (IS_ERR(imx6uirq.device)) {
		return PTR_ERR(imx6uirq.device);
	}
		
	/* 5、始化按键 */
	atomic_set(&imx6uirq.keyvalue, INVAKEY);
	atomic_set(&imx6uirq.releasekey, 0);
	keyio_init();
	return 0;
}

/*
 * @description	: 驱动出口函数
 * @param 		: 无
 * @return 		: 无
 */
static void __exit imx6uirq_exit(void)
{
	unsigned i = 0;
	/* 删除定时器 */
	del_timer_sync(&imx6uirq.timer);	/* 删除定时器 */
		
	/* 释放中断 */	
	for (i = 0; i < KEY_NUM; i++) {
		free_irq(imx6uirq.irqkeydesc[i].irqnum, &imx6uirq);
		gpio_free(imx6uirq.irqkeydesc[i].gpio);
	}
	cdev_del(&imx6uirq.cdev);
	unregister_chrdev_region(imx6uirq.devid, IMX6UIRQ_CNT);
	device_destroy(imx6uirq.class, imx6uirq.devid);
	class_destroy(imx6uirq.class);
}	
	
module_init(imx6uirq_init);
module_exit(imx6uirq_exit);
MODULE_LICENSE("GPL");
	
	
```

```app
#include "stdio.h"
#include "unistd.h"
#include "sys/types.h"
#include "sys/stat.h"
#include "fcntl.h"
#include "stdlib.h"
#include "string.h"
#include "poll.h"
#include "sys/select.h"
#include "sys/time.h"
#include "linux/ioctl.h"
#include "signal.h"
/***************************************************************
Copyright © ALIENTEK Co., Ltd. 1998-2029. All rights reserved.
文件名		: asyncnotiApp.c
作者	  	: 左忠凯
版本	   	: V1.0
描述	   	: 异步通知测试APP
其他	   	: 无
使用方法	：./asyncnotiApp /dev/asyncnoti 打开测试App
论坛 	   	: www.openedv.com
日志	   	: 初版V1.0 2019/8/13 左忠凯创建
***************************************************************/

static int fd = 0;	/* 文件描述符 */

/*
 * SIGIO信号处理函数
 * @param - signum 	: 信号值
 * @return 			: 无
 */
static void sigio_signal_func(int signum)
{
	int err = 0;
	unsigned int keyvalue = 0;

	err = read(fd, &keyvalue, sizeof(keyvalue));
	if(err < 0) {
		/* 读取错误 */
	} else {
		printf("sigio signal! key value=%d\r\n", keyvalue);
	}
}

/*
 * @description		: main主程序
 * @param - argc 	: argv数组元素个数
 * @param - argv 	: 具体参数
 * @return 			: 0 成功;其他 失败
 */
int main(int argc, char *argv[])
{
	int flags = 0;
	char *filename;

	if (argc != 2) {
		printf("Error Usage!\r\n");
		return -1;
	}

	filename = argv[1];
	fd = open(filename, O_RDWR);
	if (fd < 0) {
		printf("Can't open file %s\r\n", filename);
		return -1;
	}

	/* 设置信号SIGIO的处理函数 */
	signal(SIGIO, sigio_signal_func);
	
	fcntl(fd, F_SETOWN, getpid());		/* 设置当前进程接收SIGIO信号 	*/
	flags = fcntl(fd, F_GETFL);			/* 获取当前的进程状态 			*/
	fcntl(fd, F_SETFL, flags | FASYNC);	/* 设置进程启用异步通知功能 	*/	

	while(1) {
		sleep(2);
	}

	close(fd);
	return 0;
}

```

## 信号和epoll的配合
### 传统信号处理的问题
传统的信号处理方式存在以下问题：
- 信号会中断epoll_wait，导致返回-1，errno=EINTR
- 信号处理函数中只能调用异步信号安全函数，限制很大
- 异步执行难以调试，容易出现竞态条件

```c
// 传统方式的问题
int epoll_fd = epoll_create1(0);

while (1) {
    int nfds = epoll_wait(epoll_fd, events, MAX_EVENTS, -1);
    if (nfds == -1) {
        if (errno == EINTR) {
            // 被信号中断，需要重新调用
            continue;
        }
        perror("epoll_wait");
        break;
    }
    // 处理事件...
}
```

### 现代解决方案：signalfd + eventfd

#### 1. signalfd方案
将信号转换为文件描述符事件，统一通过epoll处理：

```c
#include <sys/signalfd.h>

// 阻塞信号，不让它们异步处理
sigset_t mask;
sigemptyset(&mask);
sigaddset(&mask, SIGINT);
sigaddset(&mask, SIGTERM);
sigprocmask(SIG_BLOCK, &mask, NULL);

// 创建signalfd，将信号转换为文件描述符事件
int signal_fd = signalfd(-1, &mask, SFD_CLOEXEC);

// 将signalfd加入epoll
struct epoll_event ev;
ev.events = EPOLLIN;
ev.data.fd = signal_fd;
epoll_ctl(epoll_fd, EPOLL_CTL_ADD, signal_fd, &ev);

// 在事件循环中处理
while (1) {
    int nfds = epoll_wait(epoll_fd, events, MAX_EVENTS, -1);
    
    for (int i = 0; i < nfds; i++) {
        if (events[i].data.fd == signal_fd) {
            struct signalfd_siginfo si;
            read(signal_fd, &si, sizeof(si));
            printf("收到信号: %d\n", si.ssi_signo);
            // 在这里可以安全地处理信号
        }
    }
}
```

#### 2. Self-Pipe Trick + EventFD
network网络库使用的经典方案：

**EventFD - 线程间通信的文件描述符**
```c
// 创建eventfd
int eventfd = eventfd(0, EFD_CLOEXEC | EFD_NONBLOCK);

// 线程A：发送通知
uint64_t value = 1;
write(eventfd, &value, sizeof(value));  // 写入数字，计数器+1

// 线程B：接收通知  
uint64_t result;
read(eventfd, &result, sizeof(result));  // 读取会清零计数器
```

**Self-Pipe Trick**
```c
int pipe_fd[2];  // 创建管道

void signal_handler(int sig) {
    char byte = sig;
    write(pipe_fd[1], &byte, 1);  // 只写一个字节到管道
}

int main() {
    pipe(pipe_fd);
    signal(SIGINT, signal_handler);
    
    // 将管道读端加入epoll
    epoll_ctl(epollfd, EPOLL_CTL_ADD, pipe_fd[0], &ev);
    
    while (1) {
        int nfds = epoll_wait(epollfd, events, MAX_EVENTS, -1);
        
        for (int i = 0; i < nfds; i++) {
            if (events[i].data.fd == pipe_fd[0]) {
                char sig;
                read(pipe_fd[0], &sig, 1);
                printf("在主循环中安全处理信号 %d\n", sig);
            }
        }
    }
}
```

### network风格的统一事件处理

```cpp
class EventLoop {
private:
    int epollfd_;
    int wakeupFd_;    // eventfd，用于线程间通信
    bool quit_;
    std::vector<std::function<void()>> pendingFunctors_;
    
public:
    void loop() {
        while (!quit_) {
            // 所有事件都通过epoll_wait获取
            int nfds = epoll_wait(epollfd_, events, MAX_EVENTS, timeout);
            
            for (int i = 0; i < nfds; i++) {
                int fd = events[i].data.fd;
                
                if (fd == wakeupFd_) {
                    handleWakeup();        // 处理线程间通信
                } else if (fd == listenfd_) {
                    handleNewConnection(); // 处理新连接
                } else {
                    handleIO(fd);          // 处理IO事件
                }
            }
            
            doPendingFunctors();  // 执行待处理任务
        }
    }
    
    // 从其他线程安全地退出循环
    void quit() {
        quit_ = true;
        wakeup();  // 通过eventfd唤醒epoll_wait
    }
    
    void wakeup() {
        uint64_t one = 1;
        write(wakeupFd_, &one, sizeof(one));
    }
    
    void runInLoop(std::function<void()> cb) {
        if (isInLoopThread()) {
            cb();  // 在EventLoop线程，直接执行
        } else {
            queueInLoop(cb);  // 在其他线程，加入队列并唤醒
        }
    }
};

// 信号处理：转换为eventfd事件
void signalHandler(int sig) {
    // 通知EventLoop处理信号
    g_eventLoop->runInLoop([sig]() {
        handleSignalSafely(sig);  // 在EventLoop线程中安全处理
    });
}
```

### 嵌入式Linux中的应用示例

```c
// IMX6ULL中的事件循环实现
struct event_loop {
    int epoll_fd;
    int eventfd;
    int quit;
    
    // 设备文件描述符
    int gpio_fd;
    int uart_fd;
};

struct event_loop g_loop;

// 信号处理函数
void signal_handler(int sig) {
    printf("Received signal %d\n", sig);
    
    // 通过eventfd通知主循环
    uint64_t value = sig;
    write(g_loop.eventfd, &value, sizeof(value));
}

void handle_eventfd(struct event_loop *loop) {
    uint64_t value;
    ssize_t n = read(loop->eventfd, &value, sizeof(value));
    
    if (n == sizeof(value)) {
        printf("Signal %lu received in main loop\n", value);
        
        switch (value) {
            case SIGINT:
            case SIGTERM:
                printf("Shutting down gracefully...\n");
                loop->quit = 1;
                break;
            case SIGUSR1:
                printf("Reloading configuration...\n");
                // 重新加载配置
                break;
        }
    }
}

int main() {
    struct epoll_event ev, events[10];
    
    // 初始化事件循环
    g_loop.epoll_fd = epoll_create1(EPOLL_CLOEXEC);
    g_loop.eventfd = eventfd(0, EFD_CLOEXEC | EFD_NONBLOCK);
    g_loop.quit = 0;
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGUSR1, signal_handler);
    signal(SIGPIPE, SIG_IGN);
    
    // 将eventfd加入epoll
    ev.events = EPOLLIN;
    ev.data.fd = g_loop.eventfd;
    epoll_ctl(g_loop.epoll_fd, EPOLL_CTL_ADD, g_loop.eventfd, &ev);
    
    // 添加其他设备文件
    g_loop.gpio_fd = open("/dev/gpio_key", O_RDONLY | O_NONBLOCK);
    if (g_loop.gpio_fd >= 0) {
        ev.events = EPOLLIN;
        ev.data.fd = g_loop.gpio_fd;
        epoll_ctl(g_loop.epoll_fd, EPOLL_CTL_ADD, g_loop.gpio_fd, &ev);
    }
    
    printf("Event loop started, pid=%d\n", getpid());
    
    // 主事件循环
    while (!g_loop.quit) {
        int nfds = epoll_wait(g_loop.epoll_fd, events, 10, 1000);  // 1秒超时
        
        if (nfds > 0) {
            for (int i = 0; i < nfds; i++) {
                int fd = events[i].data.fd;
                
                if (fd == g_loop.eventfd) {
                    handle_eventfd(&g_loop);
                } else if (fd == g_loop.gpio_fd) {
                    handle_gpio_event(fd);
                }
            }
        } else if (nfds == 0) {
            // 超时，执行定期任务
            printf("Heartbeat...\n");
        }
    }
    
    // 清理资源
    close(g_loop.gpio_fd);
    close(g_loop.eventfd);
    close(g_loop.epoll_fd);
    
    printf("Event loop stopped\n");
    return 0;
}
```

### 核心优势

**network设计的核心优势：**

1. **统一事件模型**：信号、IO事件都通过epoll处理
2. **线程安全**：信号通过eventfd转换为线程安全的事件  
3. **非阻塞**：整个系统都是非阻塞的
4. **高性能**：epoll + 非阻塞IO的组合
5. **易于调试**：所有处理都在主事件循环中，逻辑清晰

**关键技术点：**
- **EventFD**：将线程间通信转换为文件描述符事件
- **Self-Pipe Trick**：信号处理的经典技巧
- **One Loop Per Thread**：每个线程一个事件循环
- **统一的事件派发**：所有事件都通过相同的机制处理

**架构总结：**
```
信号 ────→ EventFD ────→ epoll ────→ 统一事件处理
网络IO ──────────────→ epoll ────→ 统一事件处理  
定时器 ──────────────→ epoll ────→ 统一事件处理
```

这种设计将复杂的异步世界统一为简单的同步事件循环，让异步编程变得简单而优雅！

## platform driver
使用系统自带驱动的话
compatible = "gpio-leds";
gpios = <&gpio1 3 GPIO_ACTIVE_LOW>;
要对应
/sys/bus/platform/devices/dtsleds 驱动会显示在这下面 

miscdriver
可以帮助自动创建设备号
/sys/class/misc

### /sys 文件系统结构

```
/sys/
├── bus/           # 总线类型（i2c, spi, platform, usb等）
├── class/         # 设备类型（gpio, misc, input, tty等）
├── devices/       # 所有设备的实体
├── firmware/      # 固件相关信息
├── fs/            # 文件系统信息
├── kernel/        # 内核参数和调试信息
├── module/        # 内核模块信息
└── power/         # 电源管理
```

### input子系统
初始化 input_dev的事件类型以及事件值
上报输入事件
inputdev = input_allocate_device();
inputdev->name = KEYINPUT_NAME;  // 设置设备名称
inputdev->evbit[0] = BIT_MASK(EV_KEY) | BIT_MASK(EV_REP);  // 设置事件类型
input_set_capability(inputdev, EV_KEY, KEY_0);  // 设置按键能力
input_register_device(inputdev);
input_report_key(dev->inputdev, keydesc->value, 1);  // 报告按键按下
input_sync(dev->inputdev);  // 同步事件

input_report_key(dev->inputdev, keydesc->value, 0);  // 报告按键释放
input_sync(dev->inputdev);  // 同步事件
input_unregister_device(inputdev);  // 注销设备
input_free_device(inputdev);        // 释放设备


### lcd

```
LCD控制器 (/sys/class/graphics/fb0/)
主要控制显示参数：
bits_per_pixel: 控制色深
blank: 控制屏幕开关 (0=开启，1=关闭)
mode: 显示模式（分辨率、刷新率等）
rotate: 屏幕旋转（0、90、180、270度）
virtual_size: 虚拟屏幕大小
state: 显示状态
cursor: 光标控制
pan: 屏幕平移

使用示例：
# 查看当前色深
cat /sys/class/graphics/fb0/bits_per_pixel
# 关闭显示
echo 1 > /sys/class/graphics/fb0/blank
# 开启显示
echo 0 > /sys/class/graphics/fb0/blank


背光控制 (/sys/class/backlight/backlight-display/)
控制屏幕背光：
actual_brightness: 当前实际亮度（只读）
brightness: 设置亮度值
max_brightness: 最大亮度值（只读）
bl_power: 背光电源控制
type: 背光类型
scale: 亮度调节范围


使用示例：
# 查看最大亮度
cat /sys/class/backlight/backlight-display/max_brightness
# 设置当前亮度
echo 7 > /sys/class/backlight/backlight-display/brightness
# 查看当前亮度
cat /sys/class/backlight/backlight-display/actual_brightness
```

#### lcdif和panel

硬件分层更清晰：
LCDIF节点：专注于控制器硬件（寄存器、时钟、中断等）
Panel节点：专注于面板特性（时序、分辨率、接口等

### rtc
```
snvs_rtc: snvs-rtc-lp {
					compatible = "fsl,sec-v4.0-mon-rtc-lp";
					regmap = <&snvs>;
					offset = <0x34>;
					interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>,
						     <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
				};
```

### i2c

```
#include <linux/types.h>
#include <linux/kernel.h>
#include <linux/delay.h>
#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/init.h>
#include <linux/module.h>
#include <linux/errno.h>
#include <linux/gpio.h>
#include <linux/cdev.h>
#include <linux/device.h>
#include <linux/of_gpio.h>
#include <linux/semaphore.h>
#include <linux/timer.h>
#include <linux/i2c.h>
#include <asm/mach/map.h>
#include <asm/uaccess.h>
#include <asm/io.h>
#include "ap3216creg.h"
/***************************************************************
Copyright © ALIENTEK Co., Ltd. 1998-2029. All rights reserved.
文件名		: ap3216c.c
作者	  	: 左忠凯
版本	   	: V1.0
描述	   	: AP3216C驱动程序
其他	   	: 无
论坛 	   	: www.openedv.com
日志	   	: 初版V1.0 2019/9/2 左忠凯创建
***************************************************************/
#define AP3216C_CNT	1
#define AP3216C_NAME	"ap3216c"

struct ap3216c_dev {
	dev_t devid;			/* 设备号 	 */
	struct cdev cdev;		/* cdev 	*/
	struct class *class;	/* 类 		*/
	struct device *device;	/* 设备 	 */
	struct device_node	*nd; /* 设备节点 */
	int major;			/* 主设备号 */
	void *private_data;	/* 私有数据 */
	unsigned short ir, als, ps;		/* 三个光传感器数据 */
};

static struct ap3216c_dev ap3216cdev;

/*
 * @description	: 从ap3216c读取多个寄存器数据
 * @param - dev:  ap3216c设备
 * @param - reg:  要读取的寄存器首地址
 * @param - val:  读取到的数据
 * @param - len:  要读取的数据长度
 * @return 		: 操作结果
 */
static int ap3216c_read_regs(struct ap3216c_dev *dev, u8 reg, void *val, int len)
{
	int ret;
	struct i2c_msg msg[2];
	struct i2c_client *client = (struct i2c_client *)dev->private_data;

	/* msg[0]为发送要读取的首地址 */
	msg[0].addr = client->addr;			/* ap3216c地址 */
	msg[0].flags = 0;					/* 标记为发送数据 */
	msg[0].buf = &reg;					/* 读取的首地址 */
	msg[0].len = 1;						/* reg长度*/

	/* msg[1]读取数据 */
	msg[1].addr = client->addr;			/* ap3216c地址 */
	msg[1].flags = I2C_M_RD;			/* 标记为读取数据*/
	msg[1].buf = val;					/* 读取数据缓冲区 */
	msg[1].len = len;					/* 要读取的数据长度*/

	ret = i2c_transfer(client->adapter, msg, 2);
	if(ret == 2) {
		ret = 0;
	} else {
		printk("i2c rd failed=%d reg=%06x len=%d\n",ret, reg, len);
		ret = -EREMOTEIO;
	}
	return ret;
}

/*
 * @description	: 向ap3216c多个寄存器写入数据
 * @param - dev:  ap3216c设备
 * @param - reg:  要写入的寄存器首地址
 * @param - val:  要写入的数据缓冲区
 * @param - len:  要写入的数据长度
 * @return 	  :   操作结果
 */
static s32 ap3216c_write_regs(struct ap3216c_dev *dev, u8 reg, u8 *buf, u8 len)
{
	u8 b[256];
	struct i2c_msg msg;
	struct i2c_client *client = (struct i2c_client *)dev->private_data;
	
	b[0] = reg;					/* 寄存器首地址 */
	memcpy(&b[1],buf,len);		/* 将要写入的数据拷贝到数组b里面 */
		
	msg.addr = client->addr;	/* ap3216c地址 */
	msg.flags = 0;				/* 标记为写数据 */

	msg.buf = b;				/* 要写入的数据缓冲区 */
	msg.len = len + 1;			/* 要写入的数据长度 */

	return i2c_transfer(client->adapter, &msg, 1);
}

/*
 * @description	: 读取ap3216c指定寄存器值，读取一个寄存器
 * @param - dev:  ap3216c设备
 * @param - reg:  要读取的寄存器
 * @return 	  :   读取到的寄存器值
 */
static unsigned char ap3216c_read_reg(struct ap3216c_dev *dev, u8 reg)
{
	u8 data = 0;

	ap3216c_read_regs(dev, reg, &data, 1);
	return data;

#if 0
	struct i2c_client *client = (struct i2c_client *)dev->private_data;
	return i2c_smbus_read_byte_data(client, reg);
#endif
}

/*
 * @description	: 向ap3216c指定寄存器写入指定的值，写一个寄存器
 * @param - dev:  ap3216c设备
 * @param - reg:  要写的寄存器
 * @param - data: 要写入的值
 * @return   :    无
 */
static void ap3216c_write_reg(struct ap3216c_dev *dev, u8 reg, u8 data)
{
	u8 buf = 0;
	buf = data;
	ap3216c_write_regs(dev, reg, &buf, 1);
}

/*
 * @description	: 读取AP3216C的数据，读取原始数据，包括ALS,PS和IR, 注意！
 *				: 如果同时打开ALS,IR+PS的话两次数据读取的时间间隔要大于112.5ms
 * @param - ir	: ir数据
 * @param - ps 	: ps数据
 * @param - ps 	: als数据 
 * @return 		: 无。
 */
void ap3216c_readdata(struct ap3216c_dev *dev)
{
	unsigned char i =0;
    unsigned char buf[6];
	
	/* 循环读取所有传感器数据 */
    for(i = 0; i < 6; i++)	
    {
        buf[i] = ap3216c_read_reg(dev, AP3216C_IRDATALOW + i);	
    }

    if(buf[0] & 0X80) 	/* IR_OF位为1,则数据无效 */
		dev->ir = 0;					
	else 				/* 读取IR传感器的数据   		*/
		dev->ir = ((unsigned short)buf[1] << 2) | (buf[0] & 0X03); 			
	
	dev->als = ((unsigned short)buf[3] << 8) | buf[2];	/* 读取ALS传感器的数据 			 */  
	
    if(buf[4] & 0x40)	/* IR_OF位为1,则数据无效 			*/
		dev->ps = 0;    													
	else 				/* 读取PS传感器的数据    */
		dev->ps = ((unsigned short)(buf[5] & 0X3F) << 4) | (buf[4] & 0X0F); 
}

/*
 * @description		: 打开设备
 * @param - inode 	: 传递给驱动的inode
 * @param - filp 	: 设备文件，file结构体有个叫做private_data的成员变量
 * 					  一般在open的时候将private_data指向设备结构体。
 * @return 			: 0 成功;其他 失败
 */
static int ap3216c_open(struct inode *inode, struct file *filp)
{
	filp->private_data = &ap3216cdev;

	/* 初始化AP3216C */
	ap3216c_write_reg(&ap3216cdev, AP3216C_SYSTEMCONG, 0x04);		/* 复位AP3216C 			*/
	mdelay(50);														/* AP3216C复位最少10ms 	*/
	ap3216c_write_reg(&ap3216cdev, AP3216C_SYSTEMCONG, 0X03);		/* 开启ALS、PS+IR 		*/
	return 0;
}

/*
 * @description		: 从设备读取数据 
 * @param - filp 	: 要打开的设备文件(文件描述符)
 * @param - buf 	: 返回给用户空间的数据缓冲区
 * @param - cnt 	: 要读取的数据长度
 * @param - offt 	: 相对于文件首地址的偏移
 * @return 			: 读取的字节数，如果为负值，表示读取失败
 */
static ssize_t ap3216c_read(struct file *filp, char __user *buf, size_t cnt, loff_t *off)
{
	short data[3];
	long err = 0;

	struct ap3216c_dev *dev = (struct ap3216c_dev *)filp->private_data;
	
	ap3216c_readdata(dev);

	data[0] = dev->ir;
	data[1] = dev->als;
	data[2] = dev->ps;
	err = copy_to_user(buf, data, sizeof(data));
	return 0;
}

/*
 * @description		: 关闭/释放设备
 * @param - filp 	: 要关闭的设备文件(文件描述符)
 * @return 			: 0 成功;其他 失败
 */
static int ap3216c_release(struct inode *inode, struct file *filp)
{
	return 0;
}

/* AP3216C操作函数 */
static const struct file_operations ap3216c_ops = {
	.owner = THIS_MODULE,
	.open = ap3216c_open,
	.read = ap3216c_read,
	.release = ap3216c_release,
};

/*
 * @description     : i2c驱动的probe函数，当驱动与
 *                    设备匹配以后此函数就会执行
 * @param - client  : i2c设备
 * @return          : 0，成功;其他负值,失败
 */
static int ap3216c_probe(struct i2c_client *client)
{
	/* 1、构建设备号 */
	if (ap3216cdev.major) {
		ap3216cdev.devid = MKDEV(ap3216cdev.major, 0);
		register_chrdev_region(ap3216cdev.devid, AP3216C_CNT, AP3216C_NAME);
	} else {
		alloc_chrdev_region(&ap3216cdev.devid, 0, AP3216C_CNT, AP3216C_NAME);
		ap3216cdev.major = MAJOR(ap3216cdev.devid);
	}

	/* 2、注册设备 */
	cdev_init(&ap3216cdev.cdev, &ap3216c_ops);
	cdev_add(&ap3216cdev.cdev, ap3216cdev.devid, AP3216C_CNT);

	/* 3、创建类 */
	ap3216cdev.class = class_create(AP3216C_NAME);
	if (IS_ERR(ap3216cdev.class)) {
		return PTR_ERR(ap3216cdev.class);
	}

	/* 4、创建设备 */
	ap3216cdev.device = device_create(ap3216cdev.class, NULL, ap3216cdev.devid, NULL, AP3216C_NAME);
	if (IS_ERR(ap3216cdev.device)) {
		return PTR_ERR(ap3216cdev.device);
	}

	ap3216cdev.private_data = client;

	return 0;
}

/*
 * @description     : i2c驱动的remove函数，移除i2c驱动的时候此函数会执行
 * @param - client  : i2c设备
 * @return          : void
 */
static void ap3216c_remove(struct i2c_client *client)
{
	/* 删除设备 */
	cdev_del(&ap3216cdev.cdev);
	unregister_chrdev_region(ap3216cdev.devid, AP3216C_CNT);

	/* 注销掉类和设备 */
	device_destroy(ap3216cdev.class, ap3216cdev.devid);
	class_destroy(ap3216cdev.class);
}

/* 传统匹配方式ID列表 */
static const struct i2c_device_id ap3216c_id[] = {
	{"zexuan,ap3216c", 0},  
	{}
};

/* 设备树匹配列表 */
static const struct of_device_id ap3216c_of_match[] = {
	{ .compatible = "zexuan,ap3216c" },
	{ /* Sentinel */ }
};

/* i2c驱动结构体 */	
static struct i2c_driver ap3216c_driver = {
	.probe = ap3216c_probe,
	.remove = ap3216c_remove,
	.driver = {
			.owner = THIS_MODULE,
		   	.name = "ap3216c",
		   	.of_match_table = ap3216c_of_match, 
		   },
	.id_table = ap3216c_id,
};
		   
/*
 * @description	: 驱动入口函数
 * @param 		: 无
 * @return 		: 无
 */
static int __init ap3216c_init(void)
{
	int ret = 0;

	ret = i2c_add_driver(&ap3216c_driver);
	return ret;
}

/*
 * @description	: 驱动出口函数
 * @param 		: 无
 * @return 		: 无
 */
static void __exit ap3216c_exit(void)
{
	i2c_del_driver(&ap3216c_driver);
}

/* module_i2c_driver(ap3216c_driver) */

module_init(ap3216c_init);
module_exit(ap3216c_exit);
MODULE_LICENSE("GPL");
MODULE_AUTHOR("zexuan");

```
IR (红外线数据)
ALS (环境光数据)
PS (接近传感数据)
### spi


### uart

### 中断线程化
大家应该注意到了“request_threaded_irq”相比“request_irq”多了个 threaded 函数，也就是线
程的意思。那么为什么要中断线程化呢？我们都知道硬件中断具有最高优先级，不论什么时候
只要硬件中断发生，那么内核都会终止当前正在执行的操作，转而去执行中断处理程序(不考虑
关闭中断和中断优先级的情况)，如果中断非常频繁的话那么内核将会频繁的执行中断处理程序，
导致任务得不到及时的处理。中断线程化以后中断将作为内核线程运行，而且也可以被赋予不
同的优先级，任务的优先级可能比中断线程的优先级高，这样做的目的就是保证高优先级的任
务能被优先处理。大家可能会疑问，前面不是说可以将比较耗时的中断放到下半部(bottom half)
处理吗？虽然下半部可以被延迟处理，但是依旧先于线程执行，中断线程化可以让这些比较耗
时的下半部与进程进行公平竞争。

### touch


sequenceDiagram
    participant T as 触摸开始
    participant D as 驱动层
    participant K as 内核Input子系统
    participant E as 事件结束
    T->>D: 手指触摸屏幕
    D->>K: 1. ABS_MT_SLOT(0) - 选择第一个触摸点
    D->>K: 2. ABS_MT_TRACKING_ID(5) - 分配触摸点ID
    D->>K: 3. ABS_MT_POSITION_X(1004) - 上报X坐标
    D->>K: 4. ABS_MT_POSITION_Y(23) - 上报Y坐标
    D->>K: 5. BTN_TOUCH(1) - 报告触摸按下
    D->>K: 6. ABS_X(1008) - 兼容单点触摸X坐标
    D->>K: 7. ABS_Y(41) - 兼容单点触摸Y坐标
    D->>K: 8. EV_SYN - 同步事件
    T->>D: 手指离开屏幕
    D->>K: 9. ABS_MT_TRACKING_ID(-1) - 触摸点结束
    D->>K: 10. BTN_TOUCH(0) - 报告触摸抬起
    D->>K: 11. EV_SYN - 同步事件
    K->>E: 完成一次触摸事件上报

### regmap与iio ,iic/spi和adc/dac配合
#### regmap
掩码部分read_flag_mask 和 write_flag_mask这两个比较重要

```
#include <linux/spi/spi.h>
#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/init.h>
#include <linux/delay.h>
#include <linux/ide.h>
#include <linux/errno.h>
#include <linux/platform_device.h>
#include "icm20608reg.h"
#include <linux/gpio.h>
#include <linux/device.h>
#include <asm/uaccess.h>
#include <linux/cdev.h>
#include <linux/regmap.h>

#define ICM20608_CNT	1
#define ICM20608_NAME	"icm20608"

struct icm20608_dev {
	struct spi_device *spi;		/* spi设备 */
	dev_t devid;				/* 设备号 	 */
	struct cdev cdev;			/* cdev 	*/
	struct class *class;		/* 类 		*/
	struct device *device;		/* 设备 	 */
	struct device_node	*nd; 	/* 设备节点 */
	signed int gyro_x_adc;		/* 陀螺仪X轴原始值 	 */
	signed int gyro_y_adc;		/* 陀螺仪Y轴原始值		*/
	signed int gyro_z_adc;		/* 陀螺仪Z轴原始值 		*/
	signed int accel_x_adc;		/* 加速度计X轴原始值 	*/
	signed int accel_y_adc;		/* 加速度计Y轴原始值	*/
	signed int accel_z_adc;		/* 加速度计Z轴原始值 	*/
	signed int temp_adc;		/* 温度原始值 			*/
	struct regmap *regmap;				/* regmap */
	struct regmap_config regmap_config;	
};

/*
 * @description	: 读取icm20608指定寄存器值，读取一个寄存器
 * @param - dev:  icm20608设备
 * @param - reg:  要读取的寄存器
 * @return 	  :   读取到的寄存器值
 */
static unsigned char icm20608_read_onereg(struct icm20608_dev *dev, u8 reg)
{
	u8 ret;
	unsigned int data;

	ret = regmap_read(dev->regmap, reg, &data);
	return (u8)data;
}

/*
 * @description	: 向icm20608指定寄存器写入指定的值，写一个寄存器
 * @param - dev:  icm20608设备
 * @param - reg:  要写的寄存器
 * @param - data: 要写入的值
 * @return   :    无
 */	

static void icm20608_write_onereg(struct icm20608_dev *dev, u8 reg, u8 value)
{
	regmap_write(dev->regmap,  reg, value);
}

/*
 * @description	: 读取ICM20608的数据，读取原始数据，包括三轴陀螺仪、
 * 				: 三轴加速度计和内部温度。
 * @param - dev	: ICM20608设备
 * @return 		: 无。
 */
void icm20608_readdata(struct icm20608_dev *dev)
{
	u8 ret;
	unsigned char data[14];

	ret = regmap_bulk_read(dev->regmap, ICM20_ACCEL_XOUT_H, data, 14);

	dev->accel_x_adc = (signed short)((data[0] << 8) | data[1]); 
	dev->accel_y_adc = (signed short)((data[2] << 8) | data[3]); 
	dev->accel_z_adc = (signed short)((data[4] << 8) | data[5]); 
	dev->temp_adc    = (signed short)((data[6] << 8) | data[7]); 
	dev->gyro_x_adc  = (signed short)((data[8] << 8) | data[9]); 
	dev->gyro_y_adc  = (signed short)((data[10] << 8) | data[11]);
	dev->gyro_z_adc  = (signed short)((data[12] << 8) | data[13]);
}

/*
 * @description		: 打开设备
 * @param - inode 	: 传递给驱动的inode
 * @param - filp 	: 设备文件，file结构体有个叫做pr似有ate_data的成员变量
 * 					  一般在open的时候将private_data似有向设备结构体。
 * @return 			: 0 成功;其他 失败
 */
static int icm20608_open(struct inode *inode, struct file *filp)
{
	return 0;
}

/*
 * @description		: 从设备读取数据 
 * @param - filp 	: 要打开的设备文件(文件描述符)
 * @param - buf 	: 返回给用户空间的数据缓冲区
 * @param - cnt 	: 要读取的数据长度
 * @param - offt 	: 相对于文件首地址的偏移
 * @return 			: 读取的字节数，如果为负值，表示读取失败
 */
static ssize_t icm20608_read(struct file *filp, char __user *buf, size_t cnt, loff_t *off)
{
	signed int data[7];
	long err = 0;
	struct cdev *cdev = filp->f_path.dentry->d_inode->i_cdev;
	struct icm20608_dev *dev = container_of(cdev, struct icm20608_dev, cdev);
            
	icm20608_readdata(dev);
	data[0] = dev->gyro_x_adc;
	data[1] = dev->gyro_y_adc;
	data[2] = dev->gyro_z_adc;
	data[3] = dev->accel_x_adc;
	data[4] = dev->accel_y_adc;
	data[5] = dev->accel_z_adc;
	data[6] = dev->temp_adc;
	err = copy_to_user(buf, data, sizeof(data));
	return 0;
}

/*
 * @description		: 关闭/释放设备
 * @param - filp 	: 要关闭的设备文件(文件描述符)
 * @return 			: 0 成功;其他 失败
 */
static int icm20608_release(struct inode *inode, struct file *filp)
{
	return 0;
}

/* icm20608操作函数 */
static const struct file_operations icm20608_ops = {
	.owner = THIS_MODULE,
	.open = icm20608_open,
	.read = icm20608_read,
	.release = icm20608_release,
};

/*
 * ICM20608内部寄存器初始化函数 
 * @param - spi : 要操作的设备
 * @return 	: 无
 */
void icm20608_reginit(struct icm20608_dev *dev)
{
	u8 value = 0;
	
	icm20608_write_onereg(dev, ICM20_PWR_MGMT_1, 0x80);
	mdelay(50);
	icm20608_write_onereg(dev, ICM20_PWR_MGMT_1, 0x01);
	mdelay(50);

	value = icm20608_read_onereg(dev, ICM20_WHO_AM_I);
	printk("ICM20608 ID = %#X\r\n", value);	

	icm20608_write_onereg(dev, ICM20_SMPLRT_DIV, 0x00); 	/* 输出速率是内部采样率		*/
	icm20608_write_onereg(dev, ICM20_GYRO_CONFIG, 0x18); 	/* 陀螺仪±2000dps量程 		*/
	icm20608_write_onereg(dev, ICM20_ACCEL_CONFIG, 0x18); 	/* 加速度计±16G量程 		*/
	icm20608_write_onereg(dev, ICM20_CONFIG, 0x04); 		/* 陀螺仪低通滤波BW=20Hz 	*/
	icm20608_write_onereg(dev, ICM20_ACCEL_CONFIG2, 0x04); /* 加速度计低通滤波BW=21.2Hz 	*/
	icm20608_write_onereg(dev, ICM20_PWR_MGMT_2, 0x00); 	/* 打开加速度计和陀螺仪所有轴 	*/
	icm20608_write_onereg(dev, ICM20_LP_MODE_CFG, 0x00); 	/* 关闭低功耗 				*/
	icm20608_write_onereg(dev, ICM20_FIFO_EN, 0x00);		/* 关闭FIFO					*/
}

/*
  * @description     : spi驱动的probe函数，当驱动与
  *                    设备匹配以后此函数就会执行
  * @param - spi  	: spi设备
  * 
  */	
static int icm20608_probe(struct spi_device *spi)
{
	int ret;
	struct icm20608_dev *icm20608dev;
	
	/* 分配icm20608dev对象的空间 */
	icm20608dev = devm_kzalloc(&spi->dev, sizeof(*icm20608dev), GFP_KERNEL);
	if(!icm20608dev)
		return -ENOMEM;

	/* 初始化regmap_config设置 */
	icm20608dev->regmap_config.reg_bits = 8;			/* 寄存器长度8bit */
	icm20608dev->regmap_config.val_bits = 8;			/* 值长度8bit */
	icm20608dev->regmap_config.read_flag_mask = 0x80;  /* 读掩码设置为0X80，ICM20608使用SPI接口读的时候寄存器最高位应该为1 */

	/* 初始化IIC接口的regmap */
	icm20608dev->regmap = regmap_init_spi(spi, &icm20608dev->regmap_config);
	if (IS_ERR(icm20608dev->regmap)) {
		return  PTR_ERR(icm20608dev->regmap);
	}	
		
	/* 注册字符设备驱动 */
	/* 1、创建设备号 */
	ret = alloc_chrdev_region(&icm20608dev->devid, 0, ICM20608_CNT, ICM20608_NAME);
	if(ret < 0) {
		pr_err("%s Couldn't alloc_chrdev_region, ret=%d\r\n", ICM20608_NAME, ret);
        goto del_regmap;
	}

	/* 2、初始化cdev */
	icm20608dev->cdev.owner = THIS_MODULE;
	cdev_init(&icm20608dev->cdev, &icm20608_ops);
	
	/* 3、添加一个cdev */
	ret = cdev_add(&icm20608dev->cdev, icm20608dev->devid, ICM20608_CNT);
	if(ret < 0) {
		goto del_unregister;
	}
	
	/* 4、创建类 */
	icm20608dev->class = class_create(THIS_MODULE, ICM20608_NAME);
	if (IS_ERR(icm20608dev->class)) {
		goto del_cdev;
	}

	/* 5、创建设备 */
	icm20608dev->device = device_create(icm20608dev->class, NULL, icm20608dev->devid, NULL, ICM20608_NAME);
	if (IS_ERR(icm20608dev->device)) {
		goto destroy_class;
	}
	icm20608dev->spi = spi;
	
	/*初始化spi_device */
	spi->mode = SPI_MODE_0;	/*MODE0，CPOL=0，CPHA=0*/
	spi_setup(spi);
	
	/* 初始化ICM20608内部寄存器 */
	icm20608_reginit(icm20608dev);	
	/* 保存icm20608dev结构体 */
	spi_set_drvdata(spi, icm20608dev);

	return 0;
destroy_class:
	device_destroy(icm20608dev->class, icm20608dev->devid);
del_cdev:
	cdev_del(&icm20608dev->cdev);
del_unregister:
	unregister_chrdev_region(icm20608dev->devid, ICM20608_CNT);
del_regmap:
	regmap_exit(icm20608dev->regmap);
	return -EIO;
}

/*
 * @description     : spi驱动的remove函数，移除spi驱动的时候此函数会执行
 * @param - spi 	: spi设备
 * @return          : 0，成功;其他负值,失败
 */
static int icm20608_remove(struct spi_device *spi)
{
	struct icm20608_dev *icm20608dev = spi_get_drvdata(spi);

	/* 注销字符设备驱动 */
	/* 1、删除cdev */
	cdev_del(&icm20608dev->cdev);
	/* 2、注销设备号 */
	unregister_chrdev_region(icm20608dev->devid, ICM20608_CNT); 
	/* 3、注销设备 */
	device_destroy(icm20608dev->class, icm20608dev->devid);
	/* 4、注销类 */
	class_destroy(icm20608dev->class); 
	/* 5、删除regmap */
	regmap_exit(icm20608dev->regmap);

	return 0;
}

/* 传统匹配方式ID列表 */
static const struct spi_device_id icm20608_id[] = {
	{"alientek,icm20608", 0},
	{}
};

/* 设备树匹配列表 */
static const struct of_device_id icm20608_of_match[] = {
	{ .compatible = "alientek,icm20608" },
	{ /* Sentinel */ }
};

/* SPI驱动结构体 */
static struct spi_driver icm20608_driver = {
	.probe = icm20608_probe,
	.remove = icm20608_remove,
	.driver = {
			.owner = THIS_MODULE,
		   	.name = "icm20608",
		   	.of_match_table = icm20608_of_match,
		   },
	.id_table = icm20608_id,
};

/*
 * @description	: 驱动入口函数
 * @param 		: 无
 * @return 		: 无
 */
static int __init icm20608_init(void)
{
	return spi_register_driver(&icm20608_driver);
}

/*
 * @description	: 驱动出口函数
 * @param 		: 无
 * @return 		: 无
 */
static void __exit icm20608_exit(void)
{
	spi_unregister_driver(&icm20608_driver);
}

module_init(icm20608_init);
module_exit(icm20608_exit);
MODULE_LICENSE("GPL");
MODULE_AUTHOR("ALIENTEK");
MODULE_INFO(intree, "Y");

```


#### iio
// IIO_VAL_INT: 整数格式
// 用户写入: 123 → val=123, val2=0

// IIO_VAL_INT_PLUS_MICRO: 整数+微秒格式  
// 用户写入: 1.234567 → val=1, val2=234567

// IIO_VAL_INT_PLUS_NANO: 整数+纳秒格式
// 用户写入: 1.234567890 → val=1, val2=234567890

功能	不使用IIO	使用IIO
用户接口	自己实现ioctl/sysfs	自动生成标准接口
数据格式	自定义协议	标准化格式转换
缓冲区	自己管理内存和同步	框架提供完整方案
事件处理	自建通知机制	标准事件系统
设备管理	手动注册设备节点	自动设备发现
调试支持	自己实现调试接口	内置丰富调试信息


iio命名方式
```
.type = IIO_ANGL_VEL, 
.modified = 1, 
.channel2 = IIO_MOD_X, 
.info_mask_shared_by_type = BIT(IIO_CHAN_INFO_SCALE),
.info_mask_separate = BIT(IIO_CHAN_INFO_RAW) | 
BIT(IIO_CHAN_INFO_CALIBBIAS), 
.scan_index = INV_ICM20608_SCAN_GYRO_X, 
.scan_type = { 
    .sign = 's',
 .realbits = 16, 
 .storagebits = 16, 
 .shift = 0, 
 .endianness = IIO_BE, 
 },
```
这回生成in_accel_x_raw 和 in_accel_x_calibias


常用宏
```
#define ICM20608_CHAN(_type, _channel2, _index)                    \
	{                                                             \
		.type = _type,                                        \
		.modified = 1,                                        \
		.channel2 = _channel2,                                \
		.info_mask_shared_by_type = BIT(IIO_CHAN_INFO_SCALE), \
		.info_mask_separate = BIT(IIO_CHAN_INFO_RAW) |	      \
				      BIT(IIO_CHAN_INFO_CALIBBIAS),   \
		.scan_index = _index,                                 \
		.scan_type = {                                        \
				.sign = 's',                          \
				.realbits = 16,                       \
				.storagebits = 16,                    \
				.shift = 0,                           \
				.endianness = IIO_BE,                 \
			     },                                       \
	}
```

温度读取不了的问题
在IIO框架中，对于传感器数据的读取通常有两种模式：
单次读取（Single-shot/one-shot）：每次读取都会触发一次新的采样
连续读取（Continuous）：传感器持续采样
当使用 fscanf 时，它会：
打开文件
读取一次数据
立即关闭文件
而 cat 命令会：
打开文件
尝试读取所有数据直到EOF
由于传感器数据是实时的，可能会导致设备忙的情况


### 块设备

```
- struct gendisk：表示一个磁盘设备
  - 包含主设备号、次设备号
  - 磁盘名称
  - 磁盘操作函数集(struct block_device_operations)
  - 请求队列(request_queue)

- struct block_device_operations：块设备操作函数集
  - open：打开设备
  - release：释放设备
  - ioctl：设备控制
  - getgeo：获取设备几何信息(对于实际硬盘)

- struct request_queue：请求队列
  - 管理IO请求
  - 可以选择使用request方式或make_request方式

a) 申请主设备号
   register_blkdev(major, name)

b) 创建并初始化请求队列
   方式1: blk_init_queue() - 使用request方式
   方式2: blk_mq_init_queue() - 多队列方式
   
c) 创建gendisk结构体
   alloc_disk(MINORS) - MINORS为次设备号数量

d) 设置gendisk相关属性
   - disk->major = major
   - disk->first_minor = 0
   - disk->fops = &xxx_fops
   - disk->queue = xxx_queue
   - disk->private_data = xxx

e) 添加磁盘设备
   add_disk(disk)


机械硬盘- 使用请求队列处理IO请求
- 优点：
  - 可以合并相邻请求
  - 可以重新排序请求
  - IO调度更优化
- 实现：通过request_fn_proc处理函数
- 适用：实际的物理设备，如硬盘


ram - 直接处理单个请求
- 优点：
  - 实现简单
  - 适合内存设备
- 实现：通过make_request_fn函数
- 适用：RAM盘等内存设备

- bio结构体：描述IO操作的基本单位
  - bio_data_dir()：获取传输方向
  - bio_segments：bio包含的段数
  - bio_sector：起始扇区

- blk_end_request：完成请求处理
- blk_end_io：结束bio请求
```


# 移植QT

移植tslib，交叉编译，用于让qt可以使用触摸屏
https://github.com/libts/tslib.git
sudo apt-get install autoconf automake libtool m4 pkg-config
git checkout tags/1.22  开发板ubuntu-base只有1.22版本
./autogen.sh
./configure --host=arm-linux-gnueabihf ac_cv_func_malloc_0_nonnull=yes --cache-file=arm-linux.cache -prefix=/root/arm-tslib

make
make install


wget https://download.qt.io/archive/qt/5.12/5.12.9/single/qt-everywhere-src-5.15.17.tar.xz
tar xf qt-everywhere-src-5.15.17.tar.xz
cd qt-everywhere-src-5.15.17/
ls
vi qtbase/mkspecs/linux-arm-gnueabi-g++/qmake.conf

```
#
# qmake configuration for building with arm-linux-gnueabi-g++
#
MAKEFILE_GENERATOR = UNIX
CONFIG += incremental
QMAKE_INCREMENTAL_STYLE = sublib
QT_QPA_DEFAULT_PLATFORM = linuxfb
QMAKE_CFLAGS += -O2 -march=armv7-a -mtune=cortex-a7 -mfpu=neon -mfloat-abi=hard
QMAKE_CXXFLAGS += -O2 -march=armv7-a -mtune=cortex-a7 -mfpu=neon -mfloat-abi=hard
include(../common/linux.conf)
include(../common/gcc-base-unix.conf)
include(../common/g++-unix.conf)
# modifications to g++.conf
QMAKE_CC = arm-linux-gnueabihf-gcc
QMAKE_CXX = arm-linux-gnueabihf-g++
QMAKE_LINK = arm-linux-gnueabihf-g++
QMAKE_LINK_SHLIB = arm-linux-gnueabihf-g++
# modifications to linux.conf
QMAKE_AR = arm-linux-gnueabihf-ar cqs
QMAKE_OBJCOPY = arm-linux-gnueabihf-objcopy
QMAKE_NM = arm-linux-gnueabihf-nm -P
QMAKE_STRIP = arm-linux-gnueabihf-strip
load(qt_config)
```


autoconfigure.sh

```
./configure -prefix /root/arm-qt \
-opensource \
-confirm-license \
-release \
-strip \
-shared \
-xplatform linux-arm-gnueabi-g++ \
-optimized-qmake \
--rpath=no \
-pch \
-skip qt3d \
-skip qtactiveqt \
-skip qtandroidextras \
-skip qtcanvas3d \
-skip qtconnectivity \
-skip qtdatavis3d \
-skip qtdoc \
-skip qtgamepad \
-skip qtlocation \
-skip qtmacextras \
-skip qtnetworkauth \
-skip qtpurchasing \
-skip qtremoteobjects \
-skip qtscript \
-skip qtscxml \
-skip qtsensors \
-skip qtspeech \
-skip qtsvg \
-skip qttools \
-skip qttranslations \
-skip qtwayland \
-skip qtwebengine \
-skip qtwebview \
-skip qtwinextras \
-skip qtx11extras \
-skip qtxmlpatterns \
-make libs \
-make examples \
-nomake tools -nomake tests \
-gui \
-widgets \
-dbus-runtime \
--glib=no \
--iconv=no \
--pcre=qt \
--zlib=qt \
-no-openssl \
--freetype=qt \
--harfbuzz=qt \
-no-opengl \
-linuxfb \
--xcb=no \
-tslib \
--libpng=qt \
--libjpeg=qt \
--sqlite=qt \
-plugin-sql-sqlite \
-I/root/arm-tslib/include \
-L/root/arm-tslib/lib \
-recheck-all
```
